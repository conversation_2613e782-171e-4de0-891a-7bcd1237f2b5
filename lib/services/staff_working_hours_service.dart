import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/staff_working_hours_settings.dart';
import '../models/working_hours_settings.dart';
import '../utils/romanian_holidays.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Service for managing staff working hours settings
class StaffWorkingHoursService {
  /// Get working hours settings for a specific staff member
  static Future<ApiResponse<StaffWorkingHoursSettings>> getStaffWorkingHours(String staffId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffWorkingHoursSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Getting working hours for staff: $staffId in salon: $salonId'); // todo this staff id is userId
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/staff/$staffId/working-hours',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final settings = StaffWorkingHoursSettings.fromJson(response.data!);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Staff working hours retrieved successfully');
          debugPrint('   Staff ID: ${settings.staffId}');
          debugPrint('   Working days: ${settings.weeklySchedule.entries.where((e) => e.value.isWorkingDay).map((e) => e.key).join(', ')}');
        }

        return ApiResponse.success(settings);
      }

      return ApiResponse<StaffWorkingHoursSettings>.error(response.error ?? 'Failed to get staff working hours');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting staff working hours: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to get staff working hours: $e');
    }
  }

  /// Update working hours settings for a specific staff member
  /// Only users with CHIEF_GROOMER role or the staff member themselves can update these settings
  static Future<ApiResponse<StaffWorkingHoursSettings>> updateStaffWorkingHours(
    String staffId,
    UpdateStaffWorkingHoursRequest request,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffWorkingHoursSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Updating working hours for staff: $staffId in salon: $salonId');
        debugPrint('   Weekly schedule: ${request.weeklySchedule.keys.join(', ')}');
        debugPrint('   Holidays count: ${request.holidays.length}');
        debugPrint('   Custom closures count: ${request.customClosures.length}');
      }

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/salons/$salonId/staff/$staffId/working-hours',
        body: request.toJson(),
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final updatedSettings = StaffWorkingHoursSettings.fromJson(response.data!);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Staff working hours updated successfully');
        }

        return ApiResponse.success(updatedSettings);
      }

      if (ApiConfig.enableLogging) {
        debugPrint('❌ Staff working hours update failed');
        debugPrint('   Response success: ${response.success}');
        debugPrint('   Response error: ${response.error}');
        debugPrint('   Response status code: ${response.statusCode}');
        debugPrint('   Response data: ${response.data}');
      }

      return ApiResponse<StaffWorkingHoursSettings>.error(response.error ?? 'Failed to update staff working hours');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating staff working hours: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to update staff working hours: $e');
    }
  }

  /// Reset working hours settings to default values for a staff member
  /// Only users with CHIEF_GROOMER role can reset settings
  static Future<ApiResponse<StaffWorkingHoursSettings>> resetStaffWorkingHours(String staffId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffWorkingHoursSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Resetting working hours for staff: $staffId in salon: $salonId');
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/salons/$salonId/staff/$staffId/working-hours/reset',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final resetSettings = StaffWorkingHoursSettings.fromJson(response.data!);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Staff working hours reset successfully');
        }

        return ApiResponse.success(resetSettings);
      }

      return ApiResponse<StaffWorkingHoursSettings>.error(response.error ?? 'Failed to reset staff working hours');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error resetting staff working hours: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to reset staff working hours: $e');
    }
  }

  /// Validate staff working hours settings request
  static String? validateStaffWorkingHoursRequest(UpdateStaffWorkingHoursRequest request) {
    // Validate each day's schedule
    for (final entry in request.weeklySchedule.entries) {
      final dayName = entry.key;
      final schedule = entry.value;

      // Skip validation for days off
      if (!schedule.isWorkingDay) continue;

      // Validate time formats
      if (schedule.startTime != null && !RomanianHolidays.isValidTimeFormat(schedule.startTime!)) {
        return 'Ora de început pentru $dayName nu este validă';
      }
      if (schedule.endTime != null && !RomanianHolidays.isValidTimeFormat(schedule.endTime!)) {
        return 'Ora de sfârșit pentru $dayName nu este validă';
      }

      // Validate time ranges
      if (schedule.startTime != null && schedule.endTime != null) {
        if (schedule.startTime!.compareTo(schedule.endTime!) >= 0) {
          return 'Ora de început trebuie să fie înainte de ora de sfârșit pentru $dayName';
        }
      }

      // Validate break times
      if (schedule.breakStart != null && schedule.breakEnd != null) {
        if (!RomanianHolidays.isValidTimeFormat(schedule.breakStart!)) {
          return 'Ora de început a pauzei pentru $dayName nu este validă';
        }
        if (!RomanianHolidays.isValidTimeFormat(schedule.breakEnd!)) {
          return 'Ora de sfârșit a pauzei pentru $dayName nu este validă';
        }
        if (schedule.breakStart!.compareTo(schedule.breakEnd!) >= 0) {
          return 'Ora de început a pauzei trebuie să fie înainte de ora de sfârșit pentru $dayName';
        }

        // Validate break is within working hours
        if (schedule.startTime != null && schedule.breakStart!.compareTo(schedule.startTime!) < 0) {
          return 'Pauza trebuie să fie în timpul programului de lucru pentru $dayName';
        }
        if (schedule.endTime != null && schedule.breakEnd!.compareTo(schedule.endTime!) > 0) {
          return 'Pauza trebuie să fie în timpul programului de lucru pentru $dayName';
        }
      }
    }

    // Validate holidays
    for (final holiday in request.holidays) {
      if (holiday.name.isEmpty) {
        return 'Numele sărbătorii nu poate fi gol';
      }
    }

    // Validate custom closures
    for (final closure in request.customClosures) {
      if (closure.reason.isEmpty) {
        return 'Motivul închiderii nu poate fi gol';
      }
    }

    return null; // No validation errors
  }

  /// Update a specific day's schedule for a staff member
  static Future<ApiResponse<StaffWorkingHoursSettings>> updateStaffDaySchedule({
    required String staffId,
    required String dayOfWeek,
    required DaySchedule schedule,
    StaffWorkingHoursSettings? currentSettings,
  }) async {
    try {
      // Use provided current settings or fetch from backend
      StaffWorkingHoursSettings settings;
      if (currentSettings != null) {
        settings = currentSettings;
        if (ApiConfig.enableLogging) {
          debugPrint('⏰ Using provided current settings for staff day schedule update');
        }
      } else {
        // Fallback to fetching current settings
        final currentResponse = await getStaffWorkingHours(staffId);
        if (!currentResponse.success || currentResponse.data == null) {
          return ApiResponse<StaffWorkingHoursSettings>.error('Failed to get current staff working hours');
        }
        settings = currentResponse.data!;
        if (ApiConfig.enableLogging) {
          debugPrint('⏰ Fetched current settings for staff day schedule update');
        }
      }

      final updatedSchedule = Map<String, DaySchedule>.from(settings.weeklySchedule);
      updatedSchedule[dayOfWeek.toLowerCase()] = schedule;

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Updating day schedule for $dayOfWeek: ${schedule.toJson()}');
        debugPrint('⏰ Full updated schedule: ${updatedSchedule.keys.join(', ')}');
      }

      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: updatedSchedule,
        holidays: settings.holidays,
        customClosures: settings.customClosures,
      );

      return await updateStaffWorkingHours(staffId, request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating staff day schedule: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to update staff day schedule: $e');
    }
  }

  /// Add or update a holiday for a staff member
  static Future<ApiResponse<StaffWorkingHoursSettings>> updateStaffHoliday({
    required String staffId,
    required Holiday holiday,
  }) async {
    try {
      // Get current settings
      final currentResponse = await getStaffWorkingHours(staffId);
      if (!currentResponse.success || currentResponse.data == null) {
        return ApiResponse<StaffWorkingHoursSettings>.error('Failed to get current staff working hours');
      }

      final currentSettings = currentResponse.data!;
      final updatedHolidays = List<Holiday>.from(currentSettings.holidays);

      // Remove existing holiday for the same date if it exists
      updatedHolidays.removeWhere((h) => 
        h.date.year == holiday.date.year &&
        h.date.month == holiday.date.month &&
        h.date.day == holiday.date.day
      );

      // Add the new/updated holiday
      updatedHolidays.add(holiday);

      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: currentSettings.weeklySchedule,
        holidays: updatedHolidays,
        customClosures: currentSettings.customClosures,
      );

      return await updateStaffWorkingHours(staffId, request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating staff holiday: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to update staff holiday: $e');
    }
  }

  /// Add a custom closure for a staff member
  static Future<ApiResponse<StaffWorkingHoursSettings>> addStaffCustomClosure(
    String staffId,
    CustomClosure closure, {
    StaffWorkingHoursSettings? currentSettings,
  }) async {
    try {
      // Use provided current settings or fetch from backend
      StaffWorkingHoursSettings settings;
      if (currentSettings != null) {
        settings = currentSettings;
        if (ApiConfig.enableLogging) {
          debugPrint('⏰ Using provided current settings for staff custom closure add');
        }
      } else {
        // Fallback to fetching current settings
        final currentResponse = await getStaffWorkingHours(staffId);
        if (!currentResponse.success || currentResponse.data == null) {
          return ApiResponse<StaffWorkingHoursSettings>.error('Failed to get current staff working hours');
        }
        settings = currentResponse.data!;
        if (ApiConfig.enableLogging) {
          debugPrint('⏰ Fetched current settings for staff custom closure add');
        }
      }

      final updatedClosures = List<CustomClosure>.from(settings.customClosures);

      // Remove existing closure for the same date if it exists
      updatedClosures.removeWhere((c) =>
        c.date.year == closure.date.year &&
        c.date.month == closure.date.month &&
        c.date.day == closure.date.day
      );

      // Add the new closure
      updatedClosures.add(closure);

      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: settings.weeklySchedule,
        holidays: settings.holidays,
        customClosures: updatedClosures,
      );

      return await updateStaffWorkingHours(staffId, request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error adding staff custom closure: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to add staff custom closure: $e');
    }
  }

  /// Remove a custom closure for a staff member
  static Future<ApiResponse<StaffWorkingHoursSettings>> removeStaffCustomClosure(
    String staffId,
    DateTime date, {
    StaffWorkingHoursSettings? currentSettings,
  }) async {
    try {
      // Use provided current settings or fetch from backend
      StaffWorkingHoursSettings settings;
      if (currentSettings != null) {
        settings = currentSettings;
        if (ApiConfig.enableLogging) {
          debugPrint('⏰ Using provided current settings for staff custom closure removal');
        }
      } else {
        // Fallback to fetching current settings
        final currentResponse = await getStaffWorkingHours(staffId);
        if (!currentResponse.success || currentResponse.data == null) {
          return ApiResponse<StaffWorkingHoursSettings>.error('Failed to get current staff working hours');
        }
        settings = currentResponse.data!;
        if (ApiConfig.enableLogging) {
          debugPrint('⏰ Fetched current settings for staff custom closure removal');
        }
      }

      final updatedClosures = settings.customClosures.where((c) =>
        !(c.date.year == date.year &&
          c.date.month == date.month &&
          c.date.day == date.day)
      ).toList();

      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: settings.weeklySchedule,
        holidays: settings.holidays,
        customClosures: updatedClosures,
      );

      return await updateStaffWorkingHours(staffId, request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error removing staff custom closure: $e');
      }
      return ApiResponse<StaffWorkingHoursSettings>.error('Failed to remove staff custom closure: $e');
    }
  }

  /// Check if staff member is available at a specific date and time
  static Future<bool> isStaffAvailable(String staffId, DateTime dateTime) async {
    try {
      final response = await getStaffWorkingHours(staffId);
      if (!response.success || response.data == null) {
        return false; // Default to unavailable if can't get settings
      }

      final settings = response.data!;
      return settings.isAvailableAtTime(dateTime);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking if staff is available: $e');
      }
      return false;
    }
  }

  /// Get working hours for multiple staff members in a single API call (OPTIMIZED)
  static Future<ApiResponse<Map<String, StaffWorkingHoursSettings>>> getBatchStaffWorkingHours(List<String> staffIds) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, StaffWorkingHoursSettings>>.error('No salon ID found');
      }

      if (staffIds.isEmpty) {
        return ApiResponse.success(<String, StaffWorkingHoursSettings>{});
      }

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Getting working hours for ${staffIds.length} staff members in batch: ${staffIds.join(', ')}');
      }

      final queryParams = {
        'staffIds': staffIds.join(','),
      };

      debugPrint('🌐 Making batch API request to: /api/salons/$salonId/staff/working-hours/batch');
      debugPrint('📋 Query params: $queryParams');

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/staff/working-hours/batch',
        queryParams: queryParams,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      debugPrint('📥 Batch API raw response:');
      debugPrint('   Success: ${response.success}');
      debugPrint('   Data is null: ${response.data == null}');
      debugPrint('   Error: ${response.error}');
      if (response.data != null) {
        debugPrint('   Data keys: ${response.data!.keys.toList()}');
        debugPrint('   Data type: ${response.data.runtimeType}');
      }

      if (response.success && response.data != null) {
        final responseData = response.data!;
        final result = <String, StaffWorkingHoursSettings>{};

        debugPrint('🔄 Processing batch response data');
        debugPrint('   Response keys: ${responseData.keys.toList()}');

        // Check if response has the expected structure from backend
        if (responseData.containsKey('staffWorkingHours')) {
          final staffWorkingHours = responseData['staffWorkingHours'];
          debugPrint('📋 Found staffWorkingHours in response');
          debugPrint('   Type: ${staffWorkingHours.runtimeType}');

          if (staffWorkingHours is Map<String, dynamic>) {
            debugPrint('🔄 Processing staff working hours map with ${staffWorkingHours.length} entries');

            for (final entry in staffWorkingHours.entries) {
              final staffId = entry.key;
              final settingsData = entry.value;

              debugPrint('📋 Processing staff: $staffId');
              debugPrint('   Settings data type: ${settingsData.runtimeType}');

              try {
                if (settingsData is Map<String, dynamic>) {
                  result[staffId] = StaffWorkingHoursSettings.fromJson(settingsData);
                  debugPrint('✅ Successfully parsed settings for staff: $staffId');
                } else {
                  debugPrint('❌ Settings data is not Map<String, dynamic> for staff: $staffId');
                  debugPrint('   Actual type: ${settingsData.runtimeType}');
                  debugPrint('   Data: $settingsData');
                }
              } catch (e) {
                debugPrint('❌ Failed to parse settings for staff $staffId: $e');
              }
            }
          } else {
            debugPrint('❌ staffWorkingHours is not a Map<String, dynamic>');
            debugPrint('   Actual type: ${staffWorkingHours.runtimeType}');
          }
        } else {
          debugPrint('❌ Response does not contain staffWorkingHours key');
          debugPrint('   Available keys: ${responseData.keys.toList()}');

          // Fallback: try to process as direct staff mapping (old format)
          debugPrint('🔄 Trying fallback processing as direct staff mapping');
          for (final entry in responseData.entries) {
            final staffId = entry.key;
            final settingsData = entry.value;

            // Skip non-staff data
            if (staffId == 'salonId' || staffId == 'requestedStaffIds' || staffId == 'foundStaffIds') {
              continue;
            }

            debugPrint('📋 Processing staff (fallback): $staffId');
            debugPrint('   Settings data type: ${settingsData.runtimeType}');

            try {
              if (settingsData is Map<String, dynamic>) {
                result[staffId] = StaffWorkingHoursSettings.fromJson(settingsData);
                debugPrint('✅ Successfully parsed settings for staff (fallback): $staffId');
              }
            } catch (e) {
              debugPrint('❌ Failed to parse settings for staff $staffId (fallback): $e');
            }
          }
        }

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Batch staff working hours retrieved successfully for ${result.length} staff members');
          for (final entry in result.entries) {
            final staffId = entry.key;
            final settings = entry.value;
            debugPrint('   Staff $staffId: ${settings.weeklySchedule.entries.where((e) => e.value.isWorkingDay).map((e) => e.key).join(', ')}');
          }
        }

        return ApiResponse.success(result);
      }

      return ApiResponse<Map<String, StaffWorkingHoursSettings>>.error(response.error ?? 'Failed to get batch staff working hours');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting batch staff working hours: $e');
      }
      return ApiResponse<Map<String, StaffWorkingHoursSettings>>.error('Failed to get batch staff working hours: $e');
    }
  }

  /// Get all staff members' availability for a specific date
  static Future<Map<String, bool>> getStaffAvailabilityForDate(List<String> staffIds, DateTime date) async {
    final availability = <String, bool>{};

    for (final staffId in staffIds) {
      try {
        final response = await getStaffWorkingHours(staffId);
        if (response.success && response.data != null) {
          availability[staffId] = response.data!.isAvailableOnDate(date);
        } else {
          availability[staffId] = false;
        }
      } catch (e) {
        availability[staffId] = false;
      }
    }

    return availability;
  }

  /// Get all staff members' availability for a specific date using batch API (OPTIMIZED)
  static Future<Map<String, bool>> getBatchStaffAvailabilityForDate(List<String> staffIds, DateTime date) async {
    try {
      final response = await getBatchStaffWorkingHours(staffIds);
      if (!response.success || response.data == null) {
        // Fallback to individual requests
        return await getStaffAvailabilityForDate(staffIds, date);
      }

      final availability = <String, bool>{};
      final batchData = response.data!;

      for (final staffId in staffIds) {
        if (batchData.containsKey(staffId)) {
          availability[staffId] = batchData[staffId]!.isAvailableOnDate(date);
        } else {
          availability[staffId] = false;
        }
      }

      return availability;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting batch staff availability, falling back to individual requests: $e');
      }
      // Fallback to individual requests
      return await getStaffAvailabilityForDate(staffIds, date);
    }
  }
}
