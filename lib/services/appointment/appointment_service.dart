import '../../config/api_config.dart';
import '../../models/api_response.dart';
import '../../models/appointment.dart';
import '../api_service.dart';
import '../auth/auth_service.dart';
import '../error_message_service.dart';
import 'package:flutter/foundation.dart';

class AppointmentService {
  // Get all appointments for current salon
  static Future<ApiResponse<List<Appointment>>> getAppointments({
    DateTime? date,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? clientId,
    String? groomerId,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Appointment>>.error('No salon selected');
      }

      final queryParams = <String, String>{};

      if (date != null) {
        queryParams['date'] = date.toIso8601String().split('T')[0];
      }
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
      }
      if (status != null) queryParams['status'] = status;
      if (clientId != null) queryParams['clientId'] = clientId;
      if (groomerId != null) queryParams['groomerId'] = groomerId;

      if (ApiConfig.enableLogging) {
        debugPrint('🔍 Getting appointments for salon $salonId');
        debugPrint('🔍 Query params: $queryParams');
      }

      final response = await ApiService.get<List<Appointment>>(
        '/api/salons/$salonId/appointments',
        queryParams: queryParams.isNotEmpty ? queryParams : null,
        fromJson: (data) => (data as List).map((item) => Appointment.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Appointment>>.error('Failed to get appointments: $e');
    }
  }

  // Get appointment by ID
  static Future<ApiResponse<Appointment>> getAppointment(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final response = await ApiService.get<Appointment>(
        '/api/salons/$salonId/appointments/$id',
        fromJson: (data) => Appointment.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to get appointment: $e');
    }
  }

  // Create new appointment (backend automatically handles all notifications)
  static Future<ApiResponse<Appointment>> createAppointment(Appointment appointment) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final response = await ApiService.post<Appointment>(
        '/api/salons/$salonId/appointments',
        body: appointment.toJson(),
        fromJson: (data) => Appointment.fromJson(data),
      );

      // Backend automatically:
      // - Sends SMS confirmation to client (if enabled in settings)
      // - Sends push notification to assigned groomer
      // - Sends push notification to all salon staff
      // - Schedules day-before reminder (if enabled in settings)

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to create appointment: $e');
    }
  }

  // Create new appointment from form data using ScheduleAppointmentRequest DTO
  static Future<ApiResponse<Appointment>> createAppointmentFromFormData(Map<String, dynamic> scheduleRequest) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final response = await ApiService.post<Appointment>(
        '/api/salons/$salonId/appointments',
        body: scheduleRequest,
        fromJson: (data) => Appointment.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to create appointment: $e');
    }
  }

  // Update existing appointment
  static Future<ApiResponse<Appointment>> updateAppointment(String id, Appointment appointment) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final response = await ApiService.put<Appointment>(
        '/api/salons/$salonId/appointments/$id',
        body: appointment.toJson(),
        fromJson: (data) => Appointment.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to update appointment: $e');
    }
  }

  // Cancel appointment (backend automatically handles all notifications)
  static Future<ApiResponse<Appointment>> cancelAppointment(String id, {String? reason}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final body = <String, dynamic>{
        if (reason != null) 'reason': reason,
      };

      final response = await ApiService.put<Appointment>(
        '/api/salons/$salonId/appointments/$id/cancel',
        body: body,
        fromJson: (data) => Appointment.fromJson(data),
      );

      // Backend automatically:
      // - Sends SMS cancellation to client (if enabled in settings)
      // - Sends push notification to assigned groomer
      // - Sends push notification to all salon staff
      // - Cancels any scheduled reminders

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to cancel appointment: $e');
    }
  }

  // Delete appointment
  static Future<ApiResponse<void>> deleteAppointment(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      final response = await ApiService.delete<void>('/api/salons/$salonId/appointments/$id');
      return response;
    } catch (e) {
      return ApiResponse<void>.error('Failed to delete appointment: $e');
    }
  }

  // Get today's appointments
  static Future<ApiResponse<List<Appointment>>> getTodayAppointments() async {
    final today = DateTime.now();
    return getAppointments(date: today);
  }

  // Get upcoming appointments (next 7 days)
  static Future<ApiResponse<List<Appointment>>> getUpcomingAppointments({int days = 7}) async {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day);
    final endDate = startDate.add(Duration(days: days));

    return getAppointments(startDate: startDate, endDate: endDate);
  }

  // Get appointments for a specific week
  static Future<ApiResponse<List<Appointment>>> getWeekAppointments(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 7));
    return getAppointments(startDate: weekStart, endDate: weekEnd);
  }

  // Get appointments for a specific month
  static Future<ApiResponse<List<Appointment>>> getMonthAppointments(DateTime month) async {
    final monthStart = DateTime(month.year, month.month, 1);
    final monthEnd = DateTime(month.year, month.month + 1, 0);
    return getAppointments(startDate: monthStart, endDate: monthEnd);
  }

  // Get client's appointments
  static Future<ApiResponse<List<Appointment>>> getClientAppointments(String clientId) async {
    return getAppointments(clientId: clientId);
  }

  // Get staff member's appointments using dedicated endpoint
  static Future<ApiResponse<List<Appointment>>> getStaffAppointments(String staffId, {
    DateTime? date,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Appointment>>.error('No salon selected');
      }

      final queryParams = <String, String>{};

      if (date != null) {
        queryParams['date'] = date.toIso8601String().split('T')[0];
      }
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
      }
      if (status != null) queryParams['status'] = status;

      final response = await ApiService.get<List<Appointment>>(
        '/api/salons/$salonId/appointments/staff/$staffId',
        queryParams: queryParams.isNotEmpty ? queryParams : null,
        fromJson: (data) => (data as List).map((item) => Appointment.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Appointment>>.error('Failed to get staff appointments: $e');
    }
  }

  // Check appointment availability
  static Future<ApiResponse<bool>> checkAvailability({
    required DateTime startTime,
    required DateTime endTime,
    String? staffId,
    String? excludeAppointmentId,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<bool>.error('No salon selected');
      }

      final queryParams = <String, String>{
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        if (staffId != null) 'staffId': staffId,
        if (excludeAppointmentId != null) 'excludeAppointmentId': excludeAppointmentId,
      };

      final response = await ApiService.get<bool>(
        '/api/salons/$salonId/appointments/availability',
        queryParams: queryParams,
        fromJson: (data) => data as bool,
      );

      return response;
    } catch (e) {
      return ApiResponse<bool>.error('Failed to check availability: $e');
    }
  }

  // Get appointment conflicts
  static Future<ApiResponse<List<Appointment>>> getAppointmentConflicts({
    required DateTime startTime,
    required DateTime endTime,
    String? staffId,
    String? excludeAppointmentId,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Appointment>>.error('No salon selected');
      }

      final queryParams = <String, String>{
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        if (staffId != null) 'staffId': staffId,
        if (excludeAppointmentId != null) 'excludeAppointmentId': excludeAppointmentId,
      };

      final response = await ApiService.get<List<Appointment>>(
        '/api/salons/$salonId/appointments/conflicts',
        queryParams: queryParams,
        fromJson: (data) => (data as List).map((item) => Appointment.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Appointment>>.error('Failed to get appointment conflicts: $e');
    }
  }

  // Mark appointment as completed (backend automatically handles all notifications)
  static Future<ApiResponse<Appointment>> completeAppointment(String id, {String? notes}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final body = <String, dynamic>{
        if (notes != null) 'notes': notes,
      };

      final response = await ApiService.put<Appointment>(
        '/api/salons/$salonId/appointments/$id/complete',
        body: body,
        fromJson: (data) => Appointment.fromJson(data),
      );

      // Backend automatically:
      // - Sends SMS completion message to client
      // - Sends push notification to staff
      // - Schedules follow-up message (2 hours later, if enabled in settings)

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to complete appointment: $e');
    }
  }

  // Mark appointment as no-show
  static Future<ApiResponse<Appointment>> markNoShow(String id, {String? notes}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final body = <String, dynamic>{
        'status': 'no_show',
        if (notes != null) 'noShowNotes': notes,
      };

      final response = await ApiService.put<Appointment>(
        '/api/salons/$salonId/appointments/$id',
        body: body,
        fromJson: (data) => Appointment.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to mark appointment as no-show: $e');
    }
  }

  // Reschedule appointment (backend automatically handles all notifications)
  static Future<ApiResponse<Appointment>> rescheduleAppointment(
    String id, {
    required DateTime newStartTime,
    required DateTime newEndTime,
    String? reason,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Appointment>.error('No salon selected');
      }

      final body = <String, dynamic>{
        'startTime': newStartTime.toIso8601String(),
        'endTime': newEndTime.toIso8601String(),
        if (reason != null) 'reason': reason,
      };

      final response = await ApiService.put<Appointment>(
        '/api/salons/$salonId/appointments/$id/reschedule',
        body: body,
        fromJson: (data) => Appointment.fromJson(data),
      );

      // Backend automatically:
      // - Sends SMS reschedule notification to client
      // - Sends push notification to staff
      // - Cancels old reminder and schedules new one

      return response;
    } catch (e) {
      return ApiResponse<Appointment>.error('Failed to reschedule appointment: $e');
    }
  }

  // Get appointment statistics
  static Future<ApiResponse<Map<String, dynamic>>> getAppointmentStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/appointments/stats',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Validate appointment data
  static String? validateAppointment(Appointment appointment) {
    if (appointment.clientId.trim().isEmpty) {
      return 'Clientul este obligatoriu';
    }

    if (appointment.service.trim().isEmpty) {
      return 'Serviciul este obligatoriu';
    }

    if (appointment.startTime.isAfter(appointment.endTime)) {
      return 'Ora de început trebuie să fie înainte de ora de sfârșit';
    }

    if (appointment.startTime.isBefore(DateTime.now().subtract(const Duration(minutes: 5)))) {
      return 'Nu se pot programa întâlniri în trecut';
    }

    return null; // No validation errors
  }

  // Bulk operations
  static Future<ApiResponse<List<Appointment>>> createMultipleAppointments(
    List<Appointment> appointments,
  ) async {
    final response = await ApiService.post<List<Appointment>>(
      '/api/appointments/bulk',
      body: {'appointments': appointments.map((a) => a.toJson()).toList()},
      fromJson: (data) => (data as List).map((item) => Appointment.fromJson(item)).toList(),
    );

    return response;
  }

  // Export appointments
  static Future<ApiResponse<String>> exportAppointments({
    DateTime? startDate,
    DateTime? endDate,
    String format = 'csv',
    List<String>? appointmentIds,
  }) async {
    final body = <String, dynamic>{
      'format': format,
      if (startDate != null) 'startDate': startDate.toIso8601String().split('T')[0],
      if (endDate != null) 'endDate': endDate.toIso8601String().split('T')[0],
      if (appointmentIds != null) 'appointmentIds': appointmentIds,
    };

    final response = await ApiService.post<String>(
      '/api/appointments/export',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Get available time slots for a staff member
  static Future<ApiResponse<List<DateTime>>> getAvailableTimeSlots({
    required String staffId,
    required DateTime date,
    required int durationMinutes,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<DateTime>>.error('No salon selected');
      }

      final queryParams = <String, String>{
        'staffId': staffId,
        'date': date.toIso8601String().split('T')[0],
        'duration': durationMinutes.toString(),
      };

      final response = await ApiService.get<List<DateTime>>(
        '/api/salons/$salonId/appointments/available-slots',
        queryParams: queryParams,
        fromJson: (data) => (data as List).map((item) => DateTime.parse(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<DateTime>>.error('Failed to get available time slots: $e');
    }
  }

  // Get appointment summary for a date range
  static Future<ApiResponse<Map<String, dynamic>>> getAppointmentSummary({
    DateTime? startDate,
    DateTime? endDate,
    String? staffId,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, dynamic>>.error('No salon selected');
      }

      final queryParams = <String, String>{};

      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
      }
      if (staffId != null) queryParams['staffId'] = staffId;

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/appointments/summary',
        queryParams: queryParams.isNotEmpty ? queryParams : null,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error('Failed to get appointment summary: $e');
    }
  }

  // Validate appointment time slot
  static Future<ApiResponse<Map<String, dynamic>>> validateTimeSlot({
    required DateTime startTime,
    required DateTime endTime,
    required String staffId,
    String? excludeAppointmentId,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, dynamic>>.error('No salon selected');
      }

      final queryParams = <String, String>{
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        'staffId': staffId,
        if (excludeAppointmentId != null) 'excludeAppointmentId': excludeAppointmentId,
      };

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/appointments/validate-slot',
        queryParams: queryParams,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error('Failed to validate time slot: $e');
    }
  }
}
