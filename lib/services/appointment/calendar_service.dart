import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../config/api_config.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../models/pet.dart';

import '../../models/appointment_alternative.dart';
import '../auth/auth_service.dart';
import '../client/client_service.dart';
import '../client/pet_service.dart';
import '../service_management_service.dart';
import '../error_message_service.dart';
import 'appointment_service.dart';

/// Result class for appointment creation operations
class AppointmentCreationResult {
  final bool success;
  final Appointment? appointment;
  final String? errorMessage;
  final String? errorCode;
  final Map<String, dynamic>? errorDetails;
  final List<AppointmentAlternative> alternatives;

  AppointmentCreationResult._({
    required this.success,
    this.appointment,
    this.errorMessage,
    this.errorCode,
    this.errorDetails,
    this.alternatives = const [],
  });

  factory AppointmentCreationResult.success(Appointment appointment) {
    return AppointmentCreationResult._(
      success: true,
      appointment: appointment,
    );
  }

  factory AppointmentCreationResult.failure(
    String errorMessage, {
    String? errorCode,
    Map<String, dynamic>? errorDetails,
  }) {
    final List<AppointmentAlternative> parsedAlternatives = [];

    if (errorCode == 'SCHEDULING_CONFLICT' && errorDetails != null) {
      debugPrint('🔍 Parsing conflict response with errorDetails: $errorDetails');

      // Try multiple possible paths for alternatives in the response
      final alternativesData = errorDetails['details']?['alternatives'] ??  // Primary path for current backend
                              errorDetails['alternatives'] ??
                              errorDetails['suggestions'] ??
                              errorDetails['suggestedTimes'] ??
                              errorDetails['details']?['suggestions'] ??
                              errorDetails['details']?['suggestedTimes'];

      debugPrint('🔍 Found alternatives data: $alternativesData');

      if (alternativesData is List) {
        debugPrint('📋 Processing ${alternativesData.length} alternatives');
        for (final alt in alternativesData) {
          final parsed = _parseAlternative(alt);
          if (parsed != null) {
            parsedAlternatives.add(parsed);
            debugPrint('✅ Successfully parsed alternative: ${parsed.staffName} at ${parsed.startTime}');
          } else {
            debugPrint('❌ Failed to parse alternative: $alt');
          }
        }
      } else {
        debugPrint('⚠️ No alternatives list found in response');
      }

      // If no alternatives found, create some default suggestions based on current time
      if (parsedAlternatives.isEmpty) {
        debugPrint('🔄 Creating default alternatives as fallback');
        parsedAlternatives.addAll(_createDefaultAlternatives());
      }

      debugPrint('📊 Final alternatives count: ${parsedAlternatives.length}');
    }

    return AppointmentCreationResult._(
      success: false,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorDetails: errorDetails,
      alternatives: parsedAlternatives,
    );
  }

  /// Parse alternative from various backend formats
  static AppointmentAlternative? _parseAlternative(dynamic altData) {
    try {
      if (altData is Map<String, dynamic>) {
        // Handle the specific backend format with nested 'suggestion' object
        if (altData.containsKey('suggestion')) {
          final suggestion = altData['suggestion'] as Map<String, dynamic>?;
          if (suggestion != null) {
            return _parseBackendSuggestion(suggestion, altData);
          }
        }

        // Handle full alternative object (direct format)
        if (altData.containsKey('staffId') && altData.containsKey('startTime')) {
          return AppointmentAlternative.fromJson(altData);
        }

        // Handle suggestion format from block-time API
        if (altData.containsKey('startTime') && altData.containsKey('endTime')) {
          return AppointmentAlternative(
            staffId: altData['staffId']?.toString() ?? '',
            staffName: altData['staffName'] ?? altData['description'] ?? 'Staff disponibil',
            startTime: DateTime.parse(altData['startTime']),
            endTime: DateTime.parse(altData['endTime']),
            reason: altData['description'] ?? altData['type'],
          );
        }
      }

      // Handle simple time string format like "10:30", "11:00"
      if (altData is String) {
        final timeMatch = RegExp(r'^(\d{1,2}):(\d{2})$').firstMatch(altData);
        if (timeMatch != null) {
          final hour = int.parse(timeMatch.group(1)!);
          final minute = int.parse(timeMatch.group(2)!);
          final now = DateTime.now();
          final startTime = DateTime(now.year, now.month, now.day, hour, minute);

          return AppointmentAlternative(
            staffId: '',
            staffName: 'Staff disponibil',
            startTime: startTime,
            endTime: startTime.add(const Duration(hours: 1)),
            reason: 'Oră sugerată',
          );
        }
      }
    } catch (e) {
      debugPrint('Error parsing alternative: $e');
    }
    return null;
  }

  /// Parse the specific backend suggestion format
  static AppointmentAlternative? _parseBackendSuggestion(
    Map<String, dynamic> suggestion,
    Map<String, dynamic> parentData
  ) {
    try {
      debugPrint('🔍 Parsing backend suggestion: $suggestion');
      debugPrint('🔍 Parent data: $parentData');

      // Extract date and time information
      final dateStr = suggestion['date'] as String?;
      final startTimeStr = suggestion['startTime'] as String?;
      final endTimeStr = suggestion['endTime'] as String?;

      debugPrint('🔍 Extracted fields: date=$dateStr, startTime=$startTimeStr, endTime=$endTimeStr');

      if (dateStr == null || startTimeStr == null || endTimeStr == null) {
        debugPrint('❌ Missing required fields in suggestion: date=$dateStr, startTime=$startTimeStr, endTime=$endTimeStr');
        return null;
      }

      // Parse date (format: 2025-06-14)
      final dateParts = dateStr.split('-');
      if (dateParts.length != 3) {
        debugPrint('❌ Invalid date format: $dateStr (expected YYYY-MM-DD)');
        return null;
      }

      final year = int.tryParse(dateParts[0]);
      final month = int.tryParse(dateParts[1]);
      final day = int.tryParse(dateParts[2]);

      if (year == null || month == null || day == null) {
        debugPrint('❌ Failed to parse date components: year=$year, month=$month, day=$day');
        return null;
      }

      debugPrint('✅ Parsed date: $year-$month-$day');

      // Parse start time (format: 09:00 or 09:00:00)
      final startTimeParts = startTimeStr.split(':');
      if (startTimeParts.length < 2 || startTimeParts.length > 3) {
        debugPrint('Invalid start time format: $startTimeStr (expected HH:MM or HH:MM:SS)');
        return null;
      }

      final startHour = int.tryParse(startTimeParts[0]);
      final startMinute = int.tryParse(startTimeParts[1]);

      if (startHour == null || startMinute == null) {
        debugPrint('❌ Failed to parse start time components: hour=$startHour, minute=$startMinute');
        return null;
      }

      debugPrint('✅ Parsed start time: $startHour:$startMinute');

      // Parse end time (format: 11:08 or 11:08:00)
      final endTimeParts = endTimeStr.split(':');
      if (endTimeParts.length < 2 || endTimeParts.length > 3) {
        debugPrint('❌ Invalid end time format: $endTimeStr (expected HH:MM or HH:MM:SS)');
        return null;
      }

      final endHour = int.tryParse(endTimeParts[0]);
      final endMinute = int.tryParse(endTimeParts[1]);

      if (endHour == null || endMinute == null) {
        debugPrint('❌ Failed to parse end time components: hour=$endHour, minute=$endMinute');
        return null;
      }

      debugPrint('✅ Parsed end time: $endHour:$endMinute');

      // Create DateTime objects
      final startTime = DateTime(year, month, day, startHour, startMinute);
      final endTime = DateTime(year, month, day, endHour, endMinute);

      // Extract additional information
      final staffId = suggestion['staffId']?.toString() ?? '';
      final staffName = suggestion['staffName'] ?? 'Staff disponibil';
      final reason = parentData['reason'] ?? parentData['type'] ?? 'Sugestie';
      final priority = parentData['priority'] as int?;
      final confidence = (parentData['confidence'] as num?)?.toDouble();

      debugPrint('✅ Parsed alternative: $staffName at $startTimeStr-$endTimeStr on $dateStr');

      return AppointmentAlternative(
        staffId: staffId,
        staffName: staffName,
        startTime: startTime,
        endTime: endTime,
        priority: priority,
        confidence: confidence,
        reason: reason,
      );
    } catch (e) {
      debugPrint('❌ Error parsing backend suggestion: $e');
      return null;
    }
  }

  /// Create default alternatives when none are provided by backend
  static List<AppointmentAlternative> _createDefaultAlternatives() {
    final now = DateTime.now();
    final alternatives = <AppointmentAlternative>[];

    // Suggest next available hours (9 AM to 5 PM)
    for (int hour = 9; hour <= 17; hour++) {
      final startTime = DateTime(now.year, now.month, now.day, hour, 0);
      if (startTime.isAfter(now)) {
        alternatives.add(AppointmentAlternative(
          staffId: '',
          staffName: 'Staff disponibil',
          startTime: startTime,
          endTime: startTime.add(const Duration(hours: 1)),
          reason: 'Oră sugerată',
          priority: alternatives.length + 1,
        ));

        if (alternatives.length >= 3) break; // Limit to 3 suggestions
      }
    }

    return alternatives;
  }

  /// Get user-friendly error message in Romanian
  String get userFriendlyError {
    return ErrorMessageService.getApiErrorMessage(
      errorCode: errorCode,
      errorMessage: errorMessage,
      fallbackMessage: 'Nu s-a putut crea programarea',
    );
  }

  /// Check if this is a scheduling conflict error
  bool get isSchedulingConflict => errorCode == 'SCHEDULING_CONFLICT';

  /// Check if this is an invalid time slot error
  bool get isInvalidTimeSlot => errorCode == 'INVALID_TIME_SLOT';

  /// Check if this is a staff unavailable error
  bool get isStaffUnavailable => errorCode == 'STAFF_UNAVAILABLE';
}

class CalendarService {
  // Singleton pattern
  static final CalendarService _instance = CalendarService._internal();
  factory CalendarService() => _instance;
  CalendarService._internal();

  // Cache for appointment data to improve performance
  final Map<String, List<Appointment>> _appointmentCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Clear cache when appointments are modified
  void clearCache() {
    _appointmentCache.clear();
    _cacheTimestamps.clear();
    debugPrint('📊 Calendar cache cleared');
  }

  // Check if cache is valid for a given key
  bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;

    final isValid = DateTime.now().difference(timestamp) < _cacheExpiry;
    if (!isValid) {
      _appointmentCache.remove(key);
      _cacheTimestamps.remove(key);
    }
    return isValid;
  }

  // Base URL for API calls (using centralized configuration)
  String get baseUrl => ApiConfig.baseUrl;

  // Helper method to get current salon ID
  Future<String?> _getCurrentSalonId() async {
    try {
      return await AuthService.getCurrentSalonId();
    } catch (e) {
      debugPrint('❌ Error getting current salon ID: $e');
      return null;
    }
  }

  // Helper method to get HTTP headers with authentication
  Future<Map<String, String>> _getHeaders() async {
    try {
      final token = await AuthService.getAccessToken();

      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      };
    } catch (e) {
      debugPrint('❌ Error getting headers: $e');
      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
    }
  }

  // Get appointments for a specific date using HTTP service (optimized with caching)
  Future<List<Appointment>> getAppointmentsForDate(DateTime date) async {
    final cacheKey = 'date_${date.year}-${date.month}-${date.day}';

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      final cachedData = _appointmentCache[cacheKey];
      if (cachedData != null) {
        debugPrint('📊 Using cached appointments for $cacheKey');
        return cachedData;
      }
    }

    try {
      // Use date parameter to fetch only appointments for that specific date
      final response = await AppointmentService.getAppointments(date: date);

      if (response.success && response.data != null) {
        // Cache the result
        _appointmentCache[cacheKey] = response.data!;
        _cacheTimestamps[cacheKey] = DateTime.now();
        debugPrint('📊 Cached appointments for $cacheKey (${response.data!.length} appointments)');

        return response.data!;
      } else {
        // Return empty list if HTTP fails
        debugPrint('HTTP service failed for date: $date');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching appointments from HTTP: $e');
      // Return empty list on error
      return [];
    }
  }

  // Get appointments for a date range using HTTP service (optimized - single API call)
  Future<List<Appointment>> getAppointmentsForDateRange(DateTime startDate, DateTime endDate) async {
    try {
      // Use date range parameters to fetch appointments efficiently
      final response = await AppointmentService.getAppointments(
        startDate: startDate,
        endDate: endDate,
      );

      if (response.success && response.data != null) {
        debugPrint('✅ Fetched ${response.data!.length} appointments for date range: ${startDate.toString().split(' ')[0]} to ${endDate.toString().split(' ')[0]}');
        return response.data!;
      } else {
        // Return empty list if HTTP fails
        debugPrint('HTTP service failed for date range: $startDate to $endDate');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching appointments for date range from HTTP: $e');
      // Return empty list on error
      return [];
    }
  }

  // Get all clients using HTTP service
  Future<List<Client>> getAllClients() async {
    try {
      final response = await ClientService.getClients();

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        debugPrint('HTTP service failed for clients');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching all clients: $e');
      return [];
    }
  }

  // Get a client by ID using HTTP service
  Future<Client?> getClientById(String clientId) async {
    try {
      final response = await ClientService.getClients();

      if (response.success && response.data != null) {
        // Find client by ID
        try {
          return response.data!.firstWhere((client) => client.id == clientId);
        } catch (e) {
          debugPrint('Client with ID $clientId not found');
          return null;
        }
      } else {
        debugPrint('HTTP service failed for client ID: $clientId');
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching client by ID: $e');
      return null;
    }
  }

  // Get a pet by ID using HTTP service
  Future<Pet?> getPetById(String petId) async {
    try {
      final response = await PetService.getPets();

      if (response.success && response.data != null) {
        // Find pet by ID
        try {
          return response.data!.firstWhere((pet) => pet.id == petId);
        } catch (e) {
          debugPrint('Pet with ID $petId not found');
          return null;
        }
      } else {
        debugPrint('HTTP service failed for pet ID: $petId');
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching pet by ID: $e');
      return null;
    }
  }

  // Get all pets for a client using HTTP service
  Future<List<Pet>> getPetsForClient(String clientId) async {
    try {
      debugPrint('🔄 CalendarService: Calling PetService.getPetsForClient for client: $clientId');
      final response = await PetService.getPetsForClient(clientId);

      if (response.success && response.data != null) {
        debugPrint('✅ CalendarService: PetService returned ${response.data!.length} pets for client $clientId');
        return response.data!;
      } else {
        debugPrint('❌ CalendarService: HTTP service failed for client pets: $clientId - Error: ${response.error}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ CalendarService: Exception fetching pets for client $clientId: $e');
      return [];
    }
  }



  // Add a new appointment using HTTP service
  Future<Appointment?> addAppointment(Appointment appointment) async {
    try {
      final response = await AppointmentService.createAppointment(appointment);

      if (response.success && response.data != null) {
        return response.data;
      } else {
        debugPrint('HTTP service failed to create appointment');
        return null;
      }
    } catch (e) {
      debugPrint('Error creating appointment: $e');
      return null;
    }
  }

  // Add a new appointment from form data using ScheduleAppointmentRequest DTO
  Future<AppointmentCreationResult> addAppointmentFromFormData(Map<String, dynamic> scheduleRequest) async {
    try {
      final response = await AppointmentService.createAppointmentFromFormData(scheduleRequest);

      if (response.success && response.data != null) {
        return AppointmentCreationResult.success(response.data!);
      } else {
        debugPrint('HTTP service failed to create appointment from form data: ${response.error}');
        return AppointmentCreationResult.failure(
          response.error ?? 'Unknown error',
          errorCode: response.errorCode,
          errorDetails: response.errorDetails,
        );
      }
    } catch (e) {
      debugPrint('Error creating appointment from form data: $e');
      return AppointmentCreationResult.failure('Error creating appointment: $e');
    }
  }

  // Get available grooming services with their durations using HTTP service
  Future<Map<String, int>> getServiceDurations() async {
    try {
      final response = await ServiceManagementService.getServices();

      if (response.success && response.data != null) {
        // Convert services to duration map
        final Map<String, int> durations = {};
        for (final service in response.data!) {
          durations[service.name] = service.duration;
        }
        return durations;
      } else {
        debugPrint('HTTP service failed for service durations');
        return {};
      }
    } catch (e) {
      debugPrint('Error fetching service durations: $e');
      return {};
    }
  }

  // Check if a time slot is available (no conflicts)
  Future<bool> isTimeSlotAvailable(DateTime start, DateTime end, [String? excludeAppointmentId]) async {
    final appointments = await getAppointmentsForDate(start);

    for (final appointment in appointments) {
      // Skip the appointment we're excluding (for rescheduling)
      if (excludeAppointmentId != null && appointment.id == excludeAppointmentId) {
        continue;
      }

      // Skip canceled appointments
      if (appointment.status == 'canceled') {
        continue;
      }

      // Check for overlap
      if (start.isBefore(appointment.endTime) && end.isAfter(appointment.startTime)) {
        return false; // Conflict found
      }
    }

    return true; // No conflicts
  }

  // Block time in the calendar using HTTP service
  Future<bool> blockTime(DateTime start, DateTime end, String reason, {String? staffId}) async {
    try {
      final salonId = await _getCurrentSalonId();
      if (salonId == null) {
        debugPrint('❌ No current salon ID available for blocking time');
        return false;
      }

      final url = Uri.parse('$baseUrl/api/salons/$salonId/block-time');

      final requestBody = {
        'startTime': start.toUtc().toIso8601String(),
        'endTime': end.toUtc().toIso8601String(),
        'reason': reason,
        'staffIds': staffId != null ? [staffId] : [],
        'isRecurring': false,
      };

      debugPrint('🔄 Blocking time: POST $url');
      debugPrint('📤 Request body: ${jsonEncode(requestBody)}');

      final response = await http.post(
        url,
        headers: await _getHeaders(),
        body: jsonEncode(requestBody),
      );

      debugPrint('📥 Block time response: ${response.statusCode}');
      debugPrint('📥 Response body: ${response.body}');

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          debugPrint('✅ Time blocked successfully');
          return true;
        } else {
          debugPrint('❌ Block time failed: ${responseData['error']?['message']}');
          return false;
        }
      } else if (response.statusCode == 409) {
        // Scheduling conflict
        final responseData = jsonDecode(response.body);
        debugPrint('⚠️ Scheduling conflict: ${responseData['error']?['message']}');
        return false;
      } else {
        debugPrint('❌ Block time failed with status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error blocking time: $e');
      return false;
    }
  }

  // Get blocked time slots for a salon
  Future<List<Map<String, dynamic>>> getBlockedTimes({
    DateTime? startDate,
    DateTime? endDate,
    String? staffId,
    String? reason,
    String? status,
  }) async {
    debugPrint('getBlockedTimes: $startDate - $endDate, staffId: $staffId, reason: $reason, status: $status');
    try {
      final salonId = await _getCurrentSalonId();
      if (salonId == null) {
        debugPrint('❌ No current salon ID available for getting blocked times');
        return [];
      }

      final queryParams = <String, String>{};
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
      }
      if (staffId != null) {
        queryParams['staffId'] = staffId;
      }
      if (reason != null) {
        queryParams['reason'] = reason;
      }
      if (status != null) {
        queryParams['status'] = status;
      }

      final uri = Uri.parse('$baseUrl/api/salons/$salonId/block-time').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      debugPrint('🔄 Getting blocked times: GET $uri');

      final response = await http.get(
        uri,
        headers: await _getHeaders(),
      );

      debugPrint('📥 Get blocked times response: ${response.statusCode}');
      debugPrint('📥 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        debugPrint('📥 Parsed response data: $responseData');

        if (responseData['success'] == true) {
          final data = responseData['data'];
          debugPrint('📥 Response data field: $data');

          final blocks = data['blocks'] as List<dynamic>? ?? [];
          debugPrint('✅ Retrieved ${blocks.length} blocked time slots');

          // Log each block for debugging
          for (int i = 0; i < blocks.length; i++) {
            debugPrint('📋 Block $i: ${blocks[i]}');
          }

          return blocks.cast<Map<String, dynamic>>();
        } else {
          debugPrint('❌ Response success=false: ${responseData['error']}');
        }
      }

      debugPrint('❌ Failed to get blocked times: ${response.statusCode}');
      return [];
    } catch (e) {
      debugPrint('❌ Error getting blocked times: $e');
      return [];
    }
  }

  // Check availability for blocking time
  Future<Map<String, dynamic>?> checkBlockTimeAvailability({
    required DateTime startTime,
    required DateTime endTime,
    required List<String> staffIds,
  }) async {
    try {
      final salonId = await _getCurrentSalonId();
      if (salonId == null) {
        debugPrint('❌ No current salon ID available for availability check');
        return null;
      }

      final url = Uri.parse('$baseUrl/api/salons/$salonId/block-time/check-availability');

      final requestBody = {
        'startTime': startTime.toUtc().toIso8601String(),
        'endTime': endTime.toUtc().toIso8601String(),
        'staffIds': staffIds,
      };

      debugPrint('🔄 Checking availability: POST $url');
      debugPrint('📤 Request body: ${jsonEncode(requestBody)}');

      final response = await http.post(
        url,
        headers: await _getHeaders(),
        body: jsonEncode(requestBody),
      );

      debugPrint('📥 Availability check response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          debugPrint('✅ Availability check completed');
          return responseData['data'];
        }
      }

      debugPrint('❌ Availability check failed: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('❌ Error checking availability: $e');
      return null;
    }
  }

  // Delete (cancel) a blocked time slot
  Future<bool> deleteBlockTime(String blockId, {String? reason}) async {
    try {
      final salonId = await _getCurrentSalonId();
      if (salonId == null) {
        debugPrint('❌ No current salon ID available for deleting block time');
        return false;
      }

      final queryParams = <String, String>{};
      if (reason != null) {
        queryParams['reason'] = reason;
      }

      final uri = Uri.parse('$baseUrl/api/salons/$salonId/block-time/$blockId').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      debugPrint('🔄 Deleting block time: DELETE $uri');

      final response = await http.delete(
        uri,
        headers: await _getHeaders(),
      );

      debugPrint('📥 Delete block time response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          debugPrint('✅ Block time deleted successfully');
          return true;
        }
      }

      debugPrint('❌ Failed to delete block time: ${response.statusCode}');
      return false;
    } catch (e) {
      debugPrint('❌ Error deleting block time: $e');
      return false;
    }
  }



}
