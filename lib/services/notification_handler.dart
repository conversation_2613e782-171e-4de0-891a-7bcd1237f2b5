import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Simple notification handler for receiving and displaying notifications
/// All notification logic is handled by the backend
class NotificationHandler {
  static FirebaseMessaging? _messaging;
  static FlutterLocalNotificationsPlugin? _localNotifications;
  static bool _isInitialized = false;

  /// Initialize Firebase messaging and local notifications
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        if (kDebugMode) {
          debugPrint('🔔 Notification handler already initialized');
        }
        return true;
      }

      if (kDebugMode) {
        debugPrint('🔔 Initializing notification handler...');
      }

      // Initialize Firebase Messaging
      _messaging = FirebaseMessaging.instance;

      // Request permission for notifications
      final settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        if (kDebugMode) {
          debugPrint('⚠️ Notification permission denied');
        }
        return false;
      }

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get FCM token and register with backend
      await _registerFCMToken();

      // Set up message handlers
      _setupMessageHandlers();

      _isInitialized = true;
      if (kDebugMode) {
        debugPrint('✅ Notification handler initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error initializing notification handler: $e');
      }
      return false;
    }
  }

  /// Initialize local notifications for foreground display
  static Future<void> _initializeLocalNotifications() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications!.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Get FCM token and register with backend
  static Future<void> _registerFCMToken() async {
    try {
      final token = await _messaging!.getToken();
      if (token != null) {
        if (kDebugMode) {
          debugPrint('📱 FCM Token: ${token.substring(0, 20)}...');
        }

        // Send token to backend
        await _sendTokenToBackend(token);

        // Listen for token refresh
        _messaging!.onTokenRefresh.listen(_sendTokenToBackend);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error registering FCM token: $e');
      }
    }
  }

  /// Send FCM token to backend
  static Future<void> _sendTokenToBackend(String token) async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 Attempting to register FCM token with backend...');
      }

      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        if (kDebugMode) {
          debugPrint('⚠️ No user ID available, skipping token registration');
          debugPrint('   This is normal if user is not logged in yet');
          debugPrint('   Token will be registered after successful login');
        }
        return;
      }

      if (kDebugMode) {
        debugPrint('🔑 User ID found: $userId');
        debugPrint('📱 Registering FCM token: ${token.substring(0, 20)}...');
      }

      // Determine platform
      String platformName;
      if (kIsWeb) {
        platformName = 'web';
      } else if (Platform.isIOS) {
        platformName = 'ios';
      } else if (Platform.isAndroid) {
        platformName = 'android';
      } else {
        platformName = 'unknown';
      }

      final response = await ApiService.post<void>(
        '/api/notifications/register-token',
        body: {
          'userId': userId,
          'token': token,
          'platform': platformName,
        },
      );

      if (response.success) {
        if (kDebugMode) {
          debugPrint('✅ FCM token registered successfully with backend');
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ Failed to register FCM token: ${response.error}');
          debugPrint('   Status code: ${response.statusCode}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error sending token to backend: $e');
        debugPrint('   This might be due to network issues or backend unavailability');
      }
    }
  }

  /// Set up Firebase message handlers
  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageTap);

    // Handle app launch from terminated state
    _messaging!.getInitialMessage().then((message) {
      if (message != null) {
        _handleMessageTap(message);
      }
    });
  }

  /// Handle messages received while app is in foreground
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      if (kDebugMode) {
        debugPrint('🔔 Foreground message: ${message.notification?.title}');
      }

      // Show local notification
      await _showLocalNotification(message);

      // Show in-app notification if needed
      _showInAppNotification(message);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error handling foreground message: $e');
      }
    }
  }

  /// Handle message tap (background or terminated)
  static void _handleMessageTap(RemoteMessage message) {
    try {
      if (kDebugMode) {
        debugPrint('👆 Message tapped: ${message.data}');
      }

      // Navigate based on notification data
      _navigateFromNotification(message.data);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error handling message tap: $e');
      }
    }
  }

  /// Show local notification for foreground messages
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'appointment_notifications',
        'Appointment Notifications',
        channelDescription: 'Notifications for appointment updates',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications!.show(
        message.hashCode,
        message.notification?.title ?? 'Animalia Grooming',
        message.notification?.body ?? '',
        details,
        payload: message.data.toString(),
      );
      if (kDebugMode) {
        debugPrint('📲 Local notification displayed: '
            '${message.notification?.title ?? ''}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error showing local notification: $e');
      }
    }
  }

  /// Show in-app notification overlay
  static void _showInAppNotification(RemoteMessage message) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;

    // Show snackbar for in-app notification
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.notification?.title ?? 'Notificare',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            if (message.notification?.body != null)
              Text(message.notification!.body!),
          ],
        ),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Vezi',
          onPressed: () => _navigateFromNotification(message.data),
        ),
      ),
    );
    if (kDebugMode) {
      debugPrint('📢 In-app notification shown: '
          '${message.notification?.title ?? ''}');
    }
  }

  /// Navigate to appropriate screen based on notification data
  static void _navigateFromNotification(Map<String, dynamic> data) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;

    try {
      final type = data['type'] as String?;
      final appointmentId = data['appointmentId'] as String?;
      final clientId = data['clientId'] as String?;

      switch (type) {
        case 'appointment_created':
        case 'appointment_updated':
        case 'appointment_cancelled':
        case 'appointment_completed':
          if (appointmentId != null) {
            if (kDebugMode) {
              debugPrint('➡️ Navigate to appointment details: $appointmentId');
            }
            Navigator.of(context).pushNamed(
              '/appointment-details',
              arguments: appointmentId,
            );
          }
          break;

        case 'appointment_reminder':
          // Navigate to today's calendar view
          if (kDebugMode) {
            debugPrint('➡️ Navigate to calendar from reminder');
          }
          Navigator.of(context).pushNamed('/calendar');
          break;

        case 'client_message':
          if (clientId != null) {
            if (kDebugMode) {
              debugPrint('➡️ Navigate to client details: $clientId');
            }
            Navigator.of(context).pushNamed(
              '/client-details',
              arguments: clientId,
            );
          }
          break;

        default:
          // Navigate to dashboard for unknown types
          if (kDebugMode) {
            debugPrint('➡️ Navigate to dashboard (unknown type: $type)');
          }
          Navigator.of(context).pushNamed('/dashboard');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error navigating from notification: $e');
      }
    }
  }

  /// Handle notification tap from local notifications
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      if (kDebugMode) {
        debugPrint('👆 Local notification tapped: ${response.payload}');
      }

      // Parse payload and navigate
      if (response.payload != null) {
        // Parse the payload data and navigate accordingly
        // This would contain the same data as Firebase message
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error handling notification tap: $e');
      }
    }
  }

  /// Get current FCM token
  static Future<String?> getCurrentToken() async {
    try {
      return await _messaging?.getToken();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    try {
      final settings = await _messaging?.getNotificationSettings();
      return settings?.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error checking notification status: $e');
      }
      return false;
    }
  }

  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    try {
      final settings = await _messaging?.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      return settings?.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error requesting notification permissions: $e');
      }
      return false;
    }
  }

  /// Unregister FCM token (for logout)
  static Future<void> unregisterToken() async {
    try {
      final token = await _messaging?.getToken();
      if (token == null) return;

      if (kDebugMode) {
        debugPrint('📤 Unregistering FCM token...');
      }

      final response = await ApiService.delete<void>(
        '/api/users/fcm-token',
        body: {'token': token},
      );

      if (response.success) {
        if (kDebugMode) {
          debugPrint('✅ FCM token unregistered successfully');
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ Failed to unregister FCM token: ${response.error}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error unregistering FCM token: $e');
      }
    }

    _isInitialized = false;
  }


  /// Get current FCM token
  static Future<String?> getFcmToken() async {
    try {
      return await _messaging?.getToken();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Manually register FCM token (call after login)
  static Future<void> registerTokenAfterLogin() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        debugPrint('⚠️ NotificationHandler not initialized, cannot register token');
      }
      return;
    }

    try {
      final token = await _messaging?.getToken();
      if (token != null) {
        if (kDebugMode) {
          debugPrint('🔄 Manually registering FCM token after login...');
        }
        await _sendTokenToBackend(token);
      } else {
        if (kDebugMode) {
          debugPrint('⚠️ No FCM token available for registration');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error in manual token registration: $e');
      }
    }
  }
}

/// Navigation service for global navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    debugPrint('🔔 Background message: ${message.notification?.title}');
  }
  // Background messages are automatically handled by the system
  // No additional processing needed here
}
