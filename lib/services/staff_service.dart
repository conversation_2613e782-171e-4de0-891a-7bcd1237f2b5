import 'package:flutter/material.dart';
import '../models/api_response.dart';
import '../models/user_role.dart';
import '../config/api_config.dart';
import '../utils/formatters/phone_number_utils.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Staff member response model for API communication
class StaffResponse {
  final String id; // This should be staffId for working hours endpoints
  final String? userId; // User account ID (different from staffId)
  final String name;
  final String? nickname; // Display name for appointments and calendar
  final String? phone;
  final String? email;
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final bool isActive;
  final DateTime joinedAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? workingHours;
  final List<String> specialties;
  final String? notes;
  final int experience;
  final double rating;
  final int totalAppointments;
  final bool isPending; // Flag to indicate if this is a pending invitation

  StaffResponse({
    required this.id,
    this.userId,
    required this.name,
    this.nickname,
    this.phone,
    this.email,
    required this.groomerRole,
    required this.clientDataPermission,
    this.isActive = true,
    required this.joinedAt,
    this.updatedAt,
    this.workingHours,
    this.specialties = const [],
    this.notes,
    this.experience = 0,
    this.rating = 0.0,
    this.totalAppointments = 0,
    this.isPending = false, // Default to false
  });

  /// Helper method to parse client data permission from various formats
  static ClientDataPermission _parseClientDataPermission(Map<String, dynamic> json) {
    // Try nested permissions object first
    if (json['permissions'] != null && json['permissions'] is Map) {
      final permissions = json['permissions'] as Map<String, dynamic>;
      final clientDataAccess = permissions['clientDataAccess'];
      if (clientDataAccess != null) {
        // Map backend enum values to frontend enum values
        switch (clientDataAccess.toString().toUpperCase()) {
          case 'FULL':
            return ClientDataPermission.fullAccess;
          case 'LIMITED':
            return ClientDataPermission.limitedAccess;
          case 'NONE':
            return ClientDataPermission.noAccess;
          default:
            return ClientDataPermission.noAccess;
        }
      }
    }

    // Fallback to direct field
    return ClientDataPermission.fromString(json['clientDataPermission'] ?? 'NO_ACCESS');
  }

  /// Create from JSON
  factory StaffResponse.fromJson(Map<String, dynamic> json) {
    // Get staffId and userId separately - staffId is needed for working hours endpoints
    final staffId = json['staffId'] ??
                   json['id'] ??
                   json['staff_id'] ??
                   'unknown-staff-id'; // Provide default to prevent null

    final userId = json['userId'] ?? json['user_id'];

    final name = json['userName'] ?? json['name'] ?? 'Unknown Staff';
    final nickname = json['nickname'] ?? json['displayName'];

    final staffResponse = StaffResponse(
      id: staffId,
      userId: userId,
      name: name,
      nickname: nickname,
      phone: json['userPhone'] ?? json['phone'],
      email: json['userEmail'] ?? json['email'],
      groomerRole: GroomerRole.fromString(json['role'] ?? json['groomerRole'] ?? 'GROOMER'),
      clientDataPermission: _parseClientDataPermission(json),
      isActive: json['isActive'] ?? true,
      joinedAt: json['hiredAt'] != null
          ? DateTime.parse(json['hiredAt'])
          : (json['joinedAt'] != null
              ? DateTime.parse(json['joinedAt'])
              : DateTime.now()),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
      workingHours: json['workingHours'] != null
          ? Map<String, dynamic>.from(json['workingHours'])
          : null,
      specialties: json['specializations'] != null
          ? List<String>.from(json['specializations'])
          : (json['specialties'] != null
              ? List<String>.from(json['specialties'])
              : []),
      notes: json['notes'],
      experience: json['experience'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalAppointments: json['totalAppointments'] ?? 0,
    );
    return staffResponse;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'staffId': id,
      'userId': userId,
      'name': name,
      'nickname': nickname,
      'phone': phone,
      'email': email,
      'groomerRole': groomerRole.value,
      'clientDataPermission': clientDataPermission.value,
      'isActive': isActive,
      'joinedAt': joinedAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'workingHours': workingHours,
      'specialties': specialties,
      'notes': notes,
      'experience': experience,
      'rating': rating,
      'totalAppointments': totalAppointments,
    };
  }

  /// Get formatted phone number for display
  String? get formattedPhone => phone != null ? PhoneNumberUtils.formatForDisplay(phone!) : null;

  /// Get normalized phone number for API calls
  String? get normalizedPhone => phone != null ? PhoneNumberUtils.normalizeForApi(phone!) : null;

  /// Get display name (nickname if available, otherwise full name)
  String get displayName => nickname?.isNotEmpty == true ? nickname! : name;

  /// Copy with method for immutable updates
  StaffResponse copyWith({
    String? staffId,
    String? userId,
    String? name,
    String? nickname,
    String? phone,
    String? email,
    GroomerRole? groomerRole,
    ClientDataPermission? clientDataPermission,
    bool? isActive,
    DateTime? joinedAt,
    DateTime? updatedAt,
    Map<String, dynamic>? workingHours,
    List<String>? specialties,
    String? notes,
    int? experience,
    double? rating,
    int? totalAppointments,
  }) {
    return StaffResponse(
      id: staffId ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      nickname: nickname ?? this.nickname,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      groomerRole: groomerRole ?? this.groomerRole,
      clientDataPermission: clientDataPermission ?? this.clientDataPermission,
      isActive: isActive ?? this.isActive,
      joinedAt: joinedAt ?? this.joinedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      workingHours: workingHours ?? this.workingHours,
      specialties: specialties ?? this.specialties,
      notes: notes ?? this.notes,
      experience: experience ?? this.experience,
      rating: rating ?? this.rating,
      totalAppointments: totalAppointments ?? this.totalAppointments,
    );
  }
}

/// Pending staff invitation model
class PendingStaffInvitation {
  final String invitationId;
  final String phoneNumber;
  final String? nickname; // Display name for the invited staff member
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final String status;
  final String? message;
  final String invitedBy;
  final DateTime invitedAt;
  final DateTime expiresAt;
  final bool isExpired;

  PendingStaffInvitation({
    required this.invitationId,
    required this.phoneNumber,
    this.nickname,
    required this.groomerRole,
    required this.clientDataPermission,
    required this.status,
    this.message,
    required this.invitedBy,
    required this.invitedAt,
    required this.expiresAt,
    required this.isExpired,
  });

  /// Create from JSON
  factory PendingStaffInvitation.fromJson(Map<String, dynamic> json) {
    return PendingStaffInvitation(
      invitationId: json['invitationId'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      nickname: json['nickname'] ?? json['displayName'],
      groomerRole: GroomerRole.fromString(json['groomerRole'] ?? 'REGULAR_GROOMER'),
      clientDataPermission: ClientDataPermission.fromString(json['clientDataPermission'] ?? 'NO_ACCESS'),
      status: json['status'] ?? 'PENDING',
      message: json['message'],
      invitedBy: json['invitedBy'] ?? '',
      invitedAt: json['invitedAt'] != null
          ? DateTime.parse(json['invitedAt'])
          : DateTime.now(),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'])
          : DateTime.now(),
      isExpired: json['isExpired'] ?? false,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'invitationId': invitationId,
      'phoneNumber': phoneNumber,
      'nickname': nickname,
      'groomerRole': groomerRole.value,
      'clientDataPermission': clientDataPermission.value,
      'status': status,
      'message': message,
      'invitedBy': invitedBy,
      'invitedAt': invitedAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'isExpired': isExpired,
    };
  }

  /// Get display name for pending invitation (nickname if available, otherwise formatted phone number)
  String get displayName => nickname?.isNotEmpty == true ? nickname! : PhoneNumberUtils.formatForDisplay(phoneNumber);

  /// Get formatted phone number for display
  String get formattedPhoneNumber => PhoneNumberUtils.formatForDisplay(phoneNumber);

  /// Get normalized phone number for API calls
  String get normalizedPhoneNumber => PhoneNumberUtils.normalizeForApi(phoneNumber);

  /// Check if invitation is still valid
  bool get isValid => !isExpired && DateTime.now().isBefore(expiresAt);
}

/// Staff list response model
class StaffListResponse {
  final List<StaffResponse> activeStaff;
  final List<PendingStaffInvitation> pendingStaff;
  final int totalActiveCount;
  final int totalPendingCount;
  final int activeCount;
  final int inactiveCount;

  StaffListResponse({
    required this.activeStaff,
    required this.pendingStaff,
    required this.totalActiveCount,
    required this.totalPendingCount,
    required this.activeCount,
    required this.inactiveCount,
  });

  /// Get all staff (active + pending) as a combined list for UI
  List<dynamic> get allStaff => [...activeStaff, ...pendingStaff];

  /// Get total count of all staff (active + pending)
  int get totalCount => totalActiveCount + totalPendingCount;

  /// Create from JSON
  factory StaffListResponse.fromJson(dynamic json) {
    // Handle case where backend returns a direct array instead of object with activeStaff/pendingStaff
    if (json is List) {
      // Backend returned a direct array of staff members
      final staffList = json.map((item) => StaffResponse.fromJson(item)).toList();
      return StaffListResponse(
        activeStaff: staffList.where((staff) => staff.isActive).toList(),
        pendingStaff: [], // No pending staff in direct array response
        totalActiveCount: staffList.where((staff) => staff.isActive).length,
        totalPendingCount: 0,
        activeCount: staffList.where((staff) => staff.isActive).length,
        inactiveCount: staffList.where((staff) => !staff.isActive).length,
      );
    } else if (json is Map<String, dynamic>) {
      // Expected format with activeStaff and pendingStaff properties
      return StaffListResponse(
        activeStaff: json['activeStaff'] != null
            ? (json['activeStaff'] as List).map((item) => StaffResponse.fromJson(item)).toList()
            : [],
        pendingStaff: json['pendingStaff'] != null
            ? (json['pendingStaff'] as List).map((item) => PendingStaffInvitation.fromJson(item)).toList()
            : [],
        totalActiveCount: json['totalActiveCount'] ?? 0,
        totalPendingCount: json['totalPendingCount'] ?? 0,
        activeCount: json['activeCount'] ?? 0,
        inactiveCount: json['inactiveCount'] ?? 0,
      );
    } else {
      throw Exception('Unexpected staff list response format: ${json.runtimeType}');
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'activeStaff': activeStaff.map((s) => s.toJson()).toList(),
      'pendingStaff': pendingStaff.map((p) => p.toJson()).toList(),
      'totalActiveCount': totalActiveCount,
      'totalPendingCount': totalPendingCount,
      'activeCount': activeCount,
      'inactiveCount': inactiveCount,
    };
  }
}

/// Add staff request model
class AddStaffRequest {
  final String phoneNumber;
  final String? nickname; // Display name for the staff member
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final String? notes;

  AddStaffRequest({
    required String phoneNumber,
    this.nickname,
    required this.groomerRole,
    required this.clientDataPermission,
    this.notes,
  }) : phoneNumber = PhoneNumberUtils.normalizeForApi(phoneNumber);

  /// Create from raw input (with phone number formatting)
  factory AddStaffRequest.fromInput({
    required String phoneNumber,
    String? nickname,
    required GroomerRole groomerRole,
    required ClientDataPermission clientDataPermission,
    String? notes,
  }) {
    return AddStaffRequest(
      phoneNumber: phoneNumber, // Will be normalized in constructor
      nickname: nickname,
      groomerRole: groomerRole,
      clientDataPermission: clientDataPermission,
      notes: notes,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'phoneNumber': phoneNumber, // Already normalized to +40XXXXXXXXX format
      'groomerRole': groomerRole.value, // Backend expects 'role' instead of 'groomerRole'
      'clientDataPermission': clientDataPermission.value,
    };

    // Only add optional fields if they're not null or empty
    if (nickname != null && nickname!.isNotEmpty) {
      json['nickname'] = nickname;
    }

    if (notes != null && notes!.isNotEmpty) {
      json['notes'] = notes;
    }

    return json;
  }

  /// Validate the request using Romanian phone number validation
  String? validate() {
    // Use the phone number utility for validation
    return PhoneNumberUtils.getValidationError(phoneNumber);
  }

  /// Get formatted phone number for display
  String get formattedPhoneNumber => PhoneNumberUtils.formatForDisplay(phoneNumber);

  /// Check if phone number is valid Romanian mobile
  bool get isValidRomanianMobile => PhoneNumberUtils.isValidRomanianMobile(phoneNumber);
}

/// Update staff role request model
class UpdateStaffRoleRequest {
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final String? notes;

  UpdateStaffRoleRequest({
    required this.groomerRole,
    required this.clientDataPermission,
    this.notes,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'groomerRole': groomerRole.value,
      'clientDataPermission': clientDataPermission.value,
      'notes': notes,
    };
  }
}

/// Create staff request model (for future use)
class CreateStaffRequest {
  final String name;
  final String phoneNumber;
  final String? email;
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final List<String> specialties;
  final String? notes;
  final int experience;
  final Map<String, dynamic>? workingHours;

  CreateStaffRequest({
    required this.name,
    required this.phoneNumber,
    this.email,
    required this.groomerRole,
    required this.clientDataPermission,
    this.specialties = const [],
    this.notes,
    this.experience = 0,
    this.workingHours,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'groomerRole': groomerRole.value,
      'clientDataPermission': clientDataPermission.value,
      'specialties': specialties,
      'notes': notes,
      'experience': experience,
      'workingHours': workingHours,
    };
  }

  /// Validate the request
  String? validate() {
    if (name.trim().isEmpty) {
      return 'Numele este obligatoriu';
    }

    if (phoneNumber.trim().isEmpty) {
      return 'Numărul de telefon este obligatoriu';
    }

    // Basic phone validation for Romanian numbers
    final phoneRegex = RegExp(r'^\+?40[0-9]{9}$|^0[0-9]{9}$');
    if (!phoneRegex.hasMatch(phoneNumber.replaceAll(' ', '').replaceAll('-', ''))) {
      return 'Numărul de telefon nu este valid';
    }

    if (email != null && email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
      if (!emailRegex.hasMatch(email!)) {
        return 'Adresa de email nu este validă';
      }
    }

    return null; // No validation errors
  }
}

/// Update staff request model
class UpdateStaffRequest {
  final String? nickname;
  final GroomerRole? groomerRole;
  final ClientDataPermission? clientDataPermission;
  final String? notes;
  final bool? isActive;

  UpdateStaffRequest({
    this.nickname,
    this.groomerRole,
    this.clientDataPermission,
    this.notes,
    this.isActive,
  });

  /// Create from input for nickname update
  factory UpdateStaffRequest.updateNickname({
    required String? nickname,
  }) {
    return UpdateStaffRequest(
      nickname: nickname,
    );
  }

  /// Create from input for role update
  factory UpdateStaffRequest.updateRole({
    required GroomerRole groomerRole,
    required ClientDataPermission clientDataPermission,
  }) {
    return UpdateStaffRequest(
      groomerRole: groomerRole,
      clientDataPermission: clientDataPermission,
    );
  }

  /// Convert to JSON (only include non-null fields)
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};

    if (nickname != null) {
      json['nickname'] = nickname;
    }
    if (groomerRole != null) {
      json['groomerRole'] = groomerRole!.value;
    }
    if (clientDataPermission != null) {
      json['clientDataPermission'] = clientDataPermission!.value;
    }
    if (notes != null) {
      json['notes'] = notes;
    }
    if (isActive != null) {
      json['isActive'] = isActive;
    }

    return json;
  }
}

/// Service for managing staff members in salons
class StaffService {
  /// Get all staff members for the current salon
  static Future<ApiResponse<StaffListResponse>> getStaff(
    String salonId, {
    bool activeOnly = true,
    String? search,
  }) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🔍 Getting staff for salon: $salonId');
      }

      debugPrint(' 1  Active only: $activeOnly');
      debugPrint('   Search: $search');

      final queryParams = <String, String>{};

      if (!activeOnly) queryParams['activeOnly'] = 'false';
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      debugPrint(' 2  Active only: $activeOnly');
      debugPrint('   Search: $search');

      debugPrint(' 3  Active only: $queryParams');
      final response = await ApiService.get<StaffListResponse>(
        '/api/salons/$salonId/staff',
        queryParams: queryParams.isNotEmpty ? queryParams : null,
        fromJson: (data) {
          return StaffListResponse.fromJson(data);
        },
      );

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error loading staff: $e');
      }
      return ApiResponse<StaffListResponse>.error('Failed to load staff: $e');
    }
  }

  /// Add a new staff member to the salon
  static Future<ApiResponse<StaffResponse>> addStaff(
    String salonId,
    AddStaffRequest request,
  ) async {
    try {
      // Validate request
      final validationError = request.validate();
      if (validationError != null) {
        return ApiResponse<StaffResponse>.error(validationError);
      }

      if (ApiConfig.enableLogging) {
        debugPrint('➕ Adding staff member to salon: $salonId');
        debugPrint('📱 Phone: ${request.phoneNumber}');
        debugPrint('👤 Role: ${request.groomerRole.displayName}');
        debugPrint('📝 Request JSON: ${request.toJson()}');
      }

      final response = await ApiService.post<StaffResponse>(
        '/api/salons/$salonId/staff',
        body: request.toJson(),
        fromJson: (data) {
          // Check if response is an invitation response
          if (data is Map<String, dynamic> && 
              (data.containsKey('invitationId') || data.containsKey('type'))) {
            if (ApiConfig.enableLogging) {
              debugPrint('✅ Successfully sent invitation to: ${request.phoneNumber}');
              debugPrint('📊 Invitation ID: ${data['invitationId']}');
              debugPrint('📝 Message: ${data['message']}');
            }
            
            // Create a placeholder StaffResponse for the invited user
            return StaffResponse(
              id: data['invitationId'] ?? '',
              name: '',
              nickname: request.nickname,
              phone: request.phoneNumber,
              email: '',
              groomerRole: request.groomerRole,
              clientDataPermission: request.clientDataPermission,
              isActive: false,
              joinedAt: DateTime.now(),
              isPending: true, // Mark as pending invitation
            );
          }
          
          // Handle regular staff response
          if (data is List && data.isNotEmpty) {
            // Backend returned an array, take the first item
            return StaffResponse.fromJson(data.first);
          } else if (data is Map<String, dynamic>) {
            // Backend returned a single object
            return StaffResponse.fromJson(data);
          } else {
            throw Exception('Unexpected response format: ${data.runtimeType}');
          }
        },
      );

      if (ApiConfig.enableLogging) {
        if (response.success) {
          debugPrint('✅ Successfully added staff member: ${response.data?.name ?? "Unknown"}');
          debugPrint('📊 Staff member ID: ${response.data?.id}');
          debugPrint('📱 Phone: ${response.data?.phone}');
          debugPrint('👤 Role: ${response.data?.groomerRole.displayName}');
        } else {
          debugPrint('❌ Failed to add staff member: ${response.error}');
          debugPrint('🔍 Request payload was: ${request.toJson()}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error adding staff member: $e');
      }
      return ApiResponse<StaffResponse>.error('Failed to add staff member: $e');
    }
  }

  /// Update staff member information (nickname, role, permissions, etc.)
  static Future<ApiResponse<StaffResponse>> updateStaff(
    String salonId,
    String userId,
    UpdateStaffRequest request,
  ) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🔄 Updating staff member: $userId in salon: $salonId');
        if (request.nickname != null) {
          debugPrint('📝 New nickname: ${request.nickname}');
        }
        if (request.groomerRole != null) {
          debugPrint('👤 New role: ${request.groomerRole!.displayName}');
        }
        if (request.clientDataPermission != null) {
          debugPrint('🔐 New permissions: ${request.clientDataPermission!.displayName}');
        }
        debugPrint('📝 Request JSON: ${request.toJson()}');
      }

      final response = await ApiService.put<StaffResponse>(
        '/api/salons/$salonId/staff/$userId',
        body: request.toJson(),
        fromJson: (data) => StaffResponse.fromJson(data),
      );

      if (ApiConfig.enableLogging) {
        if (response.success) {
          debugPrint('✅ Successfully updated staff member: ${response.data?.name}');
          if (request.nickname != null) {
            debugPrint('📝 New display name: ${response.data?.displayName}');
          }
        } else {
          debugPrint('❌ Failed to update staff member: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating staff member: $e');
      }
      return ApiResponse<StaffResponse>.error('Failed to update staff member: $e');
    }
  }

  /// Update staff member role and permissions (legacy method for backward compatibility)
  static Future<ApiResponse<StaffResponse>> updateStaffRole(
    String salonId,
    String userId,
    UpdateStaffRoleRequest request,
  ) async {
    // Convert to new UpdateStaffRequest format
    final updateRequest = UpdateStaffRequest.updateRole(
      groomerRole: request.groomerRole,
      clientDataPermission: request.clientDataPermission,
    );

    return updateStaff(salonId, userId, updateRequest);
  }

  /// Toggle staff member active status
  static Future<ApiResponse<StaffResponse>> toggleStaffStatus(
    String salonId,
    String staffId,
  ) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🔄 Toggling staff status for user: $staffId in salon: $salonId');
      }

      final response = await ApiService.patch<StaffResponse>(
        '/api/salons/$salonId/staff/$staffId/toggle-status',
        fromJson: (data) => StaffResponse.fromJson(data),
      );

      if (ApiConfig.enableLogging) {
        if (response.success) {
          debugPrint('✅ Successfully toggled staff status for: ${response.data?.name}');
          debugPrint('📊 New status: ${response.data?.isActive == true ? "Active" : "Inactive"}');
        } else {
          debugPrint('❌ Failed to toggle staff status: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error toggling staff status: $e');
      }
      return ApiResponse<StaffResponse>.error('Failed to toggle staff status: $e');
    }
  }

  /// Remove staff member from salon
  static Future<ApiResponse<void>> removeStaff(
    String salonId,
    String staffId,
  ) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🗑️ Removing staff member: $staffId from salon: $salonId');
      }

      final response = await ApiService.delete<void>(
        '/api/salons/$salonId/staff/$staffId',
      );

      if (ApiConfig.enableLogging) {
        if (response.success) {
          debugPrint('✅ Successfully removed staff member');
        } else {
          debugPrint('❌ Failed to remove staff member: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error removing staff member: $e');
      }
      return ApiResponse<void>.error('Failed to remove staff member: $e');
    }
  }

  /// Get staff members for the current user's salon (convenience method)
  static Future<ApiResponse<StaffListResponse>> getCurrentSalonStaff({
    bool activeOnly = true,
    String? search,
  }) async {
    debugPrint('getCurrentSalonStaff: $activeOnly, $search');
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffListResponse>.error('No salon selected');
      }
      debugPrint('getCurrentSalonStaff: $salonId');

      return await getStaff(salonId, activeOnly: activeOnly, search: search);
    } catch (e) {
      return ApiResponse<StaffListResponse>.error('Failed to load current salon staff: $e');
    }
  }

  /// Add staff member to current user's salon (convenience method)
  static Future<ApiResponse<StaffResponse>> addStaffToCurrentSalon(
    AddStaffRequest request,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffResponse>.error('No salon selected');
      }

      return await addStaff(salonId, request);
    } catch (e) {
      return ApiResponse<StaffResponse>.error('Failed to add staff to current salon: $e');
    }
  }

  /// Update staff member in current user's salon (convenience method)
  static Future<ApiResponse<StaffResponse>> updateStaffInCurrentSalon(
    String userId,
    UpdateStaffRequest request,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffResponse>.error('No salon selected');
      }

      return await updateStaff(salonId, userId, request);
    } catch (e) {
      return ApiResponse<StaffResponse>.error('Failed to update staff member in current salon: $e');
    }
  }

  /// Update staff role in current user's salon (convenience method - legacy)
  static Future<ApiResponse<StaffResponse>> updateStaffRoleInCurrentSalon(
    String userId,
    UpdateStaffRoleRequest request,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffResponse>.error('No salon selected');
      }

      return await updateStaffRole(salonId, userId, request);
    } catch (e) {
      return ApiResponse<StaffResponse>.error('Failed to update staff role in current salon: $e');
    }
  }

  /// Update staff nickname in current user's salon (convenience method)
  static Future<ApiResponse<StaffResponse>> updateStaffNicknameInCurrentSalon(
    String userId,
    String? nickname,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffResponse>.error('No salon selected');
      }

      final request = UpdateStaffRequest.updateNickname(nickname: nickname);
      return await updateStaff(salonId, userId, request);
    } catch (e) {
      return ApiResponse<StaffResponse>.error('Failed to update staff nickname in current salon: $e');
    }
  }

  /// Toggle staff status in current user's salon (convenience method)
  static Future<ApiResponse<StaffResponse>> toggleStaffStatusInCurrentSalon(
    String staffId,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<StaffResponse>.error('No salon selected');
      }

      return await toggleStaffStatus(salonId, staffId);
    } catch (e) {
      return ApiResponse<StaffResponse>.error('Failed to toggle staff status in current salon: $e');
    }
  }

  /// Remove staff from current user's salon (convenience method)
  static Future<ApiResponse<void>> removeStaffFromCurrentSalon(
    String staffId,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      return await removeStaff(salonId, staffId);
    } catch (e) {
      return ApiResponse<void>.error('Failed to remove staff from current salon: $e');
    }
  }

  /// Resend invitation to a pending staff member
  static Future<ApiResponse<void>> resendInvitation(
    String salonId,
    String invitationId,
  ) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('📤 Resending invitation: $invitationId for salon: $salonId');
      }

      final response = await ApiService.post<void>(
        '/api/invitations/$invitationId/resend',
      );

      if (ApiConfig.enableLogging) {
        if (response.success) {
          debugPrint('✅ Successfully resent invitation');
        } else {
          debugPrint('❌ Failed to resend invitation: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error resending invitation: $e');
      }
      return ApiResponse<void>.error('Failed to resend invitation: $e');
    }
  }

  /// Cancel a pending staff invitation
  static Future<ApiResponse<void>> cancelInvitation(
    String salonId,
    String invitationId,
  ) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🗑️ Canceling invitation: $invitationId for salon: $salonId');
      }

      final response = await ApiService.delete<void>(
        '/api/invitations/$invitationId',
      );

      if (ApiConfig.enableLogging) {
        if (response.success) {
          debugPrint('✅ Successfully canceled invitation');
        } else {
          debugPrint('❌ Failed to cancel invitation: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error canceling invitation: $e');
      }
      return ApiResponse<void>.error('Failed to cancel invitation: $e');
    }
  }

  /// Resend invitation for current user's salon (convenience method)
  static Future<ApiResponse<void>> resendInvitationInCurrentSalon(
    String invitationId,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      return await resendInvitation(salonId, invitationId);
    } catch (e) {
      return ApiResponse<void>.error('Failed to resend invitation in current salon: $e');
    }
  }

  /// Cancel invitation for current user's salon (convenience method)
  static Future<ApiResponse<void>> cancelInvitationInCurrentSalon(
    String invitationId,
  ) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      return await cancelInvitation(salonId, invitationId);
    } catch (e) {
      return ApiResponse<void>.error('Failed to cancel invitation in current salon: $e');
    }
  }

  /// Helper method to check if debug mode is enabled
  static bool get debugMode => ApiConfig.enableLogging;
}

/// Predefined staff colors for calendar consistency
class StaffColors {
  static const List<Color> availableColors = [
    Color(0xFF2C4B34), // Forest Green (default)
    Color(0xFFA85D39), // Logo Brown
    Color(0xFF6B73FF), // Blue
    Color(0xFF9C27B0), // Purple
    Color(0xFFFF6B6B), // Red
    Color(0xFF4ECDC4), // Teal
    Color(0xFFFFE66D), // Yellow
    Color(0xFFFF8A65), // Orange
    Color(0xFF81C784), // Light Green
    Color(0xFF64B5F6), // Light Blue
  ];

  static Color getColorForIndex(int index) {
    return availableColors[index % availableColors.length];
  }

  static int getIndexForColor(Color color) {
    return availableColors.indexOf(color);
  }

  /// Get color for staff member based on their ID
  static Color getColorForStaff(String staffId) {
    // Use hash of staff ID to get consistent color
    final hash = staffId.hashCode.abs();
    return getColorForIndex(hash);
  }
}