import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/working_hours_settings.dart';
import '../services/api_service.dart';
import '../config/api_config.dart';
import '../utils/romanian_holidays.dart';
import 'auth/auth_service.dart';

/// Service for managing salon working hours settings
class WorkingHoursService {
  /// Get working hours settings for the current salon
  static Future<ApiResponse<WorkingHoursSettings>> getWorkingHours() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<WorkingHoursSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Getting working hours for salon: $salonId');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/working-hours',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final settings = WorkingHoursSettings.fromJson(response.data!);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Working hours retrieved successfully');
        }

        return ApiResponse.success(settings);
      }

      return ApiResponse<WorkingHoursSettings>.error(response.error ?? 'Failed to get working hours');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting working hours: $e');
      }
      return ApiResponse<WorkingHoursSettings>.error('Failed to get working hours: $e');
    }
  }

  /// Update working hours settings for the current salon
  /// Only users with CHIEF_GROOMER role can update these settings
  static Future<ApiResponse<WorkingHoursSettings>> updateWorkingHours(UpdateWorkingHoursRequest request) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<WorkingHoursSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('⏰ Updating working hours for salon: $salonId');
        debugPrint('   Weekly schedule: ${request.weeklySchedule.keys.join(', ')}');
        debugPrint('   Holidays count: ${request.holidays.length}');
        debugPrint('   Custom closures count: ${request.customClosures.length}');
      }

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/salons/$salonId/working-hours',
        body: request.toJson(),
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final updatedSettings = WorkingHoursSettings.fromJson(response.data!);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Working hours updated successfully');
        }

        return ApiResponse.success(updatedSettings);
      }

      return ApiResponse<WorkingHoursSettings>.error(response.error ?? 'Failed to update working hours');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating working hours: $e');
      }
      return ApiResponse<WorkingHoursSettings>.error('Failed to update working hours: $e');
    }
  }

  /// Validate working hours settings request
  static Future<ApiResponse<WorkingHoursSettings>> updateDaySchedule({
    required String dayOfWeek,
    required DaySchedule schedule,
  }) async {
    try {
      // Get current settings
      final currentResponse = await getWorkingHours();
      if (!currentResponse.success || currentResponse.data == null) {
        return ApiResponse<WorkingHoursSettings>.error('Failed to get current working hours');
      }

      final currentSettings = currentResponse.data!;
      final updatedSchedule = Map<String, DaySchedule>.from(currentSettings.weeklySchedule);
      updatedSchedule[dayOfWeek.toLowerCase()] = schedule;

      final request = UpdateWorkingHoursRequest(
        weeklySchedule: updatedSchedule,
        holidays: currentSettings.holidays,
        customClosures: currentSettings.customClosures,
      );

      return await updateWorkingHours(request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating day schedule: $e');
      }
      return ApiResponse<WorkingHoursSettings>.error('Failed to update day schedule: $e');
    }
  }

  /// Add or update a holiday
  static Future<ApiResponse<WorkingHoursSettings>> updateHoliday(Holiday holiday) async {
    try {
      // Get current settings
      final currentResponse = await getWorkingHours();
      if (!currentResponse.success || currentResponse.data == null) {
        return ApiResponse<WorkingHoursSettings>.error('Failed to get current working hours');
      }

      final currentSettings = currentResponse.data!;
      final updatedHolidays = List<Holiday>.from(currentSettings.holidays);

      // Remove existing holiday for the same date if any
      updatedHolidays.removeWhere((h) =>
        h.date.year == holiday.date.year &&
        h.date.month == holiday.date.month &&
        h.date.day == holiday.date.day
      );

      // Add the new/updated holiday
      updatedHolidays.add(holiday);

      final request = UpdateWorkingHoursRequest(
        weeklySchedule: currentSettings.weeklySchedule,
        holidays: updatedHolidays,
        customClosures: currentSettings.customClosures,
      );

      return await updateWorkingHours(request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating holiday: $e');
      }
      return ApiResponse<WorkingHoursSettings>.error('Failed to update holiday: $e');
    }
  }

  /// Add a custom closure
  static Future<ApiResponse<WorkingHoursSettings>> addCustomClosure(CustomClosure closure) async {
    try {
      // Get current settings
      final currentResponse = await getWorkingHours();
      if (!currentResponse.success || currentResponse.data == null) {
        return ApiResponse<WorkingHoursSettings>.error('Failed to get current working hours');
      }

      final currentSettings = currentResponse.data!;
      final updatedClosures = List<CustomClosure>.from(currentSettings.customClosures);

      // Remove existing closure for the same date if any
      updatedClosures.removeWhere((c) =>
        c.date.year == closure.date.year &&
        c.date.month == closure.date.month &&
        c.date.day == closure.date.day
      );

      // Add the new closure
      updatedClosures.add(closure);

      final request = UpdateWorkingHoursRequest(
        weeklySchedule: currentSettings.weeklySchedule,
        holidays: currentSettings.holidays,
        customClosures: updatedClosures,
      );

      return await updateWorkingHours(request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error adding custom closure: $e');
      }
      return ApiResponse<WorkingHoursSettings>.error('Failed to add custom closure: $e');
    }
  }

  /// Remove a custom closure
  static Future<ApiResponse<WorkingHoursSettings>> removeCustomClosure(DateTime date) async {
    try {
      // Get current settings
      final currentResponse = await getWorkingHours();
      if (!currentResponse.success || currentResponse.data == null) {
        return ApiResponse<WorkingHoursSettings>.error('Failed to get current working hours');
      }

      final currentSettings = currentResponse.data!;
      final updatedClosures = currentSettings.customClosures.where((c) =>
        !(c.date.year == date.year &&
          c.date.month == date.month &&
          c.date.day == date.day)
      ).toList();

      final request = UpdateWorkingHoursRequest(
        weeklySchedule: currentSettings.weeklySchedule,
        holidays: currentSettings.holidays,
        customClosures: updatedClosures,
      );

      return await updateWorkingHours(request);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error removing custom closure: $e');
      }
      return ApiResponse<WorkingHoursSettings>.error('Failed to remove custom closure: $e');
    }
  }

  /// Check if current user can modify working hours settings
  /// This should be used in UI to show/hide edit controls
  static Future<bool> canModifyWorkingHours() async {
    try {
      // This would typically check the user's role in the current salon
      // For now, we'll assume the permission check is done at the UI level
      // using PermissionGuard with requireManagementAccess
      final salonId = await AuthService.getCurrentSalonId();
      return salonId != null;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking working hours permissions: $e');
      }
      return false;
    }
  }

  /// Check if salon is open at a specific date and time
  static Future<bool> isSalonOpen(DateTime dateTime) async {
    try {
      final response = await getWorkingHours();
      if (!response.success || response.data == null) {
        return false; // Default to closed if can't get settings
      }

      final settings = response.data!;
      return settings.isOpenOnDate(dateTime);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking if salon is open: $e');
      }
      return false;
    }
  }

  /// Get next available working day
  static Future<DateTime?> getNextWorkingDay(DateTime fromDate) async {
    try {
      final response = await getWorkingHours();
      if (!response.success || response.data == null) {
        return null;
      }

      final settings = response.data!;
      DateTime checkDate = fromDate.add(const Duration(days: 1));

      // Check up to 30 days ahead
      for (int i = 0; i < 30; i++) {
        if (settings.isOpenOnDate(checkDate)) {
          return checkDate;
        }
        checkDate = checkDate.add(const Duration(days: 1));
      }

      return null; // No working day found in next 30 days
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting next working day: $e');
      }
      return null;
    }
  }
}
