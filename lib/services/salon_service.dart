import 'package:flutter/foundation.dart';
import '../models/salon.dart';

import '../models/user_salon_association.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Service for managing salon operations
class SalonService {
  /// Create a new salon
  static Future<ApiResponse<CreateSalonResponse>> createSalon(CreateSalonRequest request) async {
    try {
      // Validate request first
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return ApiResponse<CreateSalonResponse>.error(validationErrors.first);
      }

      if (ApiConfig.enableLogging) {
        debugPrint('🏢 Creating salon: ${request.name}');
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/salons',
        body: request.toJson(),
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final createResponse = CreateSalonResponse.fromJson(response.data!);

        // Update stored user data with new salon information
        await _updateUserSalonData(createResponse.salon);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Salon created successfully: ${createResponse.salon.id}');
          debugPrint('✅ Current salon ID updated to: ${createResponse.salon.id}');
        }

        return ApiResponse.success(createResponse);
      }

      return ApiResponse<CreateSalonResponse>.error(response.error ?? 'Failed to create salon');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error creating salon: $e');
      }
      return ApiResponse<CreateSalonResponse>.error('Failed to create salon: $e');
    }
  }

  /// Get salon by ID
  static Future<ApiResponse<Salon>> getSalon(String salonId) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final salon = Salon.fromJson(response.data!);
        return ApiResponse.success(salon);
      }

      return ApiResponse<Salon>.error(response.error ?? 'Failed to get salon');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting salon: $e');
      }
      return ApiResponse<Salon>.error('Failed to get salon: $e');
    }
  }

  /// Get current user's salon
  static Future<ApiResponse<Salon?>> getCurrentUserSalon() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.success(null);
      }

      return await getSalon(salonId);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting current user salon: $e');
      }
      return ApiResponse<Salon?>.error('Failed to get current salon: $e');
    }
  }

  /// Check if user needs onboarding (no salon association and has pending invitations)
  static Future<ApiResponse<OnboardingStatus>> checkOnboardingStatus() async {
    try {
      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        return ApiResponse<OnboardingStatus>.error('No user ID found');
      }

      debugPrint('🔄 GET /api/user/$userId/onboarding-status');
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/user/$userId/onboarding-status',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );
      debugPrint('   Response: ${response.data}');

      if (response.success && response.data != null) {
        final status = OnboardingStatus.fromJson(response.data!);
        return ApiResponse.success(status);
      }

      return ApiResponse<OnboardingStatus>.error(response.error ?? 'Failed to check onboarding status');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking onboarding status: $e');
      }
      return ApiResponse<OnboardingStatus>.error('Failed to check onboarding status: $e');
    }
  }

  /// Get all salons that the current user belongs to
  static Future<ApiResponse<List<UserSalonAssociation>>> getUserSalons() async {
    try {
      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        return ApiResponse<List<UserSalonAssociation>>.error('No user ID found');
      }

      final response = await ApiService.get<List<dynamic>>(
        '/api/user/$userId/salons',
        fromJson: (data) => List<dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final associations = response.data!
            .map((json) => UserSalonAssociation.fromJson(Map<String, dynamic>.from(json)))
            .toList();
        return ApiResponse.success(associations);
      }

      return ApiResponse<List<UserSalonAssociation>>.error(response.error ?? 'Failed to get user salons');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting user salons: $e');
      }
      return ApiResponse<List<UserSalonAssociation>>.error('Failed to get user salons: $e');
    }
  }

  /// Switch the user's active salon
  static Future<ApiResponse<void>> switchActiveSalon(String salonId) async {
    try {
      debugPrint('🔄 SalonService: Initiating salon switch to: $salonId');

      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        debugPrint('❌ SalonService: No user ID found for salon switch');
        return ApiResponse<void>.error('No user ID found');
      }

      debugPrint('🔄 SalonService: Making API request to switch salon...');
      final response = await ApiService.post<void>(
        '/api/user/$userId/switch-salon',
        body: {'salonId': salonId},
      );

      if (response.success) {
        debugPrint('✅ SalonService: API request successful, updating local salon ID...');

        // Update stored salon ID locally
        await AuthService.updateCurrentSalonId(salonId);

        debugPrint('✅ SalonService: Successfully switched to salon: $salonId');
        debugPrint('📊 SalonService: Salon switch completed - providers will be notified to clear cached data');

        return ApiResponse<void>.success(null);
      }

      debugPrint('❌ SalonService: API request failed: ${response.error}');
      return ApiResponse<void>.error(response.error ?? 'Failed to switch salon');
    } catch (e) {
      debugPrint('❌ SalonService: Error switching salon: $e');
      return ApiResponse<void>.error('Failed to switch salon: $e');
    }
  }

  /// Update an existing salon
  static Future<ApiResponse<Salon>> updateSalon(
      String salonId, CreateSalonRequest request) async {
    try {
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return ApiResponse<Salon>.error(validationErrors.first);
      }

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/salons/' + salonId,
        body: request.toJson(),
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final salon = Salon.fromJson(response.data!);
        return ApiResponse.success(salon);
      }

      return ApiResponse<Salon>.error(response.error ?? 'Failed to update salon');
    } catch (e) {
      return ApiResponse<Salon>.error('Failed to update salon: $e');
    }
  }

  /// Delete a salon by ID
  static Future<ApiResponse<bool>> deleteSalon(String salonId) async {
    try {
      debugPrint('🗑️ SalonService: Deleting salon: $salonId');
      final response = await ApiService.delete('/api/salons/' + salonId);
      if (response.success) {
        debugPrint('✅ SalonService: Salon deletion API call successful');

        // Check if the deleted salon was the current active salon
        final currentSalonId = await AuthService.getCurrentSalonId();
        debugPrint('🔍 SalonService: Current salon ID: $currentSalonId, Deleted salon ID: $salonId');

        if (currentSalonId == salonId) {
          debugPrint('🔄 SalonService: Deleted salon was current active salon, handling salon switch...');
          await _handleCurrentSalonDeletion(deletedSalonId: salonId);
        } else {
          debugPrint('ℹ️ SalonService: Deleted salon was not current active salon, no switch needed');
        }
        return ApiResponse.success(true);
      }
      return ApiResponse<bool>.error(response.error ?? 'Failed to delete salon');
    } catch (e) {
      return ApiResponse<bool>.error('Failed to delete salon: $e');
    }
  }

  /// Handle deletion of the current active salon
  static Future<void> _handleCurrentSalonDeletion({required String deletedSalonId}) async {
    try {
      debugPrint('🔄 SalonService: Handling current salon deletion for salon: $deletedSalonId');

      // Add a small delay to ensure backend has processed the deletion
      await Future.delayed(const Duration(milliseconds: 500));

      // Get user's remaining salons with retry logic
      final salonsResponse = await _getUserSalonsWithRetry(excludeSalonId: deletedSalonId);
      if (salonsResponse.success && salonsResponse.data != null) {
        final remainingSalons = salonsResponse.data!;

        debugPrint('🔍 SalonService: Found ${remainingSalons.length} remaining salons');

        if (remainingSalons.isNotEmpty) {
          // Switch to the first available salon
          final newSalonId = remainingSalons.first.salonId;
          debugPrint('🔄 SalonService: Switching to remaining salon: $newSalonId');
          await AuthService.updateCurrentSalonId(newSalonId);
          debugPrint('✅ SalonService: Successfully switched to salon: $newSalonId');
        } else {
          // No salons left, clear current salon ID
          debugPrint('🔄 SalonService: No remaining salons, clearing current salon ID');
          await AuthService.updateCurrentSalonId(null);
          debugPrint('✅ SalonService: Current salon ID cleared - user will see onboarding');
        }
      } else {
        // Error getting salons, clear current salon ID to be safe
        debugPrint('⚠️ SalonService: Error getting remaining salons, clearing current salon ID');
        await AuthService.updateCurrentSalonId(null);
      }
    } catch (e) {
      debugPrint('❌ SalonService: Error handling current salon deletion: $e');
      // Clear current salon ID to prevent issues
      await AuthService.updateCurrentSalonId(null);
    }
  }

  /// Get user salons with retry logic to handle backend delays
  static Future<ApiResponse<List<UserSalonAssociation>>> _getUserSalonsWithRetry({String? excludeSalonId}) async {
    const maxRetries = 3;
    const retryDelay = Duration(milliseconds: 1000);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      debugPrint('🔄 SalonService: Fetching user salons (attempt $attempt/$maxRetries)');

      final response = await getUserSalons();
      if (response.success && response.data != null) {
        var salons = response.data!;

        // Filter out the deleted salon if specified
        if (excludeSalonId != null) {
          final originalCount = salons.length;
          salons = salons.where((salon) => salon.salonId != excludeSalonId).toList();
          debugPrint('🔍 SalonService: Filtered out deleted salon. Original: $originalCount, Remaining: ${salons.length}');
        }

        debugPrint('✅ SalonService: Successfully fetched ${salons.length} salons');
        return ApiResponse.success(salons);
      }

      if (attempt < maxRetries) {
        debugPrint('⏳ SalonService: Retrying in ${retryDelay.inMilliseconds}ms...');
        await Future.delayed(retryDelay);
      }
    }

    debugPrint('❌ SalonService: Failed to fetch salons after $maxRetries attempts');
    return ApiResponse.error('Failed to fetch user salons after multiple attempts');
  }

  /// Update stored user data after salon creation
  static Future<void> _updateUserSalonData(Salon salon) async {
    try {
      // Update the stored salon ID to the newly created salon
      await AuthService.updateCurrentSalonId(salon.id);

      if (ApiConfig.enableLogging) {
        debugPrint('✅ Updated user salon data after creation: ${salon.id}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating stored user data: $e');
      }
    }
  }
}

/// Onboarding status for groomers
class OnboardingStatus {
  final bool needsOnboarding;
  final bool hasSalonAssociation;
  final bool hasPendingInvitations;
  final int pendingInvitationCount;
  final String? currentSalonId;
  final String? currentSalonName;

  OnboardingStatus({
    required this.needsOnboarding,
    required this.hasSalonAssociation,
    required this.hasPendingInvitations,
    required this.pendingInvitationCount,
    this.currentSalonId,
    this.currentSalonName,
  });

  /// Create from JSON
  factory OnboardingStatus.fromJson(Map<String, dynamic> json) {
    return OnboardingStatus(
      needsOnboarding: json['needsOnboarding'] ?? false,
      hasSalonAssociation: json['hasSalonAssociation'] ?? false,
      hasPendingInvitations: json['hasPendingInvitations'] ?? false,
      pendingInvitationCount: json['pendingInvitationCount'] ?? 0,
      currentSalonId: json['currentSalonId'],
      currentSalonName: json['currentSalonName'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'needsOnboarding': needsOnboarding,
      'hasSalonAssociation': hasSalonAssociation,
      'hasPendingInvitations': hasPendingInvitations,
      'pendingInvitationCount': pendingInvitationCount,
      'currentSalonId': currentSalonId,
      'currentSalonName': currentSalonName,
    };
  }

  /// Check if user should see onboarding screen
  bool get shouldShowOnboarding => needsOnboarding && !hasSalonAssociation;

  /// Get onboarding message
  String get onboardingMessage {
    if (hasPendingInvitations && pendingInvitationCount > 0) {
      return 'Ai $pendingInvitationCount invitație${pendingInvitationCount > 1 ? 'i' : ''} în așteptare. Poți să le accepți sau să îți creezi propriul salon.';
    } else {
      return 'Pentru a începe să lucrezi ca groomer, trebuie să îți creezi un salon sau să accepți o invitație de la un salon existent.';
    }
  }
}
