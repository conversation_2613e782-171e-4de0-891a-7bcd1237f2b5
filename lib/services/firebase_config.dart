import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:io' show Platform;
import '../config/environment.dart';

class FirebaseConfig {
  static bool isInitialized = false;

  static Future<bool> initializeFirebase() async {
    if (isInitialized) {
      return true;
    }

    try {
      FirebaseOptions? options;

      // Get environment-specific Firebase configuration
      final firebaseConfig = EnvironmentConfig.firebaseConfig;
      final bundleId = EnvironmentConfig.bundleId;

      if (kIsWeb) {
        // Web configuration
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['appId']!, // You'll need to get web app ID from Firebase Console
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
        );
      } else if (Platform.isIOS) {
        // iOS configuration - Using environment-specific settings
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
          iosBundleId: bundleId,
        );
      } else if (Platform.isAndroid) {
        // Android configuration - Using environment-specific settings
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['appId']!, // You'll need to get Android app ID from Firebase Console
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
        );
      }

      if (options != null) {
        debugPrint('🔧 Initializing Firebase with environment-specific options...');
        debugPrint('🌍 Environment: ${EnvironmentConfig.currentEnvironment.name}');
        debugPrint('📦 Bundle ID: ${EnvironmentConfig.bundleId}');
        await Firebase.initializeApp(options: options);
      } else {
        debugPrint('🔧 Using default Firebase initialization...');
        await Firebase.initializeApp();
      }
      debugPrint('✅ Firebase initialized successfully');
      debugPrint('🔍 FIREBASE CONFIG VERIFICATION:');
      debugPrint('- Environment: ${EnvironmentConfig.currentEnvironment.name}');
      debugPrint('- Project ID: ${Firebase.app().options.projectId}');
      debugPrint('- App ID: ${Firebase.app().options.appId}');
      debugPrint('- API Key: ${Firebase.app().options.apiKey}');
      debugPrint('- Auth Domain: ${Firebase.app().options.authDomain}');
      if (!kIsWeb && Platform.isIOS) {
        debugPrint('- iOS Bundle ID: ${Firebase.app().options.iosBundleId}');
      }
      isInitialized = true;
      return true;
    } catch (e) {
      debugPrint('Error initializing Firebase: $e');
      // Instead of silently falling back to mock mode, we'll show an error
      // This ensures the app doesn't proceed with mock authentication
      debugPrint('Firebase initialization failed - real authentication is required');
      isInitialized = false;
      return false;
    }
  }
}
