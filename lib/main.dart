import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'screens/clients/client_search_screen.dart';
import 'screens/clients/client_details_screen.dart';
import 'screens/auth/phone_login_screen.dart';
import 'models/client.dart';
import 'config/api_config.dart';
import 'config/environment.dart';
import 'utils/network_test.dart';
import 'providers/calendar_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/locale_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/role_provider.dart';
import 'providers/client_provider.dart';
import 'services/auth/auth_service.dart';
import 'services/firebase_config.dart';
import 'services/notification_handler.dart';
import 'services/analytics_service.dart';
import 'services/screen_time_service.dart';
import 'services/retention_analytics_service.dart';
import 'services/error_handling_service.dart';
import 'services/feature_toggle_service.dart';
import 'widgets/auth_wrapper.dart';

// Global function to handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await FirebaseConfig.initializeFirebase();
  debugPrint('🔔 Background message: ${message.notification?.title}');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize environment configuration
  if (EnvironmentConfig.isDebugMode) {
    EnvironmentConfig.printConfig();
  }

  // Initialize Firebase
  await FirebaseConfig.initializeFirebase();

  // Initialize error handling (includes Crashlytics setup)
  ErrorHandlingService.initialize();

  // Initialize Crashlytics for uncaught async errors
  if (EnvironmentConfig.isProduction) {
    // Pass all uncaught asynchronous errors to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  // Initialize Firebase Messaging
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Initialize authentication
  await AuthService.initializeAuth();

  // Initialize simple notification handler (backend handles all logic)
  await NotificationHandler.initialize();


  // Initialize analytics
  await AnalyticsService.initialize();

  // Initialize screen time tracking
  ScreenTimeService.initialize();

  // Initialize retention analytics
  await RetentionAnalyticsService.initialize();

  // Initialize feature toggles
  await FeatureToggleService.initialize();

  // Initialize date formatting for Romanian locale
  try {
    await initializeDateFormatting('ro_RO', null);
    await initializeDateFormatting('en_US', null); // Fallback
    Intl.defaultLocale = 'ro_RO';
  } catch (e) {
    // Fallback to English locale if Romanian initialization fails
    await initializeDateFormatting('en_US', null);
    Intl.defaultLocale = 'en_US';
  }

  // Test network connectivity on startup (debug mode only)
  if (EnvironmentConfig.isDebugMode) {
    final isConnected = await NetworkTest.quickConnectivityCheck();
    if (!isConnected && EnvironmentConfig.isDevelopment) {
      // Only show warning in development environment
      debugPrint('Warning: Cannot connect to server at ${ApiConfig.baseUrl}');
    }
  }

  runApp(Phoenix(child: const MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => RoleProvider()),
        ChangeNotifierProvider(create: (_) => CalendarProvider()),
        ChangeNotifierProvider(create: (_) => ClientProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()..initializeTheme()),
        ChangeNotifierProvider(create: (_) => LocaleProvider()..initialize()),
      ],
      child: Consumer2<ThemeProvider, LocaleProvider>(
        builder: (context, themeProvider, localeProvider, child) {
          return MaterialApp(
            title: EnvironmentConfig.appName,
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            navigatorKey: NavigationService.navigatorKey,

        // Add localization support
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ro'),
          Locale('en'),
        ],
        locale: localeProvider.locale,

        home: const AuthWrapper(), // Use authentication wrapper to handle login flow
        onGenerateRoute: (settings) {
          if (settings.name == '/client-search') {
            final availableClients = settings.arguments as List<Client>? ?? [];
            return MaterialPageRoute(
              builder: (context) => ClientSearchScreen(availableClients: availableClients),
            );
          } else if (settings.name == '/client-details') {
            final client = settings.arguments as Client;
            return MaterialPageRoute(
              builder: (context) => ClientDetailsScreen(client: client),
            );
          } else if (settings.name == '/phone-login') {
            return MaterialPageRoute(
              builder: (context) => const PhoneLoginScreen(),
            );
          }
          return null;
        },
          );
        },
      ),
    );
  }
}
