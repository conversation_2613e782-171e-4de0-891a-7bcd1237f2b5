import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';

/// Types of notifications with different visual styles
enum NotificationType {
  success,
  warning,
  error,
  info,
  scheduleConflict,
}

/// Beautiful animated notification widget with smooth animations
class AnimatedNotification extends StatefulWidget {
  final String title;
  final String message;
  final NotificationType type;
  final Duration duration;
  final VoidCallback? onDismiss;
  final VoidCallback? onActionPressed;
  final String? actionLabel;
  final Widget? customIcon;
  final bool showCloseButton;
  final EdgeInsets? margin;

  const AnimatedNotification({
    super.key,
    required this.title,
    required this.message,
    required this.type,
    this.duration = const Duration(seconds: 4),
    this.onDismiss,
    this.onActionPressed,
    this.actionLabel,
    this.customIcon,
    this.showCloseButton = true,
    this.margin,
  });

  @override
  State<AnimatedNotification> createState() => _AnimatedNotificationState();
}

class _AnimatedNotificationState extends State<AnimatedNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startEntryAnimation();
    _scheduleAutoDismiss();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  void _startEntryAnimation() {
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
  }

  void _scheduleAutoDismiss() {
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  void _dismiss() async {
    await _fadeController.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            margin: widget.margin ?? const EdgeInsets.all(16),
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(16),
              shadowColor: _getNotificationColor().withValues(alpha: 0.3),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _getNotificationColor().withValues(alpha: 0.1),
                      _getNotificationColor().withValues(alpha: 0.05),
                    ],
                  ),
                  border: Border.all(
                    color: _getNotificationColor().withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Icon
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getNotificationColor().withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: widget.customIcon ?? _getDefaultIcon(),
                      ),
                      SizedBox(width: 16),
                      
                      // Content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              widget.title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: _getNotificationColor(),
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              widget.message,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.black87,
                                height: 1.4,
                              ),
                            ),
                            
                            // Action button
                            if (widget.onActionPressed != null && widget.actionLabel != null) ...[
                              SizedBox(height: 12),
                              TextButton(
                                onPressed: widget.onActionPressed,
                                style: TextButton.styleFrom(
                                  foregroundColor: _getNotificationColor(),
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                ),
                                child: Text(
                                  widget.actionLabel!,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      
                      // Close button
                      if (widget.showCloseButton) ...[
                        SizedBox(width: 8),
                        IconButton(
                          onPressed: _dismiss,
                          icon:  Icon(Icons.close, size: 20),
                          color: Colors.grey.shade600,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor() {
    switch (widget.type) {
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.scheduleConflict:
        return Theme.of(context).colorScheme.primary;
    }
  }

  Widget _getDefaultIcon() {
    IconData iconData;
    switch (widget.type) {
      case NotificationType.success:
        iconData = Icons.check_circle;
        break;
      case NotificationType.warning:
        iconData = Icons.warning;
        break;
      case NotificationType.error:
        iconData = Icons.error;
        break;
      case NotificationType.info:
        iconData = Icons.info;
        break;
      case NotificationType.scheduleConflict:
        iconData = Icons.schedule;
        break;
    }

    return Icon(
      iconData,
      color: _getNotificationColor(),
      size: 24,
    );
  }
}
