import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import '../../models/working_hours_settings.dart';
import '../../services/staff_validation_error_service.dart';

/// Specialized notification for schedule conflicts with business hours context
class ScheduleConflictNotification extends StatefulWidget {
  final String errorMessage;
  final WorkingHoursSettings? businessHours;
  final VoidCallback? onDismiss;
  final VoidCallback? onRetry;
  final VoidCallback? onViewBusinessHours;

  const ScheduleConflictNotification({
    super.key,
    required this.errorMessage,
    this.businessHours,
    this.onDismiss,
    this.onRetry,
    this.onViewBusinessHours,
  });

  @override
  State<ScheduleConflictNotification> createState() => _ScheduleConflictNotificationState();
}

class _ScheduleConflictNotificationState extends State<ScheduleConflictNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _bounceController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _bounceAnimation;



  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startEntryAnimation();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));
  }

  void _startEntryAnimation() {
    _fadeController.forward();
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _bounceController.forward();
      }
    });
  }

  void _dismiss() async {
    await _fadeController.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final parsedError = StaffValidationErrorService.parseValidationError(widget.errorMessage);

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _bounceAnimation,
          child: Container(
            margin: const EdgeInsets.all(16),
            child: Material(
              elevation: 12,
              borderRadius: BorderRadius.circular(20),
              shadowColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                      Colors.orange.withValues(alpha: 0.05),
                    ],
                  ),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(),
                    _buildContent(parsedError),
                    _buildActions(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.schedule,
              color: Theme.of(context).colorScheme.primary,
              size: 28,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Conflict de program',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Programul nu respectă restricțiile salonului',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _dismiss,
            icon:  Icon(Icons.close, size: 24),
            color: Colors.grey.shade600,
          ),
        ],
      ),
    );
  }

  Widget _buildContent(String parsedError) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Custom Romanian message
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange.shade700,
                  size: 20,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nu poți seta acest program',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Programul ales depășește orele de funcționare ale salonului. Te rugăm să alegi ore care se încadrează în programul de mai jos.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Always show business hours for schedule conflicts
          if (widget.businessHours != null) ...[
            SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.business,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Program salon',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  ..._buildBusinessHoursList(),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }



  List<Widget> _buildBusinessHoursList() {
    if (widget.businessHours == null) return [];

    final weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    final dayNames = {
      'monday': 'Luni',
      'tuesday': 'Marți',
      'wednesday': 'Miercuri',
      'thursday': 'Joi',
      'friday': 'Vineri',
      'saturday': 'Sâmbătă',
      'sunday': 'Duminică',
    };
    
    return weekDays.map((day) {
      final schedule = widget.businessHours!.getScheduleForDay(day);
      final dayName = dayNames[day] ?? day;
      
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 3),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: schedule?.isWorkingDay == true
              ? Colors.white.withValues(alpha: 0.7)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: schedule?.isWorkingDay == true
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Day indicator
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: schedule?.isWorkingDay == true
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.shade400,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 12),
            SizedBox(
              width: 80,
              child: Text(
                dayName,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: schedule?.isWorkingDay == true
                      ? Colors.black87
                      : Colors.grey.shade600,
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                schedule?.isWorkingDay == true
                    ? '${schedule!.startTime} - ${schedule.endTime}'
                    : 'Închis',
                style: TextStyle(
                  fontSize: 14,
                  color: schedule?.isWorkingDay == true
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.shade500,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (widget.onViewBusinessHours != null) ...[
            Expanded(
              child: OutlinedButton.icon(
                onPressed: widget.onViewBusinessHours,
                icon:  Icon(Icons.settings, size: 18),
                label: Text('Setări salon'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.primary,
                  side:  BorderSide(color: Theme.of(context).colorScheme.primary),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
          ],
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                _dismiss();
                widget.onRetry?.call();
              },
              icon:  Icon(Icons.refresh, size: 18),
              label: Text('Încearcă din nou'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
