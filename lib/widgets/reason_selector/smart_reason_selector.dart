import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';

/// Smart reason selector with predefined options and custom input
class SmartReasonSelector extends StatefulWidget {
  final String? initialReason;
  final Function(String) onReasonChanged;
  final String label;

  const SmartReasonSelector({
    Key? key,
    this.initialReason,
    required this.onReasonChanged,
    required this.label,
  }) : super(key: key);

  @override
  State<SmartReasonSelector> createState() => _SmartReasonSelectorState();
}

class _SmartReasonSelectorState extends State<SmartReasonSelector> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String? _selectedReason;
  bool _showCustomInput = false;
  final TextEditingController _customController = TextEditingController();

  final List<ReasonOption> _predefinedReasons = [
    ReasonOption(
      id: 'vacation',
      title: '<PERSON>ced<PERSON>',
      description: 'Conced<PERSON> personal',
      icon: Icons.beach_access,
      color: Colors.blue,
    ),
    ReasonOption(
      id: 'event',
      title: 'Eveniment',
      description: 'Eveniment special',
      icon: Icons.event,
      color: Colors.purple,
    ),
    ReasonOption(
      id: 'emergency',
      title: 'Urgență',
      description: 'Situație urgentă',
      icon: Icons.emergency,
      color: Colors.red,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _selectedReason = widget.initialReason;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _customController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 12),

        // Quick select chips
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: _predefinedReasons.map((reason) {
            final isSelected = _selectedReason == reason.title;
            return _buildReasonChip(reason, isSelected);
          }).toList(),
        ),

        SizedBox(height: 16),

        // Custom reason toggle
        GestureDetector(
          onTap: () {
            setState(() {
              _showCustomInput = !_showCustomInput;
              if (_showCustomInput) {
                _animationController.forward();
              } else {
                _animationController.reverse();
                _customController.clear();
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: _showCustomInput
                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                  : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _showCustomInput
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.shade300,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _showCustomInput ? Icons.edit : Icons.add,
                  color: _showCustomInput
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.shade600,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  _showCustomInput ? 'Motiv personalizat' : 'Adaugă motiv personalizat',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: _showCustomInput
                        ? Theme.of(context).colorScheme.primary
                        : Colors.grey.shade600,
                  ),
                ),
                const Spacer(),
                Icon(
                  _showCustomInput
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey.shade400,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        // Custom input field
        AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _showCustomInput ? 80 : 0,
              child: Opacity(
                opacity: _fadeAnimation.value,
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: TextFormField(
                    controller: _customController,
                    decoration: InputDecoration(
                      hintText: 'Introdu motivul închiderii...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _selectedReason = value.trim().isEmpty ? null : value;
                      });
                      if (value.trim().isNotEmpty) {
                        widget.onReasonChanged(value);
                      }
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildReasonChip(ReasonOption reason, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedReason = reason.title;
          _showCustomInput = false;
          _animationController.reverse();
          _customController.clear();
        });
        widget.onReasonChanged(reason.title);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? reason.color.withValues(alpha: 0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? reason.color
                : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: reason.color.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ] : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isSelected
                    ? reason.color
                    : reason.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                reason.icon,
                size: 14,
                color: isSelected ? Colors.white : reason.color,
              ),
            ),
            SizedBox(width: 6),
            Text(
              reason.title,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isSelected ? reason.color : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Model for predefined reason options
class ReasonOption {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const ReasonOption({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
