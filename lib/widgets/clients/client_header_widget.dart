import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/client.dart';
import '../../config/theme/app_theme.dart';
import '../../services/url_launcher_service.dart';

class ClientHeaderWidget extends StatelessWidget {
  final Client client;

  const ClientHeaderWidget({
    super.key,
    required this.client,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate available space for content with tighter constraints
          final availableHeight = constraints.maxHeight - 10; // Reduced padding account
          final buttonHeight = 48.0; // Slightly reduced button height to fit
          final spacing = 4.0; // Reduced spacing
          final profileHeight = availableHeight - buttonHeight - spacing;

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Profile section with constrained height
              SizedBox(
                height: profileHeight > 40 ? profileHeight : 40,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Profile photo
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).colorScheme.onPrimary,
                        border: Border.all(
                          color: Theme.of(context).colorScheme.onPrimary,
                          width: 1,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 19,
                        backgroundColor: Theme.of(context).colorScheme.secondary,
                        child: Text(
                          client.name.isNotEmpty ? client.name[0].toUpperCase() : '?',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    // Client info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            client.name,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            client.phone,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (client.email.isNotEmpty) ...[
                            Text(
                              client.email,
                              style: TextStyle(
                                color: AppColors.white.withValues(alpha: 0.9),
                                fontSize: 9,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: spacing),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      context,
                      icon: Icons.phone,
                      label: 'Sună',
                      onPressed: () => _makePhoneCall(context),
                    ),
                  ),
                  SizedBox(width: 6),
                  Expanded(
                    child: _buildActionButton(
                      context,
                      icon: Icons.message,
                      label: 'SMS',
                      onPressed: () => _sendSMS(context),
                    ),
                  ),
                  SizedBox(width: 6),
                  Expanded(
                    child: _buildActionButton(
                      context,
                      icon: FontAwesomeIcons.whatsapp,
                      label: 'WhatsApp',
                      onPressed: () => _openWhatsApp(context),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.white,
        foregroundColor: Theme.of(context).colorScheme.primary,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        elevation: 2,
        minimumSize: const Size(0, 48),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _makePhoneCall(BuildContext context) async {
    final success = await UrlLauncherService.makePhoneCall(client.phone);
    if (!success) {
      UrlLauncherService.showLaunchError(context, 'aplicația de telefon');
    }
  }

  void _sendSMS(BuildContext context) async {
    final success = await UrlLauncherService.sendSMS(
      client.phone,
      message: 'Bună ziua ${client.name}, ',
    );
    if (!success) {
      UrlLauncherService.showLaunchError(context, 'aplicația de mesaje');
    }
  }

  void _openWhatsApp(BuildContext context) async {
    final success = await UrlLauncherService.openWhatsApp(
      client.phone,
      message: 'Bună ziua ${client.name}, ',
    );
    if (!success) {
      UrlLauncherService.showLaunchError(context, 'WhatsApp');
    }
  }
}
