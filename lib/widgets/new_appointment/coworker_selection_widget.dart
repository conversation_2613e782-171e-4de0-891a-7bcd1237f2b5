import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';

import '../../services/staff_service.dart';
import 'appointment_form_data.dart';

class CoworkerSelectionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(String) onStaffChanged;

  const CoworkerSelectionWidget({
    super.key,
    required this.formData,
    required this.onStaffChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Membru echipă asignat',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: formData.assignedStaffId.isNotEmpty
              ? formData.assignedStaffId
              : null,
          decoration: const InputDecoration(
            labelText: 'Selectează membrul echipei',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person, color: AppColors.forestGreen),
          ),
          isExpanded: true,
          selectedItemBuilder: (context) {
            return formData.availableStaff.map((s) {
              return Text(
                s.displayName,
                overflow: TextOverflow.ellipsis,
              );
            }).toList();
          },
          items: formData.availableStaff.map((staff) {
            return DropdownMenuItem<String>(
              value: staff.id,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStaffColor(staff),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          staff.displayName,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                          maxLines: 1,
                        ),
                        Row(
                          children: [
                            Text(
                              staff.groomerRole.displayName,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            // Show full name if different from display name
                            if (staff.nickname != null && staff.nickname!.isNotEmpty && staff.nickname != staff.name) ...[
                              Text(
                                ' • ${staff.name}',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.grey.shade500,
                                  fontStyle: FontStyle.italic,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onStaffChanged(value);
            }
          },
        ),
      ],
    );
  }

  /// Get color for staff member based on their ID
  Color _getStaffColor(StaffResponse staff) {
    // Generate a consistent color based on staff ID
    final colors = [
      AppColors.forestGreen,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.indigo,
      Colors.pink,
    ];

    final index = staff.id.hashCode % colors.length;
    return colors[index.abs()];
  }
}
