import 'package:flutter/material.dart';
import '../../models/pet.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';
import '../../utils/breeds.dart';

class PetSelectionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(Pet) onPetSelected;
  final Function(String) onPetNameChanged;
  final Function(String) onPetBreedChanged;
  final VoidCallback onAddNewPet;
  final VoidCallback? onPriceUpdateNeeded;

  const PetSelectionWidget({
    super.key,
    required this.formData,
    required this.onPetSelected,
    required this.onPetNameChanged,
    required this.onPetBreedChanged,
    required this.onAddNewPet,
    this.onPriceUpdateNeeded,
  });

  @override
  Widget build(BuildContext context) {
    // Debug logging for pet selection state
    debugPrint('🐕 PetSelectionWidget: Building with state:');
    debugPrint('   - isExistingClient: ${formData.isExistingClient}');
    debugPrint('   - clientId: ${formData.clientId}');
    debugPrint('   - clientPets count: ${formData.clientPets.length}');
    debugPrint('   - isNewPet: ${formData.isNewPet}');
    debugPrint('   - petId: ${formData.petId}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPetSelectionHeader(context),
        SizedBox(height: 16),
        if (formData.isExistingClient && formData.clientId.isEmpty)
          _buildSelectClientMessage(context),
        if (formData.isExistingClient &&
            formData.clientId.isNotEmpty &&
            formData.clientPets.isNotEmpty &&
            !formData.isNewPet)
          _buildExistingPetDropdown(),
        if (formData.isExistingClient &&
            formData.clientId.isNotEmpty &&
            formData.clientPets.isNotEmpty &&
            formData.isNewPet)
          _buildNewPetFields(),
        if (formData.isExistingClient &&
            formData.clientId.isNotEmpty &&
            formData.clientPets.isEmpty)
          _buildNoPetsFoundMessage(context),
        if (!formData.isExistingClient)
          _buildNewPetFields(),
      ],
    );
  }

  Widget _buildPetSelectionHeader(BuildContext context) {
    return Row(
      children: [
        Text(
          'Animal de companie',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const Spacer(),
        if (formData.isExistingClient && formData.clientPets.isNotEmpty)
          TextButton.icon(
            onPressed: onAddNewPet,
            icon: Icon(formData.isNewPet ? Icons.pets : Icons.add, size: 16),
            label: Text(formData.isNewPet ? 'Selectează animal' : 'Animal nou'),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.secondary,
            ),
          ),
      ],
    );
  }

  Widget _buildSelectClientMessage(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Theme.of(context).colorScheme.onSurfaceVariant),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              'Selectează mai întâi un client pentru a vedea animalele acestuia.',
              style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoPetsFoundMessage(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.3),
            border: Border.all(color: Theme.of(context).colorScheme.error.withValues(alpha: 0.5)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.pets, color: Theme.of(context).colorScheme.error),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Clientul selectat nu are animale înregistrate. Adaugă un animal nou.',
                  style: TextStyle(color: Theme.of(context).colorScheme.onErrorContainer),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),
        _buildNewPetFields(),
      ],
    );
  }

  Widget _buildExistingPetDropdown() {
    debugPrint('🐕 PetSelectionWidget: Building dropdown with ${formData.clientPets.length} pets');
    for (final pet in formData.clientPets) {
      debugPrint('   - Pet: ${pet.name} (${pet.breed}) - ID: ${pet.id}');
    }

    return DropdownButtonFormField<String>(
      value: formData.isNewPet ? null : (formData.petId.isNotEmpty ? formData.petId : null),
      decoration: InputDecoration(
        labelText: 'Selectează animalul',
        border: OutlineInputBorder(),
        helperText: 'Alege din animalele existente ale clientului',
      ),
      isExpanded: true,
      items: formData.clientPets.map((pet) {
        return DropdownMenuItem<String>(
          value: pet.id,
          child: Text(
            '${pet.name} (${pet.breed})',
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          debugPrint('🐕 PetSelectionWidget: Pet selected: $value');
          final pet = formData.clientPets.firstWhere((p) => p.id == value);
          onPetSelected(pet);
        }
      },
    );
  }

  Widget _buildNewPetFields() {
    return Column(
      children: [
        TextFormField(
          decoration: InputDecoration(
            labelText: 'Nume animal *',
            border: OutlineInputBorder(),
          ),
          initialValue: formData.petName,
          onChanged: onPetNameChanged,
        ),
        SizedBox(height: 16),
        Autocomplete<String>(
          optionsBuilder: (TextEditingValue textEditingValue) {
            final suggestions = Breeds.forSpecies(formData.petSpecies);
            if (textEditingValue.text.isEmpty) {
              return suggestions;
            }
            return suggestions.where((option) =>
                option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
          },
          onSelected: (String selection) {
            onPetBreedChanged(selection);
            onPriceUpdateNeeded?.call();
          },
          fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
            // Sync the autocomplete controller with our breed value
            if (controller.text != formData.petBreed) {
              controller.text = formData.petBreed;
            }

            return TextFormField(
              controller: controller,
              focusNode: focusNode,
              onEditingComplete: onEditingComplete,
              onChanged: (value) {
                onPetBreedChanged(value);
              },
              decoration: InputDecoration(
                labelText: 'Rasă *',
                hintText: 'Începeți să tastați pentru sugestii...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
            );
          },
          optionsViewBuilder: (context, onSelected, options) {
            return Align(
              alignment: Alignment.topLeft,
              child: Material(
                elevation: 4.0,
                borderRadius: BorderRadius.circular(8),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: options.length,
                    itemBuilder: (context, index) {
                      final option = options.elementAt(index);
                      return InkWell(
                        onTap: () => onSelected(option),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: Text(option),
                        ),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
