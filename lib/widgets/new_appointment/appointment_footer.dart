import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../providers/calendar_provider.dart';
import '../../models/service.dart';
import 'appointment_form_data.dart';

class AppointmentFooter extends StatelessWidget {
  final AppointmentFormData formData;
  final VoidCallback onCreateAppointment;
  final Function(bool) onPaidStatusChanged;

  const AppointmentFooter({
    super.key,
    required this.formData,
    required this.onCreateAppointment,
    required this.onPaidStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        final serviceDetails = calendarProvider.getServiceDetails();
        // Use actual appointment duration (start to end time)
        final actualDuration = formData.getActualDuration();
        final priceText = formData.getTotalPriceRangeText(serviceDetails);

        // Debug logging for footer price calculation
        debugPrint('💰 Footer price calculation:');
        debugPrint('   Pet size: ${formData.petSize}');
        debugPrint('   Services: ${formData.services}');
        debugPrint('   Total price: $priceText');

        final hours = actualDuration ~/ 60;
        final minutes = actualDuration % 60;
        String durationText;
        if (hours > 0 && minutes > 0) {
          durationText = '${hours}h ${minutes}min';
        } else if (hours > 0) {
          durationText = '${hours}h';
        } else {
          durationText = '${minutes}min';
        }

        // Check if any services have size-based pricing
        bool hasSizeBasedServices = false;
        for (final service in formData.services) {
          final details = serviceDetails[service];
          if (details != null) {
            try {
              final serviceObj = Service.fromJson(details);
              if (serviceObj.sizePrices != null) {
                hasSizeBasedServices = true;
                break;
              }
            } catch (e) {
              // Continue checking other services
            }
          }
        }

        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              child: Row(
                children: [
                  // Summary info (compact)
                  Expanded(
                    flex: 2,
                    child: Row(
                      children: [
                        Flexible(
                          child: _buildCompactSummary(
                            durationText,
                            Icons.access_time,
                            Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        SizedBox(width: 12),
                        Flexible(
                          child: _buildCompactSummary(
                            priceText,
                            Icons.payments,
                            Colors.orange,
                            hasSizeBasedServices ? formData.petSize : null,
                          ),
                        ),
                      ],
                    ),
                  ),

              SizedBox(width: 16),

              // Create button (compact)
              ElevatedButton.icon(
                onPressed: onCreateAppointment,
                icon:  Icon(Icons.check, size: 18),
                label: Text(
                  'Creează',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 1,
                ),
              ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactSummary(String value, IconData icon, Color color, [String? petSize]) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        SizedBox(width: 4),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              if (petSize != null && petSize.isNotEmpty) ...[
                Text(
                  'Mărime $petSize',
                  style: TextStyle(
                    fontSize: 9,
                    color: color.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }


}
