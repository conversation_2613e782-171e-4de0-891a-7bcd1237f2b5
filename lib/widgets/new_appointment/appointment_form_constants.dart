import 'package:flutter/cupertino.dart';

/// Constants for appointment form
class AppointmentFormConstants {
  // Pet species options
  static const List<String> petSpecies = [
    'dog',
    'cat',
    'bird',
    'rabbit',
    'hamster',
    'guinea_pig',
    'ferret',
    'other',
  ];

  // Pet species display names (Romanian)
  static const Map<String, String> petSpeciesDisplayNames = {
    'dog': '<PERSON><PERSON><PERSON>',
    'cat': '<PERSON><PERSON><PERSON><PERSON>',
    'bird': '<PERSON><PERSON><PERSON><PERSON>',
    'rabbit': 'Iepure',
    'hamster': '<PERSON>ster',
    'guinea_pig': '<PERSON><PERSON><PERSON>',
    'ferret': 'Di<PERSON>',
    'other': '<PERSON><PERSON>',
  };

  // Time intervals for time picker (in minutes)
  static const int timeInterval = 15;

  // Business hours - these will be fetched from API
  static int businessStartHour = 8;
  static int businessEndHour = 20;

  /// Gets display name for pet species
  static String getSpeciesDisplayName(String species) {
    debugPrint('🔍 getSpeciesDisplayName: $species');
    return petSpeciesDisplayNames[species] ?? species;
  }

  /// Generates time options for dropdown
  static List<DateTime> generateTimeOptions(DateTime date) {
    final List<DateTime> times = [];
    final startTime = DateTime(date.year, date.month, date.day, businessStartHour);
    final endTime = DateTime(date.year, date.month, date.day, businessEndHour);

    DateTime current = startTime;
    while (current.isBefore(endTime) || current.isAtSameMomentAs(endTime)) {
      times.add(current);
      current = current.add(const Duration(minutes: timeInterval));
    }

    return times;
  }

  /// Formats time for display
  static String formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // Breed lists with predefined size (S/M/L)
  static const Map<String, String> dogBreeds = {
    'Labrador Retriever': 'L',
    'Golden Retriever': 'L',
    'German Shepherd': 'L',
    'Bulldog': 'M',
    'Chihuahua': 'S',
    'Yorkshire Terrier': 'S',
    'Poodle': 'M',
    'Beagle': 'M',
    'Rottweiler': 'L',
    'Dachshund': 'S',
    'Siberian Husky': 'L',
    'Boxer': 'L',
    'Border Collie': 'M',
    'Shih Tzu': 'S',
    'Boston Terrier': 'S',
    'Pomeranian': 'S',
    'Australian Shepherd': 'M',
    'Cocker Spaniel': 'M',
    'French Bulldog': 'M',
    'Maltese': 'S',
    'Jack Russell Terrier': 'S',
    'Metis': 'M',
  };

  static const Map<String, String> catBreeds = {
    'Persian': 'M',
    'Maine Coon': 'L',
    'Siamese': 'M',
    'British Shorthair': 'M',
    'Ragdoll': 'M',
    'Bengal': 'M',
    'Abyssinian': 'M',
    'Birman': 'M',
    'Oriental Shorthair': 'M',
    'Devon Rex': 'S',
    'Sphynx': 'M',
    'Scottish Fold': 'M',
    'Russian Blue': 'M',
    'Norwegian Forest Cat': 'L',
    'Exotic Shorthair': 'M',
    'Cornish Rex': 'S',
    'Selkirk Rex': 'M',
    'Munchkin': 'S',
    'American Shorthair': 'M',
    'Turkish Angora': 'M',
    'Burmese': 'M',
    'Metis': 'M',
  };

  static List<String> get breedList => [
        ...dogBreeds.keys,
        ...catBreeds.keys,
        'Altă rasă'
      ];

  static String getBreedSize(String breed) {
    final size = dogBreeds[breed] ?? catBreeds[breed];

    if (size == null) {
      debugPrint('⚠️ Unknown breed "$breed", defaulting to Medium size');
      return 'M'; // Default to Medium for unknown breeds
    }

    debugPrint('📏 Breed "$breed" mapped to size: $size');
    return size;
  }

  /// Check if a breed has size-based pricing available
  static bool hasBreedSizeMapping(String breed) {
    return dogBreeds.containsKey(breed) || catBreeds.containsKey(breed);
  }

  /// Get all available sizes
  static List<String> get availableSizes => ['S', 'M', 'L'];

  /// Get size description
  static String getSizeDescription(String size) {
    switch (size) {
      case 'S':
        return 'Mic (S)';
      case 'M':
        return 'Mediu (M)';
      case 'L':
        return 'Mare (L)';
      default:
        return 'Necunoscut';
    }
  }

  static String getBreedSpecies(String breed) {
    if (dogBreeds.containsKey(breed)) return 'dog';
    if (catBreeds.containsKey(breed)) return 'cat';
    return 'other';
  }
}
