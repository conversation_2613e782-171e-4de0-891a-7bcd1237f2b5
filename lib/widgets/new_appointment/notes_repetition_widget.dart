import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';

class NotesRepetitionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(bool) onNotesVisibilityChanged;
  final Function(bool) onRepetitionVisibilityChanged;
  final Function(String) onRepetitionFrequencyChanged;
  final Function(String) onNotesChanged;

  const NotesRepetitionWidget({
    super.key,
    required this.formData,
    required this.onNotesVisibilityChanged,
    required this.onRepetitionVisibilityChanged,
    required this.onRepetitionFrequencyChanged,
    required this.onNotesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Small + buttons row
        Row(
          children: [
            _buildSmallAddButton(
              'Observații',
              Icons.note_alt,
              formData.showNotes,
              () => onNotesVisibilityChanged(!formData.showNotes),
            ),
            SizedBox(width: 12),
            _buildSmallAddButton(
              'Repetare',
              Icons.repeat,
              formData.showRepetition,
              () => onRepetitionVisibilityChanged(!formData.showRepetition),
            ),
          ],
        ),

        SizedBox(height: 16),

        // Show notes field if visible
        if (formData.showNotes) ...[
          _buildNotesField(),
          SizedBox(height: 16),
        ],

        // Show repetition options if visible
        if (formData.showRepetition)
          _buildRepetitionOptions(),
      ],
    );
  }

  Widget _buildSmallAddButton(String label, IconData icon, bool isSelected, VoidCallback onTap) {
    return Builder(
      builder: (context) => InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey[100],
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey[300]!,
              width: 1,
            ),
          ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? Icons.check : Icons.add,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
            SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildNotesField() {
    return Builder(
      builder: (context) => TextFormField(
        decoration: InputDecoration(
          labelText: 'Observații',
          hintText: 'Adaugă observații pentru această programare...',
          border: const OutlineInputBorder(),
          prefixIcon: Icon(Icons.note_alt, color: Theme.of(context).colorScheme.primary),
        ),
        maxLines: 3,
        initialValue: formData.notes,
        onChanged: onNotesChanged,
      ),
    );
  }

  Widget _buildRepetitionOptions() {
    const repetitionOptions = [
      {'value': 'none', 'label': 'Fără repetare'},
      {'value': 'daily', 'label': 'Zilnic'},
      {'value': 'weekly', 'label': 'Săptămânal'},
      {'value': 'biweekly', 'label': 'La 2 săptămâni'},
      {'value': 'monthly', 'label': 'Lunar'},
    ];

    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Frecvența repetării:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: formData.repetitionFrequency,
            decoration: InputDecoration(
              labelText: 'Selectează frecvența',
              border: const OutlineInputBorder(),
              prefixIcon: Icon(Icons.repeat, color: Theme.of(context).colorScheme.primary),
            ),
          isExpanded: true,
          items: repetitionOptions.map((option) {
            return DropdownMenuItem<String>(
              value: option['value'],
              child: Text(option['label']!),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onRepetitionFrequencyChanged(value);
            }
          },
        ),
          if (formData.repetitionFrequency != 'none') ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Theme.of(context).colorScheme.primary, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getRepetitionDescription(formData.repetitionFrequency),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getRepetitionDescription(String frequency) {
    switch (frequency) {
      case 'daily':
        return 'Programarea se va repeta în fiecare zi la aceeași oră.';
      case 'weekly':
        return 'Programarea se va repeta în fiecare săptămână la aceeași zi și oră.';
      case 'biweekly':
        return 'Programarea se va repeta la fiecare 2 săptămâni.';
      case 'monthly':
        return 'Programarea se va repeta în fiecare lună la aceeași dată.';
      default:
        return '';
    }
  }
}
