import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';
import 'appointment_form_constants.dart';

class ServiceTimeSelectionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(String) onServiceChanged;
  final Function(DateTime) onStartTimeChanged;
  final Function(DateTime) onEndTimeChanged;
  final Function(bool) onPaidStatusChanged;
  final Function(String) onNotesChanged;

  const ServiceTimeSelectionWidget({
    super.key,
    required this.formData,
    required this.onServiceChanged,
    required this.onStartTimeChanged,
    required this.onEndTimeChanged,
    required this.onPaidStatusChanged,
    required this.onNotesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildServiceSelection(),
        const SizedBox(height: 16),
        _buildTimeSelection(),
        const SizedBox(height: 16),
        _buildPaymentStatus(),
        const SizedBox(height: 16),
        _buildNotesField(),
      ],
    );
  }

  Widget _buildServiceSelection() {
    final currentService = formData.services.isNotEmpty ? formData.services.first : null;

    return DropdownButtonFormField<String>(
      value: formData.availableServices.contains(currentService) ? currentService : null,
      decoration: const InputDecoration(
        labelText: 'Serviciu',
        border: OutlineInputBorder(),
      ),
      isExpanded: true,
      items: formData.availableServices.map((service) {
        return DropdownMenuItem<String>(
          value: service,
          child: Text(
            service,
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          onServiceChanged(value);
        }
      },
    );
  }

  Widget _buildTimeSelection() {
    final timeOptions = AppointmentFormConstants.generateTimeOptions(formData.startTime);

    return Row(
      children: [
        Expanded(
          child: DropdownButtonFormField<DateTime>(
            value: timeOptions.contains(formData.startTime) ? formData.startTime : null,
            decoration: const InputDecoration(
              labelText: 'Ora început',
              border: OutlineInputBorder(),
            ),
            isExpanded: true,
            items: timeOptions.map((time) {
              return DropdownMenuItem<DateTime>(
                value: time,
                child: Text(AppointmentFormConstants.formatTime(time)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                onStartTimeChanged(value);
              }
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: DropdownButtonFormField<DateTime>(
            value: timeOptions.contains(formData.endTime) ? formData.endTime : null,
            decoration: const InputDecoration(
              labelText: 'Ora sfârșit',
              border: OutlineInputBorder(),
            ),
            isExpanded: true,
            items: timeOptions.map((time) {
              return DropdownMenuItem<DateTime>(
                value: time,
                child: Text(AppointmentFormConstants.formatTime(time)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                onEndTimeChanged(value);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentStatus() {
    // Payment status removed as requested
    return const SizedBox.shrink();
  }

  Widget _buildNotesField() {
    return TextFormField(
      decoration: const InputDecoration(
        labelText: 'Observații',
        border: OutlineInputBorder(),
        alignLabelWithHint: true,
      ),
      maxLines: 3,
      initialValue: formData.notes,
      onChanged: onNotesChanged,
    );
  }
}
