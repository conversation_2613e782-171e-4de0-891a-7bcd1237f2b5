import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../config/theme/app_theme.dart';
import '../screens/main_layout.dart';
import 'dart:io' show Platform;

class SocialLoginButton extends StatelessWidget {
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final String text;
  final VoidCallback onPressed;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(icon, color: iconColor, size: 20),
          const SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(
              color: backgroundColor == Colors.white ? Colors.black87 : Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class SocialLoginButtons extends StatelessWidget {
  const SocialLoginButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return Column(
      children: [
        SocialLoginButton(
          icon: FontAwesomeIcons.google,
          backgroundColor: Colors.white,
          iconColor: Colors.red,
          text: 'Continue with Google',
          onPressed: () async {
            debugPrint('Google Sign In button pressed');
            try {
              debugPrint('Attempting Google Sign In...');
              final success = await authProvider.signInWithGoogle();
              debugPrint('Google Sign In result: $success');
              if (success && context.mounted) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => const MainLayout()),
                );
              } else if (context.mounted) {
                // Show user-friendly Romanian error message
                String errorMessage = 'Autentificarea cu Google a eșuat';
                if (authProvider.error != null) {
                  // Check if user cancelled the sign-in
                  if (authProvider.error!.toLowerCase().contains('cancelled') ||
                      authProvider.error!.toLowerCase().contains('cancel')) {
                    errorMessage = 'Autentificarea cu Google a fost anulată';
                  }
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(errorMessage),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            } catch (e) {
              debugPrint('Google Sign In exception: $e');
              if (context.mounted) {
                String errorMessage = 'Autentificarea cu Google a eșuat';
                // Check if user cancelled the sign-in
                if (e.toString().toLowerCase().contains('cancelled') ||
                    e.toString().toLowerCase().contains('cancel')) {
                  errorMessage = 'Autentificarea cu Google a fost anulată';
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(errorMessage),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            }
          },
        ),
        const SizedBox(height: 12),

        if (!kIsWeb && (Platform.isIOS || Platform.isMacOS))
          SocialLoginButton(
            icon: FontAwesomeIcons.apple,
            backgroundColor: Colors.black,
            iconColor: Colors.white,
            text: 'Continue with Apple',
            onPressed: () async {
              debugPrint('Apple Sign In button pressed');
              try {
                debugPrint('Attempting Apple Sign In...');
                final success = await authProvider.signInWithApple();
                debugPrint('Apple Sign In result: $success');
                if (success && context.mounted) {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => const MainLayout()),
                  );
                } else if (context.mounted) {
                  // Show error from provider if sign-in was not successful
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Apple Sign In failed: ${authProvider.error ?? "Unknown error"}')),
                  );
                }
              } catch (e) {
                debugPrint('Apple Sign In exception: $e');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Apple Sign In failed: $e')),
                  );
                }
              }
            },
          ),

        const SizedBox(height: 12),
        SocialLoginButton(
          icon: FontAwesomeIcons.phone,
          backgroundColor: AppColors.forestGreen,
          iconColor: Colors.white,
          text: 'Continue with Phone',
          onPressed: () {
            Navigator.pushNamed(context, '/phone-login');
          },
        ),
      ],
    );
  }
}
