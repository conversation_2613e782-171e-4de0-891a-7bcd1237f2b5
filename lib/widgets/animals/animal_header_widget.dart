import 'package:flutter/material.dart';
import 'dart:io';
import '../../models/pet.dart';
import '../../config/theme/app_theme.dart';

class AnimalHeaderWidget extends StatelessWidget {
  final Pet pet;
  final File? photoFile;
  final VoidCallback? onPhotoTap;

  const AnimalHeaderWidget({
    super.key,
    required this.pet,
    this.photoFile,
    this.onPhotoTap,
  });

  Widget _getPetIcon(String species) {
    switch (species.toLowerCase()) {
      case 'câine':
      case 'caine':
      case 'dog':
        return Icon(
          Icons.pets,
          color: AppColors.white,
          size: 24,
        );
      case 'pisică':
      case 'pisica':
      case 'cat':
        return Icon(
          Icons.pets,
          color: AppColors.white,
          size: 24,
        );
      case 'iepure':
      case 'rabbit':
        return Icon(
          Icons.cruelty_free,
          color: AppColors.white,
          size: 24,
        );
      default:
        return Icon(
          Icons.pets,
          color: AppColors.white,
          size: 24,
        );
    }
  }

  String _getGenderText(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
      case 'masculin':
      case 'm':
        return 'Masculin';
      case 'female':
      case 'feminin':
      case 'f':
        return 'Feminin';
      default:
        return 'Necunoscut';
    }
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final years = (difference.inDays / 365).floor();
    final months = ((difference.inDays % 365) / 30).floor();

    if (years > 0) {
      if (months > 0) {
        return '$years ani, $months luni';
      } else {
        return '$years ${years == 1 ? 'an' : 'ani'}';
      }
    } else if (months > 0) {
      return '$months ${months == 1 ? 'lună' : 'luni'}';
    } else {
      final weeks = (difference.inDays / 7).floor();
      if (weeks > 0) {
        return '$weeks ${weeks == 1 ? 'săptămână' : 'săptămâni'}';
      } else {
        return '${difference.inDays} ${difference.inDays == 1 ? 'zi' : 'zile'}';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Pet photo placeholder and basic info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pet photo placeholder
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.white.withValues(alpha: 0.2),
                  border: Border.all(
                    color: AppColors.white,
                    width: 2,
                  ),
                ),
                child: GestureDetector(
                  onTap: onPhotoTap,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CircleAvatar(
                        radius: 38,
                        backgroundColor: AppColors.logoBrown.withValues(alpha: 0.8),
                        backgroundImage: photoFile != null
                            ? FileImage(photoFile!)
                            : (pet.photoUrl.isNotEmpty ? NetworkImage(pet.photoUrl) as ImageProvider : null),
                        child: photoFile == null && pet.photoUrl.isEmpty
                            ? _getPetIcon(pet.species)
                            : null,
                      ),
                      if (onPhotoTap != null)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.camera_alt,
                              size: 16,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 16),
              // Pet info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Name and species
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            pet.name,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            pet.species,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Breed and gender
                    Text(
                      '${pet.breed} • ${_getGenderText(pet.gender)}',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    SizedBox(height: 4),
                    
                    // Age and weight
                    Text(
                      '${_calculateAge(pet.birthDate)} • ${pet.weight.toStringAsFixed(1)} kg',
                      style: TextStyle(
                        color: AppColors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
