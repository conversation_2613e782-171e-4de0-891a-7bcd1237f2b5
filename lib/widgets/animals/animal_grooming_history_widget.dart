import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/pet.dart';
import '../../models/appointment.dart';
import '../../config/theme/app_theme.dart';

class AnimalGroomingHistoryWidget extends StatelessWidget {
  final Pet pet;
  final List<Appointment> groomingHistory;

  const AnimalGroomingHistoryWidget({
    super.key,
    required this.pet,
    required this.groomingHistory,
  });

  Widget _buildGroomingCard(Appointment appointment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with service and date
          Row(
            children: [
              Expanded(
                child: Text(
                  appointment.service,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(appointment.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  appointment.status,
                  style: TextStyle(
                    color: _getStatusColor(appointment.status),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 8),

          // Date and groomer
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppColors.taupe,
              ),
              SizedBox(width: 4),
              Text(
                DateFormat('dd MMM yyyy, HH:mm', 'ro').format(appointment.startTime),
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.taupe,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.person,
                size: 16,
                color: AppColors.taupe,
              ),
              SizedBox(width: 4),
              Text(
                'Groomer',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.taupe,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Notes if any
          if (appointment.notes.isNotEmpty) ...[
            SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.appBackground,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Observații:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.logoBrown,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    appointment.notes,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.primary,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],

          SizedBox(height: 8),

          // Payment status
          Row(
            children: [
              const Spacer(),
              if (appointment.isPaid)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Plătit',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Neplătit',
                    style: TextStyle(
                      color: Colors.orange,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'finalizat':
      case 'completed':
        return Colors.green;
      case 'programat':
      case 'scheduled':
        return Colors.blue;
      case 'anulat':
      case 'cancelled':
        return Colors.red;
      case 'în desfășurare':
      case 'in progress':
        return Colors.orange;
      default:
        return AppColors.taupe;
    }
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: color.withValues(alpha: 0.8),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final totalSessions = groomingHistory.length;
    final lastVisit = groomingHistory.isNotEmpty
        ? groomingHistory.first.startTime
        : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Istoric grooming',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        SizedBox(height: 16),

        // Statistics
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            _buildStatCard(
              'Total\nSesiuni',
              totalSessions.toString(),
              Icons.pets,
              Theme.of(context).colorScheme.primary,
            ),
            _buildStatCard(
              'Ultima\nVizită',
              lastVisit != null
                  ? DateFormat('dd MMM', 'ro').format(lastVisit)
                  : 'N/A',
              Icons.schedule,
              AppColors.logoBrown,
            ),
          ],
        ),

        SizedBox(height: 16),

        // History list
        if (groomingHistory.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.pets,
                  size: 48,
                  color: AppColors.taupe.withValues(alpha: 0.5),
                ),
                SizedBox(height: 16),
                Text(
                  'Niciun istoric de grooming',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.taupe,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Programează prima sesiune de grooming pentru ${pet.name}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.taupe.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else
          ...groomingHistory.map((appointment) => _buildGroomingCard(appointment)).toList(),
      ],
    );
  }
}
