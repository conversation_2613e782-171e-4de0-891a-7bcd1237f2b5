import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';
import '../../services/staff_service.dart';
import '../calendar_views/google_calendar_view.dart';

class CalendarSettingsDialog extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final Function(CalendarViewMode) onViewModeChanged;

  const CalendarSettingsDialog({
    Key? key,
    required this.currentViewMode,
    required this.onViewModeChanged,
  }) : super(key: key);

  @override
  State<CalendarSettingsDialog> createState() => _CalendarSettingsDialogState();
}

class _CalendarSettingsDialogState extends State<CalendarSettingsDialog> {
  late CalendarViewMode _selectedViewMode;

  @override
  void initState() {
    super.initState();
    _selectedViewMode = widget.currentViewMode;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Setări Calendar',
        style: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 24),
            _buildStaffFilterSection(),
            SizedBox(height: 24),
            _buildDisplayOptionsSection(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Anulează'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onViewModeChanged(_selectedViewMode);
            Navigator.of(context).pop();
          },
          child: Text('Aplică'),
        ),
      ],
    );
  }


  Widget _buildStaffFilterSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final staff = provider.availableStaff;
        final selectedStaff = provider.selectedStaff;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Filtrare personal',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    if (selectedStaff.length == staff.length) {
                      provider.clearStaffSelection();
                    } else {
                      provider.selectAllStaff();
                    }
                  },
                  child: Text(
                    selectedStaff.length == staff.length
                      ? 'Deselectează tot'
                      : 'Selectează tot',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            if (staff.isEmpty)
              Text(
                'Nu este personal disponibil',
                style: TextStyle(color: Colors.grey),
              )
            else
              ...staff.map((staffMember) => _buildStaffOption(staffMember, provider)),
            SizedBox(height: 8),
            _buildStaffLegend(staff),
          ],
        );
      },
    );
  }

  Widget _buildStaffOption(StaffResponse staffMember, CalendarProvider provider) {
    final isSelected = provider.selectedStaff.contains(staffMember.id);
    final staffColor = provider.getStaffColor(staffMember.id);

    return CheckboxListTile(
      value: isSelected,
      onChanged: (value) {
        if (value == true) {
          provider.selectStaff(staffMember.id);
        } else {
          provider.deselectStaff(staffMember.id);
        }
      },
      title: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: staffColor,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(staffMember.displayName),
                if (staffMember.nickname != null && staffMember.nickname!.isNotEmpty && staffMember.nickname != staffMember.name) ...[
                  Text(
                    '(${staffMember.name})',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
      activeColor: Theme.of(context).colorScheme.primary,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildStaffLegend(List<StaffResponse> staff) {
    if (staff.isEmpty) return SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Legendă culori:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 12,
            runSpacing: 4,
            children: staff.map((staffMember) => Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: StaffColors.getColorForStaff(staffMember.id),
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 4),
                Text(
                  staffMember.displayName,
                  style: TextStyle(fontSize: 11),
                ),
              ],
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayOptionsSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Opțiuni afișare',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 8),
            CheckboxListTile(
              title: Text('Afișează programări anulate'),
              value: provider.showCanceledAppointments,
              onChanged: (value) {
                provider.setShowCanceledAppointments(value ?? false);
              },
              activeColor: Theme.of(context).colorScheme.primary,
              contentPadding: EdgeInsets.zero,
            ),
            CheckboxListTile(
              title: Text('Afișează doar programări neîncasate'),
              value: provider.showUnpaidAppointments,
              onChanged: (value) {
                provider.setShowUnpaidAppointments(value ?? false);
              },
              activeColor: Theme.of(context).colorScheme.primary,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        );
      },
    );
  }


}
