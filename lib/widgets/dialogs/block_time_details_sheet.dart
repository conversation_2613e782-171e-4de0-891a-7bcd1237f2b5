import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../providers/calendar_provider.dart';
import '../common/custom_bottom_sheet.dart';

class BlockTimeDetailsSheet extends StatefulWidget {
  final Map<String, dynamic> block;
  final bool isWeekView;

  const BlockTimeDetailsSheet({
    super.key,
    required this.block,
    this.isWeekView = false,
  });

  @override
  State<BlockTimeDetailsSheet> createState() => _BlockTimeDetailsSheetState();
}

class _BlockTimeDetailsSheetState extends State<BlockTimeDetailsSheet> {
  bool _isDeleting = false;

  Future<void> _deleteBlock() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: Text('Confirmă ștergerea'),
        content: Text('Sigur dorești să ștergi această blocare?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              
            ),
            child: Text('Șterge'),
          ),
        ],
      ),
    );
    if (confirm != true) return;

    // Close dialog immediately for instant feedback
    if (mounted) Navigator.pop(context);

    // Perform the deletion in the background
    final provider = Provider.of<CalendarProvider>(context, listen: false);
    final success = await provider.deleteBlockTime(
      widget.block['blockId'],
      refreshWeek: widget.isWeekView,
    );

    // Show feedback only if deletion failed
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Eroare la ștergerea blocării. Blocarea a fost restaurată.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 110,
            child: Text(
              label,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final start = DateTime.parse(widget.block['startTime']).toLocal();
    final end = DateTime.parse(widget.block['endTime']).toLocal();
    final duration = end.difference(start);
    final reason = (widget.block['customReason'] ?? widget.block['reason'] ?? '') as String;
    final staffNames = (widget.block['staffNames'] as List?)?.join(', ') ?? '';
    final createdBy = widget.block['createdByName'] ?? '';
    final createdAt = widget.block['createdAt'] != null
        ? DateFormat('dd MMM yyyy HH:mm').format(DateTime.parse(widget.block['createdAt']).toLocal())
        : '';
    final status = widget.block['status'] ?? '';

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _infoRow('De la', DateFormat('dd MMM yyyy HH:mm').format(start)),
          _infoRow('Până la', DateFormat('dd MMM yyyy HH:mm').format(end)),
          _infoRow('Durată', '${duration.inMinutes} min'),
          if (reason.isNotEmpty) _infoRow('Motiv', reason),
          if (staffNames.isNotEmpty) _infoRow('Personal', staffNames),
          if (createdBy.isNotEmpty) _infoRow('Creat de', '$createdBy — $createdAt'),
          _infoRow('Status', status),
          SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isDeleting ? null : _deleteBlock,
              icon: _isDeleting
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                  :  Icon(Icons.delete),
              label: Text('Șterge blocarea'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                
              ),
            ),
          ),
        ],
      ),
    );
  }
}
