import 'package:animaliaproject/widgets/new_appointment/service_time_selection_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';

import '../../services/appointment/calendar_service.dart';
import '../../services/error_message_service.dart';
import '../../models/appointment_alternative.dart';
import 'conflict_resolution_dialog.dart';
import '../new_appointment/appointment_form_constants.dart';
import '../new_appointment/appointment_form_data.dart';
import '../new_appointment/appointment_form_logic.dart';
import '../new_appointment/client_selection_widget.dart';
import '../new_appointment/pet_selection_widget.dart';

class NewAppointmentDialog extends StatefulWidget {
  final DateTime selectedDate;
  final Function(bool) onAppointmentAdded;

  const NewAppointmentDialog({
    super.key,
    required this.selectedDate,
    required this.onAppointmentAdded,
  });

  @override
  State<NewAppointmentDialog> createState() => _NewAppointmentDialogState();
}

class _NewAppointmentDialogState extends State<NewAppointmentDialog> {
  late AppointmentFormData _formData;
  late AppointmentFormLogic _formLogic;
  bool _isLoading = false;
  Future<AppointmentCreationResult>? _addAppointmentFuture;
  bool _conflictDialogShown = false;

  @override
  void initState() {
    super.initState();
    _initializeFormData();
    _initializeFormLogic();
    _loadInitialData();
  }

  void _initializeFormData() {
    final initialStartTime = _calculateInitialStartTime();
    final initialEndTime = initialStartTime.add(const Duration(hours: 1));

    _formData = AppointmentFormData(
      appointmentDate: widget.selectedDate,
      startTime: initialStartTime,
      endTime: initialEndTime,
    );
  }

  void _initializeFormLogic() {
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    _formLogic = AppointmentFormLogic(calendarProvider);
  }

  DateTime _calculateInitialStartTime() {
    final now = DateTime.now();
    final selectedDateTime = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
      widget.selectedDate.day,
      now.hour,
      now.minute,
    );

    // Round up to the nearest 15-minute interval
    final minutes = selectedDateTime.minute;
    final roundedMinutes = ((minutes / 15).ceil() * 15) % 60;
    final additionalHour = ((minutes / 15).ceil() * 15) ~/ 60;

    var startTime = DateTime(
      selectedDateTime.year,
      selectedDateTime.month,
      selectedDateTime.day,
      selectedDateTime.hour + additionalHour,
      roundedMinutes,
    );

    // Ensure start time is within business hours
    if (startTime.hour < AppointmentFormConstants.businessStartHour) {
      startTime = DateTime(startTime.year, startTime.month, startTime.day,
          AppointmentFormConstants.businessStartHour, 0);
    } else if (startTime.hour >= AppointmentFormConstants.businessEndHour) {
      if (widget.selectedDate.isAfter(DateTime.now())) {
        startTime = DateTime(widget.selectedDate.year, widget.selectedDate.month,
            widget.selectedDate.day, AppointmentFormConstants.businessStartHour, 0);
      } else {
        startTime = DateTime(startTime.year, startTime.month, startTime.day + 1,
            AppointmentFormConstants.businessStartHour, 0);
      }
    }

    return startTime;
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() => _isLoading = true);
      try {
        await _formLogic.loadInitialData(_formData);
      } finally {
        if (mounted) setState(() => _isLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_addAppointmentFuture != null) {
      return _buildAppointmentCreationDialog();
    }

    return _buildFormDialog();
  }

  Widget _buildAppointmentCreationDialog() {
    return AlertDialog(
      title: const Text('Programare nouă'),
      content: FutureBuilder<AppointmentCreationResult>(
        future: _addAppointmentFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingContent();
          } else if (snapshot.hasError) {
            return _buildErrorContent(snapshot.error.toString());
          } else if (snapshot.hasData) {
            final result = snapshot.data!;
            return _buildResultContent(result);
          } else {
            return _buildErrorContent('Nu s-a putut crea programarea');
          }
        },
      ),
      actions: [
        FutureBuilder<AppointmentCreationResult>(
          future: _addAppointmentFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox.shrink();
            }
            final result = snapshot.data;
            final success = result?.success ?? false;

            if (success) {
              return TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onAppointmentAdded(true);
                },
                child: const Text('Închide'),
              );
            } else {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextButton(
                    onPressed: () {
                      // Go back to form to try again
                      setState(() {
                        _addAppointmentFuture = null;
                      });
                    },
                    child: const Text('Încearcă din nou'),
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onAppointmentAdded(false);
                    },
                    child: const Text('Anulează'),
                  ),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildLoadingContent() {
    return const Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircularProgressIndicator(color: AppColors.forestGreen),
        SizedBox(width: 20),
        Text('Creăm programarea...'),
      ],
    );
  }

  Widget _buildErrorContent(String error) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.error, color: Colors.red, size: 48),
        const SizedBox(height: 16),
        Text(
          'Eroare: $error',
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildResultContent(AppointmentCreationResult result) {
    if (result.success) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 48),
          const SizedBox(height: 16),
          const Text(
            'Programarea a fost adăugată cu succes!',
            textAlign: TextAlign.center,
          ),
        ],
      );
    } else {
      // Show specific error message for business logic errors
      final errorMessage = result.userFriendlyError;
      final suggestion = ErrorMessageService.getSuggestedAction(result.errorCode);

      debugPrint('🔍 Dialog - Checking conflict dialog conditions:');
      debugPrint('  - isSchedulingConflict: ${result.isSchedulingConflict}');
      debugPrint('  - conflictDialogShown: $_conflictDialogShown');
      debugPrint('  - alternatives count: ${result.alternatives.length}');
      debugPrint('  - errorCode: ${result.errorCode}');

      if (result.isSchedulingConflict && !_conflictDialogShown) {
        debugPrint('✅ Dialog - Showing conflict resolution dialog');
        _conflictDialogShown = true;
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final alt = await showDialog<AppointmentAlternative>(
            context: context,
            builder: (ctx) => ConflictResolutionDialog(
              alternatives: result.alternatives,
              onTryDifferentTime: () {
                Navigator.of(ctx).pop();
                setState(() {
                  _addAppointmentFuture = null;
                  _conflictDialogShown = false;
                });
              },
              onContactSalon: () {
                Navigator.of(ctx).pop();
              },
              onAddToWaitingList: () {
                Navigator.of(ctx).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Adăugat la lista de așteptare')),
                );
                setState(() {
                  _addAppointmentFuture = null;
                  _conflictDialogShown = false;
                });
              },
              onSelectAlternative: (alternative) {
                Navigator.of(ctx).pop(alternative);
              },
            ),
          );
          if (alt != null) {
            debugPrint('🔄 Dialog - Alternative selected: ${alt.startTime} - ${alt.endTime} (Staff: ${alt.staffName})');
            setState(() {
              _formData.startTime = alt.startTime;
              _formData.endTime = alt.endTime;
              _formData.assignedStaffId = alt.staffId;
              // CRITICAL FIX: Update appointmentDate to match the alternative date
              _formData.appointmentDate = DateTime(
                alt.startTime.year,
                alt.startTime.month,
                alt.startTime.day,
              );
              debugPrint('✅ Dialog - Form data updated - Date: ${_formData.appointmentDate}, Time: ${_formData.startTime} - ${_formData.endTime}');
              _addAppointmentFuture = _formLogic.createAppointment(_formData);
              _conflictDialogShown = false;
            });
          }
        });
      }

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            result.isSchedulingConflict ? Icons.schedule : Icons.error,
            color: result.isSchedulingConflict ? Colors.orange : Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          if (suggestion != null) ...[
            const SizedBox(height: 12),
            Text(
              suggestion,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      );
    }
  }

  Widget _buildFormDialog() {
    return AlertDialog(
      title: const Text('Programare nouă'),
      content: _buildFormContent(),
      actions: _buildFormActions(),
    );
  }

  Widget _buildFormContent() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateHeader(),
          const SizedBox(height: 16),
          _buildClientSection(),
          const SizedBox(height: 16),
          _buildPetSection(),
          const SizedBox(height: 16),
          _buildServiceTimeSection(),
        ],
      ),
    );
  }

  Widget _buildDateHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Data:', style: TextStyle(fontWeight: FontWeight.bold)),
        Text(DateFormat('dd MMMM yyyy', 'ro').format(widget.selectedDate)),
      ],
    );
  }

  Widget _buildClientSection() {
    return ClientSelectionWidget(
      formData: _formData,
      isLoadingClients: _isLoading,
      onClientTypeChanged: (isExisting) {
        setState(() {
          _formLogic.handleClientTypeChange(_formData, isExisting);
        });
      },
      onClientSelected: (client) async {
        await _formLogic.handleClientSelection(_formData, client);
        setState(() {});
      },
      onClientNameChanged: (name) {
        setState(() => _formData.clientName = name);
      },
      onClientPhoneChanged: (phone) {
        setState(() => _formData.clientPhone = phone);
      },
    );
  }

  Widget _buildPetSection() {
    return PetSelectionWidget(
      formData: _formData,
      onPetSelected: (pet) {
        setState(() {
          _formLogic.handlePetSelection(_formData, pet);
        });
      },
      onPetNameChanged: (name) {
        setState(() => _formData.petName = name);
      },
      onPetBreedChanged: (breed) {
        setState(() {
          _formLogic.handlePetBreedChange(_formData, breed);
        });
      },
      onAddNewPet: () {
        setState(() {
          _formLogic.handleAddNewPet(_formData);
        });
      },
    );
  }

  Widget _buildServiceTimeSection() {
    return ServiceTimeSelectionWidget(
      formData: _formData,
      onServiceChanged: (service) {
        setState(() {
          _formLogic.handleAddService(_formData, service);
        });
      },
      onStartTimeChanged: (startTime) {
        setState(() {
          _formLogic.handleStartTimeChange(_formData, startTime);
        });
      },
      onEndTimeChanged: (endTime) {
        setState(() => _formData.endTime = endTime);
      },
      onPaidStatusChanged: (isPaid) {
        setState(() => _formData.isPaid = isPaid);
      },
      onNotesChanged: (notes) {
        setState(() => _formData.notes = notes);
      },
    );
  }

  List<Widget> _buildFormActions() {
    return [
      TextButton(
        onPressed: () => Navigator.of(context).pop(),
        child: const Text('Anulează'),
      ),
      ElevatedButton(
        onPressed: _handleCreateAppointment,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.forestGreen,
        ),
        child: const Text('Creează'),
      ),
    ];
  }

  void _handleCreateAppointment() async {
    final validationError = _formLogic.validateForm(_formData);
    if (validationError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(validationError)),
      );
      return;
    }

    setState(() {
      _conflictDialogShown = false;
      _addAppointmentFuture = _formLogic.createAppointment(_formData);
    });
  }
}
