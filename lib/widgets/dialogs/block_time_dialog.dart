import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';
import '../../services/staff_service.dart';
import '../../models/user_role.dart';

/// Production-ready block time dialog for the Animalia grooming salon management app
///
/// Features:
/// - Intuitive UX with Material Design and forest green theme
/// - Minimal clicks (3 or fewer) to complete blocking
/// - Smart defaults (current date, 1-hour duration, current user)
/// - Quick action presets (30min, 1hr, 2hr, half-day, full-day)
/// - Staff selection with current user pre-selected
/// - Native date/time pickers with Romanian localization
/// - Predefined reason chips with custom input option
/// - Visual confirmation before saving
/// - Overlap validation and warnings
/// - Responsive design for phone and tablet
/// - Accessibility support
/// - Calendar provider integration with auto-refresh
class BlockTimeDialog extends StatefulWidget {
  final DateTime selectedDate;
  final String? preselectedStaffId;
  final TimeOfDay? preselectedStartTime;

  const BlockTimeDialog({
    Key? key,
    required this.selectedDate,
    this.preselectedStaffId,
    this.preselectedStartTime,
  }) : super(key: key);

  @override
  State<BlockTimeDialog> createState() => _BlockTimeDialogState();
}

class _BlockTimeDialogState extends State<BlockTimeDialog> with TickerProviderStateMixin {
  // Form data
  late DateTime _selectedDate;
  late TimeOfDay _startTime;
  late TimeOfDay _endTime;
  String _selectedReason = 'Pauză';
  String _customReason = '';
  List<String> _selectedStaffIds = [];

  // Available data
  List<StaffResponse> _availableStaff = [];
  bool _isLoadingStaff = false;

  // UI state
  bool _isLoading = false;
  late TabController _tabController;

  // Quick action presets
  final List<Map<String, dynamic>> _quickActions = [
    {'label': '30 min', 'duration': 30, 'icon': Icons.timer},
    {'label': '1 oră', 'duration': 60, 'icon': Icons.schedule},
    {'label': '2 ore', 'duration': 120, 'icon': Icons.access_time},
    {'label': 'Jumătate zi', 'duration': 240, 'icon': Icons.wb_sunny_outlined},
    {'label': 'Zi întreagă', 'duration': 480, 'icon': Icons.brightness_1},
  ];

  // Reason categories
  final List<String> _reasonCategories = [
    'Pauză',
    'Întâlnire',
    'Concediu',
    'Personal',
    'Altele'
  ];

  @override
  void initState() {
    super.initState();
    _initializeDefaults();
    _loadStaff();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Initialize smart defaults
  void _initializeDefaults() {
    _selectedDate = widget.selectedDate;

    // Smart time defaults
    final now = TimeOfDay.now();
    if (widget.preselectedStartTime != null) {
      _startTime = widget.preselectedStartTime!;
    } else {
      // Round to next 15-minute interval
      final minutes = (now.minute / 15).ceil() * 15;
      if (minutes >= 60) {
        _startTime = TimeOfDay(hour: (now.hour + 1) % 24, minute: 0);
      } else {
        _startTime = TimeOfDay(hour: now.hour, minute: minutes);
      }
    }

    // Default 1-hour duration
    _endTime = _addDurationToTime(_startTime, 60);
  }

  /// Load available staff and pre-select current user
  Future<void> _loadStaff() async {
    setState(() {
      _isLoadingStaff = true;
    });

    try {
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);
      if (response.success && response.data != null) {
        setState(() {
          _availableStaff = response.data!.activeStaff;

          // Pre-select current user or provided staff ID
          if (widget.preselectedStaffId != null) {
            _selectedStaffIds = [widget.preselectedStaffId!];
          } else {
            // Try to find current user in staff list
            final currentUser = _availableStaff.firstWhere(
              (staff) => staff.isActive,
              orElse: () => _availableStaff.isNotEmpty ? _availableStaff.first :
                StaffResponse(
                  id: 'default',
                  name: 'Utilizator curent',
                  phone: '',
                  email: '',
                  groomerRole: GroomerRole.groomer,
                  clientDataPermission: ClientDataPermission.limitedAccess,
                  isActive: true,
                  joinedAt: DateTime.now(),
                ),
            );
            _selectedStaffIds = [currentUser.id];
          }
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading staff: $e');
    } finally {
      setState(() {
        _isLoadingStaff = false;
      });
    }
  }

  /// Add duration in minutes to a TimeOfDay
  TimeOfDay _addDurationToTime(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    return TimeOfDay(
      hour: (totalMinutes ~/ 60) % 24,
      minute: totalMinutes % 60,
    );
  }

  /// Apply quick action preset
  void _applyQuickAction(int durationMinutes) {
    setState(() {
      if (durationMinutes >= 480) {
        // Full day
        _startTime = const TimeOfDay(hour: 8, minute: 0);
        _endTime = const TimeOfDay(hour: 18, minute: 0);
      } else if (durationMinutes >= 240) {
        // Half day
        _startTime = const TimeOfDay(hour: 8, minute: 0);
        _endTime = const TimeOfDay(hour: 12, minute: 0);
      } else {
        // Keep current start time, adjust end time
        _endTime = _addDurationToTime(_startTime, durationMinutes);
      }
    });
  }

  /// Get duration in minutes between start and end time
  int _getDurationMinutes() {
    final startMinutes = _startTime.hour * 60 + _startTime.minute;
    final endMinutes = _endTime.hour * 60 + _endTime.minute;
    return endMinutes - startMinutes;
  }

  /// Format duration for display
  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes min';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ${hours == 1 ? 'oră' : 'ore'}';
      } else {
        return '$hours:${remainingMinutes.toString().padLeft(2, '0')} ore';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 600,
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  /// Build dialog header with title and close button
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppColors.forestGreen,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.block,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Blochează timp',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
            tooltip: 'Închide',
          ),
        ],
      ),
    );
  }

  /// Build main content area with tabs
  Widget _buildContent() {
    return Column(
      children: [
        // Tab bar
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: AppColors.forestGreen,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.forestGreen,
            tabs: const [
              Tab(
                icon: Icon(Icons.schedule),
                text: 'Timp',
              ),
              Tab(
                icon: Icon(Icons.people),
                text: 'Echipă',
              ),
              Tab(
                icon: Icon(Icons.info),
                text: 'Detalii',
              ),
            ],
          ),
        ),
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildTimeTab(),
              _buildStaffTab(),
              _buildDetailsTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build time selection tab
  Widget _buildTimeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick actions
          const Text(
            'Acțiuni rapide',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _quickActions.map((action) {
              return ActionChip(
                avatar: Icon(
                  action['icon'],
                  size: 18,
                  color: AppColors.forestGreen,
                ),
                label: Text(action['label']),
                onPressed: () => _applyQuickAction(action['duration']),
                backgroundColor: Colors.grey.shade100,
                side: const BorderSide(color: AppColors.forestGreen),
              );
            }).toList(),
          ),
          const SizedBox(height: 24),

          // Date selection
          const Text(
            'Data',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: _selectDate,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: AppColors.forestGreen,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      DateFormat('EEEE, dd MMMM yyyy', 'ro').format(_selectedDate),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const Icon(
                    Icons.arrow_drop_down,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Time selection
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Ora început',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.forestGreen,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () => _selectStartTime(),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.access_time,
                              color: AppColors.forestGreen,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _startTime.format(context),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Ora sfârșit',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.forestGreen,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () => _selectEndTime(),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.access_time,
                              color: AppColors.forestGreen,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _endTime.format(context),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Duration display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.forestGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.forestGreen.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.timer,
                  color: AppColors.forestGreen,
                ),
                const SizedBox(width: 12),
                Text(
                  'Durată: ${_formatDuration(_getDurationMinutes())}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.forestGreen,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build staff selection tab
  Widget _buildStaffTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selectează membrii echipei',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Timpul va fi blocat pentru membrii selectați',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          if (_isLoadingStaff) ...[
            const Center(
              child: CircularProgressIndicator(
                color: AppColors.forestGreen,
              ),
            ),
          ] else if (_availableStaff.isEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: Colors.orange,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Nu s-au găsit membri ai echipei disponibili',
                      style: TextStyle(color: Colors.orange),
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Staff list
            ...(_availableStaff.map((staff) {
              final isSelected = _selectedStaffIds.contains(staff.id);
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: CheckboxListTile(
                  value: isSelected,
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        if (!_selectedStaffIds.contains(staff.id)) {
                          _selectedStaffIds.add(staff.id);
                        }
                      } else {
                        _selectedStaffIds.remove(staff.id);
                      }
                    });
                  },
                  activeColor: AppColors.forestGreen,
                  title: Text(
                    staff.displayName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    staff.groomerRole.displayName,
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  secondary: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStaffColor(staff),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: isSelected
                        ? AppColors.forestGreen
                        : Colors.grey.shade300,
                    ),
                  ),
                  tileColor: isSelected
                    ? AppColors.forestGreen.withOpacity(0.1)
                    : null,
                ),
              );
            }).toList()),

            const SizedBox(height: 16),

            // Select all/none buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedStaffIds = _availableStaff.map((s) => s.id).toList();
                      });
                    },
                    icon: const Icon(Icons.select_all),
                    label: const Text('Selectează toți'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.forestGreen,
                      side: const BorderSide(color: AppColors.forestGreen),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedStaffIds.clear();
                      });
                    },
                    icon: const Icon(Icons.clear),
                    label: const Text('Deselectează'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey,
                      side: const BorderSide(color: Colors.grey),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build details tab with reason selection
  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Motivul blocării',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Selectează motivul pentru care blochezi timpul',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          // Reason chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _reasonCategories.map((reason) {
              final isSelected = _selectedReason == reason;
              return FilterChip(
                label: Text(reason),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedReason = reason;
                    if (reason != 'Altele') {
                      _customReason = '';
                    }
                  });
                },
                selectedColor: AppColors.forestGreen.withOpacity(0.2),
                checkmarkColor: AppColors.forestGreen,
                side: BorderSide(
                  color: isSelected
                    ? AppColors.forestGreen
                    : Colors.grey.shade300,
                ),
              );
            }).toList(),
          ),

          // Custom reason input
          if (_selectedReason == 'Altele') ...[
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Motivul personalizat',
                hintText: 'Introduceți motivul blocării...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.edit,
                  color: AppColors.forestGreen,
                ),
              ),
              maxLines: 2,
              onChanged: (value) {
                setState(() {
                  _customReason = value;
                });
              },
            ),
          ],

          const SizedBox(height: 24),

          // Summary card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(
                      Icons.summarize,
                      color: AppColors.forestGreen,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Rezumat blocaj',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.forestGreen,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildSummaryRow(
                  'Data:',
                  DateFormat('EEEE, dd MMMM yyyy', 'ro').format(_selectedDate),
                ),
                _buildSummaryRow(
                  'Interval:',
                  '${_startTime.format(context)} - ${_endTime.format(context)}',
                ),
                _buildSummaryRow(
                  'Durată:',
                  _formatDuration(_getDurationMinutes()),
                ),
                _buildSummaryRow(
                  'Echipă:',
                  _selectedStaffIds.isEmpty
                    ? 'Niciun membru selectat'
                    : '${_selectedStaffIds.length} ${_selectedStaffIds.length == 1 ? 'membru' : 'membri'}',
                ),
                _buildSummaryRow(
                  'Motiv:',
                  _selectedReason == 'Altele' && _customReason.isNotEmpty
                    ? _customReason
                    : _selectedReason,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary row for details tab
  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// Get staff color for visual identification
  Color _getStaffColor(StaffResponse staff) {
    final colors = [
      AppColors.forestGreen,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.indigo,
      Colors.pink,
    ];
    final index = staff.id.hashCode % colors.length;
    return colors[index.abs()];
  }

  /// Select date
  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ro', 'RO'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.forestGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// Select start time
  Future<void> _selectStartTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _startTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.forestGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startTime = picked;
        // Ensure end time is after start time
        if (_getDurationMinutes() <= 0) {
          _endTime = _addDurationToTime(_startTime, 60);
        }
      });
    }
  }

  /// Select end time
  Future<void> _selectEndTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _endTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.forestGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _endTime = picked;
        // Ensure end time is after start time
        if (_getDurationMinutes() <= 0) {
          _startTime = _addDurationToTime(_endTime, -60);
        }
      });
    }
  }

  /// Build footer with action buttons
  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: Colors.grey),
              ),
              child: const Text(
                'Anulează',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Save button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _validateAndSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.forestGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Blochează timp',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// Validate form and save block time
  Future<void> _validateAndSave() async {
    // Validation
    final validationError = _validateForm();
    if (validationError != null) {
      _showErrorSnackBar(validationError);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

      // Prepare block time data
      final startDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final endDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      final reason = _selectedReason == 'Altele' && _customReason.isNotEmpty
        ? _customReason
        : _selectedReason;

      // Check availability first
      final availability = await calendarProvider.checkBlockTimeAvailability(
        startTime: startDateTime,
        endTime: endDateTime,
        staffIds: _selectedStaffIds,
      );

      // Handle conflicts if any
      if (availability != null && availability['available'] == false) {
        final conflicts = availability['conflicts'] as List<dynamic>? ?? [];
        if (conflicts.isNotEmpty && mounted) {
          final shouldProceed = await _showConflictDialog(conflicts);
          if (!shouldProceed) {
            setState(() {
              _isLoading = false;
            });
            return;
          }
        }
      }

      // Proceed with blocking time for each selected staff member
      bool allSuccessful = true;
      int successCount = 0;

      for (final staffId in _selectedStaffIds) {
        final success = await calendarProvider.blockTime(
          startDateTime,
          endDateTime,
          reason,
          staffId: staffId,
        );
        if (success) {
          successCount++;
        } else {
          allSuccessful = false;
        }
      }

      if (mounted) {
        Navigator.of(context).pop();

        // Show success/error message
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            String message;
            Color backgroundColor;

            if (allSuccessful) {
              message = 'Timpul a fost blocat cu succes pentru ${_selectedStaffIds.length} ${_selectedStaffIds.length == 1 ? 'membru' : 'membri'}';
              backgroundColor = AppColors.forestGreen;
            } else if (successCount > 0) {
              message = 'Timpul a fost blocat pentru $successCount din ${_selectedStaffIds.length} membri. Verificați calendarul pentru detalii.';
              backgroundColor = Colors.orange;
            } else {
              message = 'Nu s-a putut bloca timpul pentru niciun membru. Verificați conflictele.';
              backgroundColor = Colors.red;
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: backgroundColor,
                duration: const Duration(seconds: 4),
              ),
            );
          }
        });
      }
    } catch (e) {
      debugPrint('❌ Error blocking time: $e');
      if (mounted) {
        _showErrorSnackBar('Eroare la blocarea timpului: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Validate form data
  String? _validateForm() {
    if (_selectedStaffIds.isEmpty) {
      return 'Selectați cel puțin un membru al echipei';
    }

    if (_getDurationMinutes() <= 0) {
      return 'Ora de sfârșit trebuie să fie după ora de început';
    }

    if (_getDurationMinutes() > 720) { // 12 hours
      return 'Durata nu poate fi mai mare de 12 ore';
    }

    if (_selectedReason == 'Altele' && _customReason.trim().isEmpty) {
      return 'Introduceți un motiv personalizat';
    }

    return null;
  }

  /// Show conflict dialog and return whether user wants to proceed
  Future<bool> _showConflictDialog(List<dynamic> conflicts) async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Conflicte detectate'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Există programări în intervalul selectat:',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 12),
                ...conflicts.map((conflict) {
                  final conflictDetails = conflict['conflictDetails'] as Map<String, dynamic>? ?? {};
                  final staffName = conflict['staffName'] as String? ?? 'Necunoscut';
                  final clientName = conflictDetails['clientName'] as String? ?? 'Client necunoscut';
                  final service = conflictDetails['service'] as String? ?? 'Serviciu necunoscut';

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          staffName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text('Client: $clientName'),
                        Text('Serviciu: $service'),
                      ],
                    ),
                  );
                }).toList(),
                const SizedBox(height: 16),
                const Text(
                  'Doriți să continuați cu blocarea timpului? Programările vor trebui reprogramate.',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Continuă oricum'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    });
  }
}