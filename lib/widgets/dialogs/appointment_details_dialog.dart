import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/appointment_service.dart';
import '../../services/error_handling_service.dart';
import '../../services/staff_service.dart';
import '../../services/url_launcher_service.dart';
import '../../config/theme/app_theme.dart';

class AppointmentDetailsDialog extends StatefulWidget {
  final Appointment appointment;

  const AppointmentDetailsDialog({
    super.key,
    required this.appointment,
  });

  @override
  State<AppointmentDetailsDialog> createState() => _AppointmentDetailsDialogState();
}

class _AppointmentDetailsDialogState extends State<AppointmentDetailsDialog> {
  @override
  void initState() {
    super.initState();
    // Fetch pet and client details after the widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      calendarProvider.getPetById(widget.appointment.petId);
      calendarProvider.getClientById(widget.appointment.clientId);
      calendarProvider.getPetsForClient(widget.appointment.clientId);
    });
  }

  @override
  Widget build(BuildContext context) {

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Icon(
                  _getServiceIcon(widget.appointment.service),
                  color: AppColors.forestGreen,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.appointment.service,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.forestGreen,
                        ),
                      ),
                      Text(
                        '${widget.appointment.clientName} - ${widget.appointment.petName}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(height: 24),

            // Content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Appointment Status Card
                    _buildStatusCard(),
                    const SizedBox(height: 16),

                    // Client Information Card (moved to top)
                    _buildClientInformationCard(),
                    const SizedBox(height: 16),

                    // Pet Information Card
                    _buildPetInformationCard(),
                    const SizedBox(height: 16),

                    // Service Details Card
                    _buildServiceDetailsCard(),
                    const SizedBox(height: 16),

                    // Groomer Information Card
                    _buildGroomerInformationCard(),
                    const SizedBox(height: 16),

                    // Notes Card
                    if (widget.appointment.notes.isNotEmpty)
                      _buildNotesCard(),
                  ],
                ),
              ),
            ),

            // Action buttons - only show if appointment is not canceled
            if (!_isAppointmentCanceled() || _isAppointmentCompleted()) ...[
              const Divider(height: 24),
              // Use Row for horizontal layout of icon-only buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton(
                    icon: Icons.schedule,
                    color: AppColors.forestGreen,
                    onPressed: widget.appointment.status == 'completed' ? null : () {
                      _showRescheduleDialog(context);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.cancel,
                    color: Colors.red,
                    onPressed: widget.appointment.status == 'completed' ? null : () {
                      _showCancelConfirmationDialog(context);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.check_circle,
                    color: widget.appointment.status == 'completed' ? Colors.grey : Colors.green,
                    onPressed: widget.appointment.status == 'completed' ? null : () {
                      _showCompleteConfirmationDialog(context);
                    },
                  ),
                ],
              ),
            ] else ...[
              // Show canceled status message instead of action buttons
              const Divider(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.cancel_outlined,
                      color: Colors.grey.shade600,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Această programare a fost anulată',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (widget.appointment.status) {
      case 'confirmed':
        statusColor = AppColors.forestGreen;
        statusIcon = Icons.check_circle;
        statusText = 'Confirmată';
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.access_time;
        statusText = 'În așteptare';
        break;
      case 'canceled':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'Anulată';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        statusText = widget.appointment.status;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(statusIcon, color: statusColor, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  Text(
                    '${DateFormat('dd MMMM yyyy', 'ro').format(widget.appointment.startTime)} • ${widget.appointment.timeRange}',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            ),
            if (widget.appointment.isPaid)
              const Icon(Icons.check_circle, color: Colors.green, size: 24)
            else
              const Icon(Icons.money_off, color: Colors.orange, size: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceDetailsCard() {
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        final serviceDetails = calendarProvider.getServiceDetails();
        final serviceDetail = serviceDetails[widget.appointment.service];
        final duration = serviceDetail?['duration'] ?? 60;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Detalii serviciu',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                _buildDetailRow('Serviciu:', widget.appointment.service),
                _buildDetailRow('Durata estimată:', '$duration minute'),
                _buildDetailRow('Ora început:', DateFormat('HH:mm').format(widget.appointment.startTime)),
                _buildDetailRow('Ora sfârșit:', DateFormat('HH:mm').format(widget.appointment.endTime)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGroomerInformationCard() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Find the staff member by groomerId first, then by name
        StaffResponse? staff;

        // First try to find by groomerId (most reliable)
        if (widget.appointment.groomerId != null && widget.appointment.groomerId!.isNotEmpty) {
          try {
            staff = provider.availableStaff.firstWhere(
              (s) => s.id == widget.appointment.groomerId,
            );
          } catch (e) {
            // Staff not found by ID, continue to name matching
          }
        }

        // Fallback to name matching if not found by ID
        if (staff == null) {
          String staffName;
          try {
            staffName = widget.appointment.assignedGroomer?.isNotEmpty == true
                ? widget.appointment.assignedGroomer!
                : 'Ana Popescu'; // Fallback if no staff assigned or if it's null
          } catch (e) {
            staffName = 'Ana Popescu'; // Fallback on any error
          }

          // Find the staff by name (check both full name and display name)
          try {
            staff = provider.availableStaff.firstWhere(
              (s) => s.name == staffName || s.displayName == staffName,
            );
          } catch (e) {
            // If staff not found, use first available staff
            staff = provider.availableStaff.isNotEmpty
              ? provider.availableStaff.first
              : null;
          }
        }



        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Personal asignat',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    if (staff != null)
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: provider.getStaffColor(staff.id),
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildDetailRow('Nume:', staff?.displayName ?? widget.appointment.assignedGroomer ?? 'Ana Popescu'),
                if (staff != null) ...[
                  // Show full name if different from display name
                  if (staff.nickname != null && staff.nickname!.isNotEmpty && staff.nickname != staff.name)
                    _buildDetailRow('Nume complet:', staff.name),
                  _buildDetailRow('Telefon:', staff.phone ?? 'Nu este disponibil'),
                  if (staff.specialties.isNotEmpty)
                    _buildDetailRow('Specializări:', staff.specialties.join(', ')),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPetInformationCard() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final pet = provider.petsCache[widget.appointment.petId];

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Informații animal',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    _getPetIcon(widget.appointment.petSpecies),
                  ],
                ),
                const SizedBox(height: 12),
                if (pet == null) ...[
                  _buildDetailRow('Nume:', widget.appointment.petName),
                  if (provider.isLoadingClientData)
                    const Center(child: CircularProgressIndicator(color: AppColors.forestGreen)),
                ] else ...[
                  _buildDetailRow('Nume:', pet.name),
                  _buildDetailRow('Rasă:', pet.breed),
                  _buildDetailRow('Sex:', pet.gender == 'male' ? 'Mascul' : 'Femelă'),
                  _buildDetailRow('Vârstă:', pet.age),
                  _buildDetailRow('Greutate:', '${pet.weight} kg'),
                  _buildDetailRow('Culoare:', pet.color),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildClientInformationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppColors.forestGreen, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Informații client',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                // Client avatar
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.forestGreen.withValues(alpha: 0.1),
                    border: Border.all(color: AppColors.forestGreen.withValues(alpha: 0.3)),
                  ),
                  child: Center(
                    child: Text(
                      widget.appointment.clientName.isNotEmpty
                          ? widget.appointment.clientName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: AppColors.forestGreen,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildDetailRow('Nume:', widget.appointment.clientName),
            _buildDetailRow('Telefon:', widget.appointment.clientPhone),
            const SizedBox(height: 16),
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: _buildClientActionButton(
                    icon: Icons.phone,
                    label: 'Sună',
                    color: Colors.green,
                    onPressed: () => _makePhoneCall(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildClientActionButton(
                    icon: Icons.message,
                    label: 'SMS',
                    color: Colors.blue,
                    onPressed: () => _sendSMS(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildClientActionButton(
                    icon: FontAwesomeIcons.whatsapp,
                    label: 'WhatsApp',
                    color: const Color(0xFF25D366),
                    onPressed: () => _openWhatsApp(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notițe serviciu',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(
              widget.appointment.notes,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500, color: Colors.grey),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        elevation: 0,
        minimumSize: const Size(0, 40),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(height: 2),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _showCancelConfirmationDialog(BuildContext context) {
    String cancellationReason = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Anulare programare'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Sunteți sigur că doriți să anulați această programare?'),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Motiv anulare (opțional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                onChanged: (value) => cancellationReason = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Nu'),
            ),
            ElevatedButton(
              onPressed: () => _handleCancel(context, cancellationReason),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Da, anulează'),
            ),
          ],
        );
      },
    );
  }

  void _handleCancel(BuildContext context, String reason) async {
    // Store references before closing dialog
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    navigator.pop(); // Close confirmation dialog

    try {
      final response = await AppointmentService.cancelAppointment(
        widget.appointment.id,
        reason: reason.isNotEmpty ? reason : null,
      );

      if (mounted) {
        if (response.success) {
          navigator.pop(); // Close details dialog
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Programarea a fost anulată cu succes'),
              backgroundColor: Colors.red,
            ),
          );
          // Force refresh calendar
          await calendarProvider.fetchAppointmentsForDate(widget.appointment.startTime, forceRefresh: true);
        } else {
          final errorMessage = response.error ?? 'Eroare necunoscută';
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Eroare la anularea programării: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandlingService.handleAppError(
          operation: 'cancel_appointment',
          error: e,
          screenName: 'AppointmentDetailsDialog',
        );
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Eroare la anularea programării: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  // Helper method to check if appointment is canceled
  bool _isAppointmentCanceled() {
    final status = widget.appointment.status.toLowerCase();
    return status == 'canceled' || status == 'anulat' || status == 'cancelled';
  }

  bool _isAppointmentCompleted() {
    final status = widget.appointment.status.toLowerCase();
    return status == 'completed' || status == 'finalizat' || status == 'finalized' || status == 'done';
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return IconButton(
      onPressed: onPressed,
      icon: Icon(icon, color: onPressed != null ? color : Colors.grey),
      iconSize: 28, // Slightly larger icon since no text
      padding: const EdgeInsets.all(12),
    );
  }

  IconData _getServiceIcon(String service) {
    switch (service) {
      case 'Baie completă':
        return Icons.bathtub;
      case 'Tuns și aranjat':
      case 'Tuns de vară':
        return Icons.content_cut;
      case 'Toaletare completă':
      case 'Pachet complet premium':
        return Icons.spa;
      case 'Tăiat unghii':
      case 'Îngrijire gheare':
        return Icons.cut;
      case 'Curățat urechi':
        return Icons.hearing;
      case 'Spălat dinți':
        return Icons.clean_hands;
      case 'Tratament anti-purici':
        return Icons.bug_report;
      case 'Aranjat blană lungă':
        return Icons.brush;
      case 'Masaj relaxant':
        return Icons.self_improvement;
      default:
        return Icons.pets;
    }
  }

  String _getPetSpeciesText(String species) {
    switch (species) {
      case 'dog':
        return 'Câine';
      case 'cat':
        return 'Pisică';
      case 'bird':
        return 'Pasăre';
      case 'rabbit':
        return 'Iepure';
      case 'rodent':
        return 'Rozătoare';
      case 'reptile':
        return 'Reptilă';
      case 'fish':
        return 'Pește';
      default:
        return species;
    }
  }

  Widget _getPetIcon(String species) {
    IconData iconData;
    Color iconColor;

    switch (species) {
      case 'dog':
        iconData = Icons.pets;
        iconColor = Colors.brown;
        break;
      case 'cat':
        iconData = Icons.pets;
        iconColor = Colors.grey;
        break;
      case 'bird':
        iconData = Icons.flutter_dash;
        iconColor = Colors.blue;
        break;
      default:
        iconData = Icons.pets;
        iconColor = AppColors.forestGreen;
    }

    return Icon(iconData, color: iconColor, size: 20);
  }

  void _showRescheduleDialog(BuildContext context) {
    DateTime selectedDate = widget.appointment.startTime;
    TimeOfDay selectedStartTime = TimeOfDay.fromDateTime(widget.appointment.startTime);
    TimeOfDay selectedEndTime = TimeOfDay.fromDateTime(widget.appointment.endTime);
    String rescheduleReason = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Reprogramează întâlnirea'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Date picker
                    ListTile(
                      leading: const Icon(Icons.calendar_today),
                      title: const Text('Data'),
                      subtitle: Text(DateFormat('dd MMMM yyyy', 'ro').format(selectedDate)),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: selectedDate,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        );
                        if (date != null) {
                          setState(() => selectedDate = date);
                        }
                      },
                    ),
                    // Start time picker
                    ListTile(
                      leading: const Icon(Icons.access_time),
                      title: const Text('Ora început'),
                      subtitle: Text(selectedStartTime.format(context)),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: selectedStartTime,
                        );
                        if (time != null) {
                          setState(() => selectedStartTime = time);
                        }
                      },
                    ),
                    // End time picker
                    ListTile(
                      leading: const Icon(Icons.access_time_filled),
                      title: const Text('Ora sfârșit'),
                      subtitle: Text(selectedEndTime.format(context)),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: selectedEndTime,
                        );
                        if (time != null) {
                          setState(() => selectedEndTime = time);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    // Reason field
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'Motiv reprogramare (opțional)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      onChanged: (value) => rescheduleReason = value,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Anulează'),
                ),
                ElevatedButton(
                  onPressed: () => _handleReschedule(context, selectedDate, selectedStartTime, selectedEndTime, rescheduleReason),
                  style: ElevatedButton.styleFrom(backgroundColor: AppColors.forestGreen),
                  child: const Text('Reprogramează'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _handleReschedule(BuildContext context, DateTime date, TimeOfDay startTime, TimeOfDay endTime, String reason) async {
    // Store provider reference and context before closing dialog
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    navigator.pop(); // Close reschedule dialog

    final newStartTime = DateTime(date.year, date.month, date.day, startTime.hour, startTime.minute);
    final newEndTime = DateTime(date.year, date.month, date.day, endTime.hour, endTime.minute);

    // Validate times
    if (newEndTime.isBefore(newStartTime) || newEndTime.isAtSameMomentAs(newStartTime)) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Ora de sfârșit trebuie să fie după ora de început'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      final response = await AppointmentService.rescheduleAppointment(
        widget.appointment.id,
        newStartTime: newStartTime,
        newEndTime: newEndTime,
        reason: reason.isNotEmpty ? reason : null,
      );

      if (mounted) {
        if (response.success) {
          navigator.pop(); // Close details dialog
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Programarea a fost reprogramată cu succes'),
              backgroundColor: Colors.green,
            ),
          );
          // Force refresh for both old and new dates
          final oldDate = widget.appointment.startTime;
          await calendarProvider.fetchAppointmentsForDate(date, forceRefresh: true);
          // Also refresh the old date if it's different
          if (oldDate.day != date.day || oldDate.month != date.month || oldDate.year != date.year) {
            await calendarProvider.fetchAppointmentsForDate(oldDate, forceRefresh: true);
          }
        } else {
          final errorMessage = response.error ?? 'Eroare necunoscută';
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Eroare la reprogramare: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandlingService.handleAppError(
          operation: 'reschedule_appointment',
          error: e,
          screenName: 'AppointmentDetailsDialog',
        );
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Eroare la reprogramare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCompleteConfirmationDialog(BuildContext context) {
    String completionNotes = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Completează programarea'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Marcați această programare ca fiind completată?'),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Notițe completare (opțional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                onChanged: (value) => completionNotes = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () => _handleComplete(context, completionNotes),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text('Completează'),
            ),
          ],
        );
      },
    );
  }

  void _handleComplete(BuildContext context, String notes) async {
    // Store references before closing dialog
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    navigator.pop(); // Close confirmation dialog

    try {
      final response = await AppointmentService.completeAppointment(
        widget.appointment.id,
        notes: notes.isNotEmpty ? notes : null,
      );

      if (mounted) {
        if (response.success) {
          navigator.pop(); // Close details dialog
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Programarea a fost marcată ca completată'),
              backgroundColor: Colors.green,
            ),
          );
          // Force refresh calendar
          await calendarProvider.fetchAppointmentsForDate(widget.appointment.startTime, forceRefresh: true);
        } else {
          final errorMessage = response.error ?? 'Eroare necunoscută';
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Eroare la completarea programării: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandlingService.handleAppError(
          operation: 'complete_appointment',
          error: e,
          screenName: 'AppointmentDetailsDialog',
        );
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Eroare la completarea programării: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Client action methods
  void _makePhoneCall() async {
    final success = await UrlLauncherService.makePhoneCall(widget.appointment.clientPhone);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'aplicația de telefon');
    }
  }

  void _sendSMS() async {
    final success = await UrlLauncherService.sendSMS(
      widget.appointment.clientPhone,
      message: 'Bună ziua ${widget.appointment.clientName}, ',
    );
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'aplicația de mesaje');
    }
  }

  void _openWhatsApp() async {
    final success = await UrlLauncherService.openWhatsApp(
      widget.appointment.clientPhone,
      message: 'Bună ziua ${widget.appointment.clientName}, ',
    );
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'WhatsApp');
    }
  }
}
