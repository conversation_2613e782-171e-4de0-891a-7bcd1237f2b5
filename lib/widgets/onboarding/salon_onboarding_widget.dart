import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../providers/role_provider.dart';
import '../../providers/calendar_provider.dart';
import '../../services/invitation_service.dart';
import '../../services/auth/auth_service.dart';
import '../../models/salon_invitation.dart';
import '../../screens/profile/salon_creation_screen.dart';
import '../../screens/profile/settings/services_management_screen.dart';
import '../../screens/profile/settings/profile_screen.dart';

/// Widget shown to users who don't have a salon association yet
class SalonOnboardingWidget extends StatefulWidget {
  const SalonOnboardingWidget({super.key});

  @override
  State<SalonOnboardingWidget> createState() => _SalonOnboardingWidgetState();
}

class _SalonOnboardingWidgetState extends State<SalonOnboardingWidget>
    with TickerProviderStateMixin {
  List<SalonInvitation> _pendingInvitations = [];
  bool _isLoadingInvitations = false;

  // Animation controllers for engaging animations
  late AnimationController _logoAnimationController;
  late AnimationController _cardAnimationController;
  late AnimationController _buttonAnimationController;

  // Animations
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<Offset> _cardSlideAnimation;
  late Animation<double> _cardFadeAnimation;
  late Animation<double> _buttonBounceAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadPendingInvitations();
  }

  /// Initialize all animations for engaging onboarding experience
  void _initializeAnimations() {
    // Logo animation controller (continuous rotation and scale)
    _logoAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    // Card animation controller (slide in from bottom)
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Button animation controller (bounce effect)
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Logo animations
    _logoScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _logoAnimationController,
      curve: Curves.easeInOut,
    ));

    _logoRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _logoAnimationController,
      curve: Curves.easeInOut,
    ));

    // Card animations
    _cardSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.elasticOut,
    ));

    _cardFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.easeInOut,
    ));

    // Button bounce animation
    _buttonBounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _buttonAnimationController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _logoAnimationController.repeat(reverse: true);
    _cardAnimationController.forward();

    // Delay button animation for staggered effect
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        _buttonAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _logoAnimationController.dispose();
    _cardAnimationController.dispose();
    _buttonAnimationController.dispose();
    super.dispose();
  }

  /// Load pending invitations for the user
  Future<void> _loadPendingInvitations() async {
    setState(() {
      _isLoadingInvitations = true;
    });

    try {
      final response = await InvitationService.getPendingInvitations();
      if (response.success && response.data != null) {
        setState(() {
          _pendingInvitations = response.data!;
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading pending invitations: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingInvitations = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Welcome Card with animations
          SlideTransition(
            position: _cardSlideAnimation,
            child: FadeTransition(
              opacity: _cardFadeAnimation,
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(28),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      colors: [
                        AppColors.forestGreen.withValues(alpha: 0.15),
                        AppColors.forestGreen.withValues(alpha: 0.08),
                        Colors.white,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      stops: const [0.0, 0.3, 1.0],
                    ),
                  ),
                  child: Column(
                    children: [
                      // Animated Animalia Logo
                      AnimatedBuilder(
                        animation: _logoAnimationController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _logoScaleAnimation.value,
                            child: Transform.rotate(
                              angle: _logoRotationAnimation.value,
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(60),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.forestGreen.withValues(alpha: 0.2),
                                      blurRadius: 15,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(50),
                                  child: Image.asset(
                                    'assets/images/logo-no_bg.png',
                                    width: 64,
                                    height: 64,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 20),

                      // Animated Welcome Text
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1000),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: Column(
                                children: [
                                  ShaderMask(
                                    shaderCallback: (bounds) => LinearGradient(
                                      colors: [
                                        AppColors.forestGreen,
                                        AppColors.forestGreen.withValues(alpha: 0.8),
                                      ],
                                    ).createShader(bounds),
                                    child: const Text(
                                      '🎉 Bine ai venit în Animalia! 🎉',
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    _pendingInvitations.isNotEmpty
                                        ? '✨ Ai invitații în așteptare! Poți să te alături unui salon existent sau să îți creezi propriul salon de toaletaj.'
                                        : '🚀 Pentru a începe să folosești aplicația, trebuie să îți creezi propriul salon de toaletaj.',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey.shade700,
                                      height: 1.5,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 24),

                      // Animated Features List
                      ..._buildAnimatedFeatures(),

                      const SizedBox(height: 28),

                      // Animated Action Buttons Section
                      ScaleTransition(
                        scale: _buttonBounceAnimation,
                        child: _buildActionButtons(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Enhanced Info Card with animation
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1200),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Transform.translate(
                  offset: Offset(0, 30 * (1 - value)),
                  child: Card(
                    elevation: 4,
                    color: Colors.blue.shade50,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.lightbulb_outline,
                              color: Colors.blue.shade700,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '💡 Primul pas important',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                                const SizedBox(height: 6),
                                Text(
                                  'După crearea salonului, vei deveni proprietarul și groomer șef. Vei avea acces complet la toate funcționalitățile aplicației.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.blue.shade600,
                                    height: 1.4,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // Bottom padding for safe area
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  /// Build action buttons section (invitations + create salon)
  Widget _buildActionButtons() {
    return Column(
      children: [
        // Pending Invitations Section
        if (_isLoadingInvitations)
          _buildLoadingInvitations()
        else if (_pendingInvitations.isNotEmpty)
          _buildPendingInvitations(),

        // Spacing between invitations and create button
        if (_pendingInvitations.isNotEmpty) const SizedBox(height: 16),

        // Create Salon Button
        _buildCreateSalonButton(),
      ],
    );
  }

  /// Build loading state for invitations
  Widget _buildLoadingInvitations() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 12),
            Text(
              'Se încarcă invitațiile...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build pending invitations list
  Widget _buildPendingInvitations() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            Icon(
              Icons.mail_outline,
              color: AppColors.forestGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Invitații primite (${_pendingInvitations.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.forestGreen,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Invitations List
        ...(_pendingInvitations.take(3).map((invitation) =>
          _buildInvitationCard(invitation)
        ).toList()),

        // Show more indicator if there are more than 3 invitations
        if (_pendingInvitations.length > 3)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'și încă ${_pendingInvitations.length - 3} invitații...',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  /// Build individual invitation card
  Widget _buildInvitationCard(SalonInvitation invitation) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Salon name and role
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitation.salonName,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: AppColors.forestGreen,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Rol: ${invitation.proposedRole.displayName}',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    invitation.status.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),

            // Invited by
            Text(
              'Invitat de: ${invitation.invitedByName}',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineInvitation(invitation),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Refuză'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptInvitation(invitation),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.forestGreen,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Acceptă'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build create salon button
  Widget _buildCreateSalonButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _navigateToCreateSalon(context),
        icon: const Icon(Icons.add_business),
        label: Text(
          _pendingInvitations.isNotEmpty
              ? 'Sau creează propriul salon'
              : 'Creează Salon Nou',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: _pendingInvitations.isNotEmpty
              ? Colors.grey.shade600
              : AppColors.forestGreen,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 3,
        ),
      ),
    );
  }

  /// Build animated features list with staggered animations
  List<Widget> _buildAnimatedFeatures() {
    final features = [
      {
        'icon': Icons.calendar_today,
        'title': '📅 Gestionează programările',
        'description': 'Organizează-ți calendarul și programările clienților',
        'delay': 0,
      },
      {
        'icon': Icons.people,
        'title': '👥 Administrează clienții',
        'description': 'Ține evidența clienților și animalelor lor',
        'delay': 200,
      },
      {
        'icon': Icons.group,
        'title': '🤝 Invită echipa',
        'description': 'Adaugă alți groomeri în echipa ta',
        'delay': 400,
      },
    ];

    return features.map((feature) {
      return TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 800 + (feature['delay'] as int)),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Opacity(
            opacity: value,
            child: Transform.translate(
              offset: Offset(50 * (1 - value), 0),
              child: Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: _buildFeatureItem(
                  feature['icon'] as IconData,
                  feature['title'] as String,
                  feature['description'] as String,
                ),
              ),
            ),
          );
        },
      );
    }).toList();
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.forestGreen.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.forestGreen.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.forestGreen.withValues(alpha: 0.2),
                  AppColors.forestGreen.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppColors.forestGreen,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppColors.forestGreen,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade600,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to salon creation screen
  Future<void> _navigateToCreateSalon(BuildContext context) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const SalonCreationScreen(),
      ),
    );

    if (result == true && context.mounted) {
      // Salon was created successfully, refresh all providers
      final roleProvider = context.read<RoleProvider>();
      final calendarProvider = context.read<CalendarProvider>();

      // Refresh role provider first
      debugPrint('🔄 Refreshing role provider after salon creation...');
      await roleProvider.refresh();

      // Add a longer delay to ensure backend has processed the staff creation
      debugPrint('🔄 Waiting for backend to process staff creation...');
      await Future.delayed(const Duration(seconds: 2));

      // Retry staff loading with multiple attempts
      if (context.mounted) {
        await _retryStaffLoading(calendarProvider);
        await calendarProvider.loadServices();
      }

      // go to calendar screen
      Navigator.of(context).pushNamed('/calendar');

      debugPrint('✅ Salon created successfully - triggering navigation to calendar');

      // The salon creation was successful, and roleProvider.refresh() was called
      // The MainLayout Consumer will automatically detect the salon association change
      // and switch to the calendar tab due to the state tracking logic
      debugPrint('🔄 MainLayout will automatically switch to Calendar tab');
    }
  }

  /// Accept salon invitation
  Future<void> _acceptInvitation(SalonInvitation invitation) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final response = await InvitationService.acceptInvitation(invitation.id);

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      if (response.success && mounted) {
        // Refresh role provider to update salon association
        final roleProvider = context.read<RoleProvider>();
        final calendarProvider = context.read<CalendarProvider>();

        debugPrint('🔄 Refreshing role provider after accepting invitation...');
        await roleProvider.refresh();

        // Add delay for backend processing
        await Future.delayed(const Duration(seconds: 2));

        // Load calendar data
        if (mounted) {
          await _retryStaffLoading(calendarProvider);
          await calendarProvider.loadServices();
        }

        // Remove invitation from list since it was accepted
        setState(() {
          _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
        });

        debugPrint('✅ Successfully joined salon: ${invitation.salonName}');

        // The invitation was accepted successfully, and roleProvider.refresh() was called
        // The MainLayout will automatically detect the new salon association and switch to calendar
        debugPrint('🔄 MainLayout will automatically switch to Calendar tab');
      } else {
        debugPrint('❌ Failed to accept invitation: ${response.error}');
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();

      debugPrint('❌ Error accepting invitation: $e');
    }
  }

  /// Decline salon invitation
  Future<void> _declineInvitation(SalonInvitation invitation) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Refuzi invitația?'),
        content: Text(
          'Ești sigur că vrei să refuzi invitația de la ${invitation.salonName}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Anulează'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Refuză'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await InvitationService.declineInvitation(invitation.id);

        if (response.success) {
          // Remove invitation from list
          setState(() {
            _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
          });

          debugPrint('✅ Successfully declined invitation from ${invitation.salonName}');
        } else {
          debugPrint('❌ Failed to decline invitation: ${response.error}');
        }
      } catch (e) {
        debugPrint('❌ Error declining invitation: $e');
      }
    }
  }

  /// Retry staff loading with multiple attempts
  Future<void> _retryStaffLoading(CalendarProvider calendarProvider) async {
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(seconds: 1);

    // Check current salon ID before attempting to load staff
    final currentSalonId = await AuthService.getCurrentSalonId();
    debugPrint('🔍 Current salon ID before staff loading: $currentSalonId');

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      debugPrint('🔄 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        debugPrint('✅ Staff loaded successfully on attempt $attempt: ${calendarProvider.availableStaff.length} staff members');
        return;
      }

      if (attempt < maxAttempts) {
        debugPrint('⏳ No staff found, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    debugPrint('❌ Failed to load staff after $maxAttempts attempts');
    debugPrint('❌ Final salon ID check: ${await AuthService.getCurrentSalonId()}');
  }


}
