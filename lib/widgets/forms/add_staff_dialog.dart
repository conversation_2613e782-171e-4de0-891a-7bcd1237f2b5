import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/user_role.dart';
import '../../services/staff_service.dart';
import '../../config/theme/app_theme.dart';
import '../../utils/formatters/phone_number_utils.dart';

class AddStaffDialog extends StatefulWidget {
  final VoidCallback? onSuccess;

  const AddStaffDialog({
    super.key,
    this.onSuccess,
  });

  @override
  State<AddStaffDialog> createState() => _AddStaffDialogState();
}

class _AddStaffDialogState extends State<AddStaffDialog> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _nicknameController = TextEditingController();
  final _notesController = TextEditingController();

  GroomerRole _selectedRole = GroomerRole.groomer;
  ClientDataPermission _selectedPermission = ClientDataPermission.limitedAccess;
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _nicknameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.person_add,
                  color: AppColors.forestGreen,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Adaugă Membru în Echipă',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPhoneSection(),
                      const SizedBox(height: 24),
                      _buildNicknameSection(),
                      const SizedBox(height: 24),
                      _buildRoleSection(),
                      const SizedBox(height: 24),
                      _buildPermissionsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            // Footer
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Anulează'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleSave,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.forestGreen,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('Adaugă'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhoneSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Număr de telefon',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[\d\s\+\-\(\)]')),
            _PhoneNumberFormatter(),
          ],
          decoration: const InputDecoration(
            hintText: '+40 XXX XXX XXX',
            prefixIcon: Icon(Icons.phone, color: AppColors.forestGreen),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.forestGreen),
            ),
            helperText: 'Acceptă: +40 XXX XXX XXX, 0XXX XXX XXX, XXXXXXXXX',
          ),
          validator: (value) {
            return PhoneNumberUtils.getValidationError(value ?? '');
          },
          onChanged: (value) {
            // Auto-format as user types
            if (value.isNotEmpty && PhoneNumberUtils.needsFormatting(value)) {
              final formatted = PhoneNumberUtils.formatForInput(value);
              if (formatted != value) {
                _phoneController.value = TextEditingValue(
                  text: formatted,
                  selection: TextSelection.collapsed(offset: formatted.length),
                );
              }
            }
          },
        ),
        const SizedBox(height: 8),
        Text(
          'Persoana cu acest număr de telefon va primi o invitație să se alăture echipei.',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildNicknameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Nume afișat (opțional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nicknameController,
          decoration: const InputDecoration(
            hintText: 'ex: Ana, Mihai, Dr. Popescu',
            prefixIcon: Icon(Icons.badge, color: AppColors.forestGreen),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.forestGreen),
            ),
            helperText: 'Numele care va apărea în programări și calendar',
          ),
          validator: (value) {
            // Nickname is optional, but if provided, should be reasonable length
            if (value != null && value.trim().isNotEmpty) {
              if (value.trim().length < 2) {
                return 'Numele afișat trebuie să aibă cel puțin 2 caractere';
              }
              if (value.trim().length > 50) {
                return 'Numele afișat nu poate depăși 50 de caractere';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Text(
          'Dacă nu completezi, se va folosi numele complet din contul utilizatorului.',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildRoleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Rol în echipă',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...GroomerRole.values
            .where((role) => role != GroomerRole.regularGroomer) // Exclude deprecated role
            .map((role) => RadioListTile<GroomerRole>(
          title: Text(role.displayName),
          subtitle: Text(role.description),
          value: role,
          groupValue: _selectedRole,
          activeColor: AppColors.forestGreen,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedRole = value;
                // Auto-adjust permissions based on role
                if (value.hasManagementAccess) {
                  _selectedPermission = ClientDataPermission.fullAccess;
                }
              });
            }
          },
        )),
      ],
    );
  }

  Widget _buildPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Permisiuni acces date clienți',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...ClientDataPermission.values.map((permission) => RadioListTile<ClientDataPermission>(
          title: Text(permission.displayName),
          subtitle: Text(permission.description),
          value: permission,
          groupValue: _selectedPermission,
          activeColor: AppColors.forestGreen,
          onChanged: _selectedRole.hasManagementAccess
              ? null // Chief groomers always have full access
              : (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPermission = value;
                    });
                  }
                },
        )),
        if (_selectedRole.hasManagementAccess) ...[
          const SizedBox(height: 8),
          Text(
            'Groomer Șef are automat acces complet la datele clienților.',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Note (opțional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Note despre acest membru al echipei...',
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.forestGreen),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = AddStaffRequest.fromInput(
        phoneNumber: _phoneController.text.trim(),
        nickname: _nicknameController.text.trim().isNotEmpty
            ? _nicknameController.text.trim()
            : null,
        groomerRole: _selectedRole,
        clientDataPermission: _selectedPermission,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      final response = await StaffService.addStaffToCurrentSalon(request);

      if (response.success) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Invitația a fost trimisă cu succes către ${_phoneController.text.trim()}'),
              backgroundColor: AppColors.forestGreen,
            ),
          );
          widget.onSuccess?.call();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Nu s-a putut trimite invitația'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// Custom phone number formatter for Romanian numbers
class _PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Don't format if user is deleting
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    // Don't format if text is empty
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Apply formatting
    final formatted = PhoneNumberUtils.formatForInput(newValue.text);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
