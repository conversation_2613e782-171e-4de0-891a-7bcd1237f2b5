import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../config/theme/app_theme.dart';
import '../../services/sms_verification_service.dart';
import '../../utils/formatters/phone_number_utils.dart';

/// SMS verification widget with OTP input and resend functionality
class SmsVerificationWidget extends StatefulWidget {
  final String phoneNumber;
  final Function(String code) onCodeEntered;
  final VoidCallback? onVerificationSuccess;
  final Function(String error)? onError;
  final bool autoVerify;

  const SmsVerificationWidget({
    Key? key,
    required this.phoneNumber,
    required this.onCodeEntered,
    this.onVerificationSuccess,
    this.onError,
    this.autoVerify = true,
  }) : super(key: key);

  @override
  State<SmsVerificationWidget> createState() => _SmsVerificationWidgetState();
}

class _SmsVerificationWidgetState extends State<SmsVerificationWidget> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;
  bool _isResending = false;
  int _resendCooldown = 0;
  String _currentCode = '';

  @override
  void initState() {
    super.initState();
    _startResendCooldownTimer();
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  void _startResendCooldownTimer() {
    final remainingTime = SmsVerificationService.getRemainingCooldownTime(widget.phoneNumber);
    if (remainingTime > 0) {
      setState(() {
        _resendCooldown = remainingTime;
      });
      _updateCooldownTimer();
    }
  }

  void _updateCooldownTimer() {
    if (_resendCooldown > 0) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          setState(() {
            _resendCooldown--;
          });
          _updateCooldownTimer();
        }
      });
    }
  }

  Future<void> _verifyCode(String code) async {
    if (code.length != 6) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await SmsVerificationService.verifyCode(widget.phoneNumber, code);
      
      if (response.success && response.data?.verified == true) {
        widget.onVerificationSuccess?.call();
      } else {
        widget.onError?.call(response.error ?? 'Verificarea a eșuat');
        // Clear the code on error
        _codeController.clear();
        setState(() {
          _currentCode = '';
        });
      }
    } catch (e) {
      widget.onError?.call('Eroare la verificarea codului: $e');
      _codeController.clear();
      setState(() {
        _currentCode = '';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resendCode() async {
    if (!SmsVerificationService.canResend(widget.phoneNumber)) {
      return;
    }

    setState(() {
      _isResending = true;
    });

    try {
      final response = await SmsVerificationService.sendVerificationCode(widget.phoneNumber);
      
      if (response.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Codul a fost retrimis cu succes!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: Duration(seconds: 2),
          ),
        );
        _startResendCooldownTimer();
      } else {
        widget.onError?.call(response.error ?? 'Eroare la retrimirea codului');
      }
    } catch (e) {
      widget.onError?.call('Eroare la retrimirea codului: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final normalizedPhone = PhoneNumberUtils.normalizePhoneNumber(widget.phoneNumber);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header
        Icon(
          Icons.sms,
          size: 64,
          color: Theme.of(context).colorScheme.primary,
        ),
        SizedBox(height: 16),
        
        Text(
          'Verificare SMS',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        SizedBox(height: 8),
        
        Text(
          'Am trimis un cod de verificare la',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4),
        
        Text(
          normalizedPhone,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        SizedBox(height: 32),
        
        // PIN Code input
        PinCodeTextField(
          appContext: context,
          length: 6,
          obscureText: false,
          animationType: AnimationType.fade,
          pinTheme: PinTheme(
            shape: PinCodeFieldShape.box,
            borderRadius: BorderRadius.circular(8),
            fieldHeight: 50,
            fieldWidth: 40,
            activeFillColor: AppColors.softIvory,
            inactiveFillColor: AppColors.softIvory,
            selectedFillColor: AppColors.peach,
            activeColor: Theme.of(context).colorScheme.primary,
            inactiveColor: AppColors.taupe,
            selectedColor: Theme.of(context).colorScheme.primary,
          ),
          animationDuration: const Duration(milliseconds: 300),
          backgroundColor: Colors.transparent,
          enableActiveFill: true,
          controller: _codeController,
          keyboardType: TextInputType.number,
          onCompleted: (code) {
            _currentCode = code;
            widget.onCodeEntered(code);
            if (widget.autoVerify) {
              _verifyCode(code);
            }
          },
          onChanged: (value) {
            setState(() {
              _currentCode = value;
            });
          },
        ),
        SizedBox(height: 24),
        
        // Loading indicator
        if (_isLoading)
          const CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          )
        else ...[
          // Manual verify button (if auto-verify is disabled)
          if (!widget.autoVerify && _currentCode.length == 6)
            ElevatedButton(
              onPressed: () => _verifyCode(_currentCode),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              ),
              child: Text('Verifică Codul'),
            ),
          
          SizedBox(height: 16),
          
          // Resend code section
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Nu ai primit codul? ',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              if (_resendCooldown > 0)
                Text(
                  'Retrimite în ${_resendCooldown}s',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade400,
                  ),
                )
              else
                GestureDetector(
                  onTap: _isResending ? null : _resendCode,
                  child: Text(
                    _isResending ? 'Se retrimite...' : 'Retrimite',
                    style: TextStyle(
                      fontSize: 14,
                      color: _isResending ? Colors.grey.shade400 : Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      decoration: _isResending ? null : TextDecoration.underline,
                    ),
                  ),
                ),
            ],
          ),
        ],
        
        SizedBox(height: 24),
        
        // Help text
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
               Icon(Icons.info_outline, color: Colors.blue, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Codul este valabil 5 minute. Verificați și folderul de spam.',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
