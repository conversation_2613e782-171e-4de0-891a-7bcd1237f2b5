import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/role_provider.dart';
import '../screens/auth/login_screen.dart';
import '../screens/main_layout.dart';
import '../config/theme/app_theme.dart';

/// Authentication wrapper that handles routing based on authentication state
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitializing = true;
  String? _initializationError;

  @override
  void initState() {
    super.initState();
    // Initialize authentication state when the widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAuth();
    });
  }

  Future<void> _initializeAuth() async {
    try {
      final authProvider = context.read<AuthProvider>();
      final roleProvider = context.read<RoleProvider>();

      // Set role provider reference in auth provider for future role initialization
      authProvider.setRoleProvider(roleProvider);

      // Initialize authentication first
      await authProvider.initialize();

      // Initialize roles only if user is authenticated
      if (authProvider.isAuthenticated) {
        debugPrint('🔄 AuthWrapper: User authenticated, initializing roles...');
        await roleProvider.initialize();
        debugPrint('✅ AuthWrapper: Role initialization completed');
      } else {
        debugPrint('🔍 AuthWrapper: User not authenticated, clearing roles');
        roleProvider.clear();
      }

      // Mark initialization as complete
      if (mounted) {
        setState(() {
          _isInitializing = false;
          _initializationError = null;
        });
      }
    } catch (e) {
      debugPrint('❌ AuthWrapper: Error during initialization: $e');
      if (mounted) {
        setState(() {
          _isInitializing = false;
          _initializationError = 'Initialization error: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show initialization loading screen if still initializing
    if (_isInitializing) {
      return  Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
              ),
              SizedBox(height: 16),
              Text(
                'Inițializare aplicație...',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.taupe,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Show initialization error if present
    if (_initializationError != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'Eroare la inițializare',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _initializationError!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.taupe,
                  ),
                ),
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isInitializing = true;
                    _initializationError = null;
                  });
                  _initializeAuth();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  
                ),
                child: Text('Încearcă din nou'),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.status) {
          case AuthStatus.initial:
          case AuthStatus.authenticating:
            // Show loading screen while checking authentication status
            return  Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Verificare autentificare...',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.taupe,
                      ),
                    ),
                  ],
                ),
              ),
            );

          case AuthStatus.authenticated:
            // User is authenticated and roles are initialized, show main app
            debugPrint('✅ AuthWrapper: Showing MainLayout - user authenticated and roles initialized');
            return const MainLayout();

          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            // Show any error message if present
            if (authProvider.error != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(authProvider.error!),
                    backgroundColor: Colors.orange,
                    duration: const Duration(seconds: 3),
                  ),
                );
                authProvider.clearError();
              });
            }
            // User is not authenticated or there was an error, show login screen
            return const LoginScreen();
        }
      },
    );
  }
}
