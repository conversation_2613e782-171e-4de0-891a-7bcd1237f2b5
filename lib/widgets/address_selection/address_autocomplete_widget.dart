import 'package:flutter/material.dart';
import 'package:google_places_flutter/google_places_flutter.dart';
import 'package:google_places_flutter/model/prediction.dart';
import '../../config/theme/app_theme.dart';

/// Enhanced address selection widget with Google Places autocomplete
class AddressAutocompleteWidget extends StatefulWidget {
  final TextEditingController controller;
  final String? label;
  final String? hint;
  final IconData? icon;
  final FocusNode? focusNode;
  final String? Function(String?)? validator;
  final Function(String address, double? latitude, double? longitude)? onAddressSelected;
  final String? googleApiKey;

  const AddressAutocompleteWidget({
    Key? key,
    required this.controller,
    this.label,
    this.hint,
    this.icon,
    this.focusNode,
    this.validator,
    this.onAddressSelected,
    this.googleApiKey,
  }) : super(key: key);

  @override
  State<AddressAutocompleteWidget> createState() => _AddressAutocompleteWidgetState();
}

class _AddressAutocompleteWidgetState extends State<AddressAutocompleteWidget> {
  bool _isManualEntry = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        // Toggle between autocomplete and manual entry
        Row(
          children: [
            Expanded(
              child: _buildGooglePlacesField(),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                setState(() {
                  _isManualEntry = !_isManualEntry;
                });
              },
              icon: Icon(
                _isManualEntry ? Icons.search : Icons.edit,
                color: AppColors.forestGreen,
              ),
              tooltip: _isManualEntry 
                  ? 'Căutare automată'
                  : 'Introducere manuală',
            ),
          ],
        ),
        
        if (!_isManualEntry) ...[
          const SizedBox(height: 8),
          Text(
            'Apasă pe iconița de editare pentru introducere manuală',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildGooglePlacesField() {
    return GooglePlaceAutoCompleteTextField(
      textEditingController: widget.controller,
      focusNode: widget.focusNode,
      googleAPIKey: widget.googleApiKey!,
      inputDecoration: InputDecoration(
        hintText: widget.hint ?? 'Căutați adresa...',
        prefixIcon: widget.icon != null 
            ? Icon(widget.icon, color: AppColors.forestGreen)
            : const Icon(Icons.location_on, color: AppColors.forestGreen),
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.forestGreen),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        errorBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      debounceTime: 800,
      countries: const ["ro"], // Restrict to Romania
      isLatLngRequired: true,
      getPlaceDetailWithLatLng: (Prediction prediction) {
        // Handle place selection with coordinates
        if (widget.onAddressSelected != null) {
          widget.onAddressSelected!(
            prediction.description ?? '',
            double.tryParse(prediction.lat ?? ''),
            double.tryParse(prediction.lng ?? ''),
          );
        }
        // Prevent focus from jumping to other fields
        Future.microtask(() {
          if (widget.focusNode != null && widget.focusNode!.canRequestFocus) {
            widget.focusNode!.unfocus();
          }
        });
      },
      itemClick: (Prediction prediction) {
        widget.controller.text = prediction.description ?? '';
        widget.controller.selection = TextSelection.fromPosition(
          TextPosition(offset: widget.controller.text.length),
        );
        // Prevent focus issues by unfocusing after selection
        Future.microtask(() {
          if (widget.focusNode != null && widget.focusNode!.canRequestFocus) {
            widget.focusNode!.unfocus();
          }
        });
      },
      seperatedBuilder: const Divider(height: 1),
      containerHorizontalPadding: 16,
      itemBuilder: (context, index, Prediction prediction) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(
                Icons.location_on,
                color: AppColors.forestGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      prediction.structuredFormatting?.mainText ?? '',
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    if (prediction.structuredFormatting?.secondaryText != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        prediction.structuredFormatting!.secondaryText!,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
      isCrossBtnShown: true,
    );
  }
}
