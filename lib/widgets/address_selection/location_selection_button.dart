import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../config/theme/app_theme.dart';
import 'full_screen_map_picker.dart';

/// A button widget for selecting location that opens the full-screen map picker
class LocationSelectionButton extends StatelessWidget {
  final String? selectedAddress;
  final LatLng? selectedLocation;
  final Function(LatLng location, String? address)? onLocationSelected;
  final String? label;
  final String? hint;
  final bool isRequired;
  final String? Function(String?)? validator;
  final bool showReminderDisclaimer;

  const LocationSelectionButton({
    super.key,
    this.selectedAddress,
    this.selectedLocation,
    this.onLocationSelected,
    this.label,
    this.hint,
    this.isRequired = false,
    this.validator,
    this.showReminderDisclaimer = false,
  });

  @override
  Widget build(BuildContext context) {
    final hasSelection = selectedAddress != null && selectedAddress!.isNotEmpty;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (label != null) ...[
          Text(
            isRequired ? '$label *' : label!,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8),
        ],
        
        // Selection button
        InkWell(
          onTap: () => _openLocationPicker(context),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: hasSelection 
                    ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                    : Colors.grey.shade300,
                width: hasSelection ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: hasSelection 
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    hasSelection ? Icons.location_on : Icons.add_location,
                    color: hasSelection 
                        ? Theme.of(context).colorScheme.primary 
                        : Colors.grey.shade600,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                
                // Text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hasSelection ? 'Locația selectată' : 'Selectați locația',
                        style: TextStyle(
                          fontSize: 12,
                          color: hasSelection 
                              ? Theme.of(context).colorScheme.primary 
                              : Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        hasSelection 
                            ? selectedAddress! 
                            : (hint ?? 'Apăsați pentru a selecta locația'),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: hasSelection ? FontWeight.w600 : FontWeight.w400,
                          color: hasSelection 
                              ? Colors.black87 
                              : Colors.grey.shade500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        
        // Validation error
        if (validator != null) ...[
          SizedBox(height: 4),
          Builder(
            builder: (context) {
              final error = validator!(selectedAddress);
              if (error != null) {
                return Text(
                  error,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                );
              }
              return SizedBox.shrink();
            },
          ),
        ],

        // Reminder disclaimer
        if (showReminderDisclaimer) ...[
          SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.blue.shade200,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade600,
                  size: 16,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Această adresă va fi folosită în mesajele de reminder trimise clienților',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _openLocationPicker(BuildContext context) async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => FullScreenMapPicker(
          initialLocation: selectedLocation,
          initialAddress: selectedAddress,
          onLocationSelected: onLocationSelected,
        ),
      ),
    );

    if (result != null && onLocationSelected != null) {
      final location = result['location'] as LatLng?;
      final address = result['address'] as String?;
      
      if (location != null) {
        onLocationSelected!(location, address);
      }
    }
  }
}
