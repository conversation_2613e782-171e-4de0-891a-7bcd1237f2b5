import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../services/staff_service.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/theme_provider.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_block.dart';
import 'block_time_block.dart';
import 'time_slot.dart';
import '../dialogs/block_time_dialog.dart';

class WeekView extends StatefulWidget {
  final DateTime selectedWeek;
  final Map<DateTime, List<Appointment>> appointmentsByDay;
  final Function(Appointment) onAppointmentTap;
  final Function(Map<String, dynamic>)? onBlockTap;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final Function(DateTime)? onDayTap;
  final Map<String, dynamic> businessHours;
  final VoidCallback? onSwipeToNext;
  final VoidCallback? onSwipeToPrevious;

  const WeekView({
    super.key,
    required this.selectedWeek,
    required this.appointmentsByDay,
    required this.onAppointmentTap,
    this.onBlockTap,
    this.onTimeSlotTap,
    this.onDayTap,
    required this.businessHours,
    this.onSwipeToNext,
    this.onSwipeToPrevious,
  });

  @override
  State<WeekView> createState() => _WeekViewState();
}

class _WeekViewState extends State<WeekView> {
  ScrollController? _horizontalScrollController;
  ScrollController? _verticalScrollController;
  ScrollController? _timeVerticalScrollController;

  // Swipe gesture detection variables
  double? _swipeStartX;
  double? _swipeStartY;
  bool _isHorizontalScrolling = false;

  // Swipe thresholds
  static const double _minSwipeDistance = 100.0;
  static const double _minSwipeVelocity = 300.0;
  static const double _maxVerticalDeviation = 50.0;

  @override
  void initState() {
    super.initState();
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _timeVerticalScrollController = ScrollController();

    // Synchronize the vertical scroll controllers
    _verticalScrollController?.addListener(_syncVerticalScroll);
    _timeVerticalScrollController?.addListener(_syncTimeScroll);

    // Listen to horizontal scroll to detect when user is scrolling staff columns
    _horizontalScrollController?.addListener(() {
      if (_horizontalScrollController?.position.isScrollingNotifier.value == true) {
        _isHorizontalScrolling = true;
      }
    });
  }

  void _syncVerticalScroll() {
    if (_timeVerticalScrollController != null &&
        _verticalScrollController != null &&
        _timeVerticalScrollController!.hasClients &&
        _verticalScrollController!.hasClients) {
      _timeVerticalScrollController!.jumpTo(_verticalScrollController!.offset);
    }
  }

  void _syncTimeScroll() {
    // Time scroll is disabled, so this method is not needed anymore
    // but kept for compatibility
  }

  @override
  void dispose() {
    _verticalScrollController?.removeListener(_syncVerticalScroll);
    _timeVerticalScrollController?.removeListener(_syncTimeScroll);
    _horizontalScrollController?.dispose();
    _verticalScrollController?.dispose();
    _timeVerticalScrollController?.dispose();
    super.dispose();
  }

  // Swipe gesture handlers
  void _onPanStart(DragStartDetails details) {
    _swipeStartX = details.globalPosition.dx;
    _swipeStartY = details.globalPosition.dy;
    _isHorizontalScrolling = false;
  }

  void _onPanUpdate(DragUpdateDetails details) {
    // If horizontal scrolling is active, don't process swipe gestures
    if (_isHorizontalScrolling) return;

    // Check if this looks like a horizontal scroll rather than a swipe
    if (_swipeStartX != null && _swipeStartY != null) {
      final deltaX = (details.globalPosition.dx - _swipeStartX!).abs();
      final deltaY = (details.globalPosition.dy - _swipeStartY!).abs();

      // If vertical movement is significant compared to horizontal, ignore
      if (deltaY > _maxVerticalDeviation && deltaY > deltaX * 0.5) {
        _swipeStartX = null;
        _swipeStartY = null;
        return;
      }
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_swipeStartX == null || _swipeStartY == null || _isHorizontalScrolling) {
      _swipeStartX = null;
      _swipeStartY = null;
      return;
    }

    final velocity = details.velocity.pixelsPerSecond;
    final horizontalVelocity = velocity.dx.abs();
    final verticalVelocity = velocity.dy.abs();

    // Check if this is a valid horizontal swipe
    if (horizontalVelocity > _minSwipeVelocity &&
        horizontalVelocity > verticalVelocity * 2) {

      if (velocity.dx > 0) {
        // Swipe right - go to previous week
        widget.onSwipeToPrevious?.call();
      } else {
        // Swipe left - go to next week
        widget.onSwipeToNext?.call();
      }
    }

    _swipeStartX = null;
    _swipeStartY = null;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Determine time range based on hour view mode
        final businessOpenTime = widget.businessHours['openTime'] as int;
        final businessCloseTime = widget.businessHours['closeTime'] as int;

        final openTime = provider.showFullDay ? 0 : businessOpenTime;
        final closeTime = provider.showFullDay ? 24 : businessCloseTime;

        final lunchStart = widget.businessHours['lunchBreak']['start'] as int;
        final lunchEnd = widget.businessHours['lunchBreak']['end'] as int;
        final staff = provider.availableStaff;
        final visibleStaff =
            staff.where((s) => provider.selectedStaff.contains(s.id)).toList();

        // Load staff working hours on demand for visible staff (optimized batch approach)
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final staffIds = visibleStaff.map((s) => s.id).toList();
          if (staffIds.isNotEmpty) {
            provider.loadStaffWorkingHoursOnDemand(staffIds, reason: 'Week view visible staff');
          }
        });

        const slotHeight = 60.0;
        final totalHours = closeTime - openTime;
        const staffColumnWidth = 100.0; // Smaller for week view

        // Get the week days (Monday to Sunday)
        final weekDays = _getWeekDays(widget.selectedWeek);

        return Container(
          color: AppColors.appBackground, // Consistent background
          child: Column(
            children: [
              // Combined header and calendar with synchronized scrolling
              Expanded(
              child: GestureDetector(
                onPanStart: _onPanStart,
                onPanUpdate: _onPanUpdate,
                onPanEnd: _onPanEnd,
                child: Row(
                  children: [
                    // Fixed time labels column
                    Consumer<ThemeProvider>(
                      builder: (context, themeProvider, child) {
                        return Container(
                          width: 60,
                          color: themeProvider.getCalendarBackgroundColor(context),
                          child: Column(
                        children: [
                          _buildWeekTimeHeader(),
                          Expanded(
                            child: SingleChildScrollView(
                              controller: _timeVerticalScrollController,
                              physics: const NeverScrollableScrollPhysics(),
                              child: Column(
                                children:
                                    List.generate(totalHours, (index) {
                                  final hour = openTime + index;
                                  final time = DateTime(2024, 1, 1, hour);
                                  final isCurrentHour =
                                      DateTime.now().hour == hour;

                                  return TimeLabel(
                                    time: time,
                                    isCurrentHour: isCurrentHour,
                                  );
                                }),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                      },
                    ),
                    // Scrollable calendar content
                    Expanded(
                      child: NotificationListener<OverscrollNotification>(
                        onNotification: (notification) {
                          if (_horizontalScrollController != null) {
                            if (notification.overscroll > 0 &&
                                _horizontalScrollController!.position.pixels >=
                                    _horizontalScrollController!
                                        .position.maxScrollExtent) {
                              widget.onSwipeToNext?.call();
                            } else if (notification.overscroll < 0 &&
                                _horizontalScrollController!.position.pixels <=
                                    _horizontalScrollController!
                                        .position.minScrollExtent) {
                              widget.onSwipeToPrevious?.call();
                            }
                          }
                          return false;
                        },
                        child: SingleChildScrollView(
                          controller: _horizontalScrollController,
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: weekDays.length *
                                visibleStaff.length *
                                staffColumnWidth,
                            child: Column(
                              children: [
                                _buildSynchronizedWeekHeader(
                                    weekDays, visibleStaff, staffColumnWidth),
                                Expanded(
                                  child: SingleChildScrollView(
                                    controller: _verticalScrollController,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ...weekDays.map((day) {
                                          final isWorkDay =
                                              provider.isWorkingDay(day);
                                          final dayAppointments =
                                              widget.appointmentsByDay[
                                                      _dateKey(day)] ??
                                                  [];

                                          return Row(
                                            children: visibleStaff
                                                .map((staffMember) =>
                                                    _buildDayStaffColumn(
                                                      provider,
                                                      day,
                                                      staffMember,
                                                      dayAppointments,
                                                      staffColumnWidth,
                                                      slotHeight,
                                                      totalHours,
                                                      openTime,
                                                      closeTime,
                                                      lunchStart,
                                                      lunchEnd,
                                                      isWorkDay,
                                                    ))
                                                .toList(),
                                          );
                                        }),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        );
      },
    );
  }

  Widget _buildSynchronizedWeekHeader(
      List<DateTime> weekDays,
      List<StaffResponse> visibleStaff,
      double staffColumnWidth) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.forestGreen,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Row(
        children: [
          // Day-Staff headers
          ...weekDays.map((day) {
            final isToday = _isToday(day);
            final provider = context.read<CalendarProvider>();
            final isWorkDay = provider.isWorkingDay(day);

            return GestureDetector(
              onTap: widget.onDayTap != null ? () => widget.onDayTap!(day) : null,
              child: Row(
                children: visibleStaff
                    .map((staffMember) => Container(
                        width: staffColumnWidth,
                        height: 72, // Fixed height to match time header
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: isToday
                              ? Colors.white.withOpacity(0.2)
                              : Colors.transparent,
                          border: Border(
                            left: BorderSide(
                              color: Colors.white.withOpacity(0.3),
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                DateFormat('EEE', 'ro').format(day),
                                style: TextStyle(
                                  color:
                                      isWorkDay ? Colors.white : Colors.white70,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              Text(
                                day.day.toString(),
                                style: TextStyle(
                                  color: isToday ? Colors.white : Colors.white70,
                                  fontSize: 12,
                                  fontWeight: isToday
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 2),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: StaffColors.getColorForStaff(
                                          staffMember.id),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 2),
                                  Flexible(
                                    child: Text(
                                      staffMember.displayName.split(
                                          ' ')[0], // First name/nickname only
                                      style: const TextStyle(
                                        color: Colors.white70,
                                        fontSize: 8,
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ))
                    .toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildWeekTimeHeader() {
    return Container(
      width: 60,
      height: 72, // Match the height of day-staff headers
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.forestGreen,
        border: Border(
          right: BorderSide(color: Colors.white, width: 0.5),
          bottom: BorderSide(color: Colors.white, width: 0.5),
        ),
      ),
      child: const Center(
        child: Text(
          'Ora',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  void _showQuickActionMenu(DateTime slotTime, String staffId) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Programare nouă'),
              onTap: () {
                Navigator.pop(context);
                widget.onTimeSlotTap?.call(slotTime, staffId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Blochează timp'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (_) => BlockTimeDialog(
                    selectedDate: slotTime,
                    preselectedStaffId: staffId,
                    preselectedStartTime: TimeOfDay.fromDateTime(slotTime),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildDayStaffColumn(
    CalendarProvider provider,
    DateTime day,
    StaffResponse staffMember,
    List<Appointment> dayAppointments,
    double columnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    int lunchStart,
    int lunchEnd,
    bool isWorkDay,
  ) {
    // Use synchronous method for immediate visual feedback
    // Note: staffWorking and dateClosureInfo are now handled in getTimeSlotStyling method

    final staffAppointments = dayAppointments.where((appointment) {
      if (appointment.groomerId != null &&
          appointment.groomerId!.isNotEmpty) {
        return appointment.groomerId == staffMember.id;
      }

      String appointmentStaffName;
      try {
        final groomer = appointment.assignedGroomer;
        appointmentStaffName =
            groomer.isNotEmpty ? groomer : 'Ana Popescu';
      } catch (e) {
        appointmentStaffName = 'Ana Popescu';
      }

      return appointmentStaffName == staffMember.name ||
          appointmentStaffName == staffMember.displayName;
    });

    final dayBlocks = provider.getBlockedTimesForDate(day);
    final staffBlocks = dayBlocks.where((block) {
      final ids = (block['staffIds'] as List).cast<String>();
      return ids.contains(staffMember.id);
    });

    return SizedBox(
      width: columnWidth,
      child: Stack(
        children: [
          Column(
            children: List.generate(totalHours, (index) {
              final hour = openTime + index;
              final slotTime = DateTime(
                day.year,
                day.month,
                day.day,
                hour,
              );

              // Get comprehensive styling information
              final slotStyling = provider.getTimeSlotStyling(slotTime, staffMember.id);

              final businessOpenTime = widget.businessHours['openTime'] as int;
              final businessCloseTime = widget.businessHours['closeTime'] as int;

              // Determine if this is a business hour (considering salon closure and staff availability)
              final isBusinessHour = provider.showFullDay
                  ? (slotStyling.isAvailable && hour >= businessOpenTime && hour < businessCloseTime)
                  : (slotStyling.isAvailable && hour >= openTime && hour < closeTime);

              // Debug visual styling application
              if (slotStyling.isGreyedOut) {
                // todo optimize this debugPrint('🎨 Applying grey styling to week slot: ${slotTime.toIso8601String()} - ${slotStyling.disabledReason}');
              }

              final isLunchBreak = hour >= lunchStart && hour < lunchEnd;
              final hasAppointment = staffAppointments.any((apt) =>
                  apt.startTime.hour <= hour && apt.endTime.hour > hour);

              return Container(
                decoration: BoxDecoration(
                  border: Border(
                    right:
                        BorderSide(color: Colors.grey.shade300, width: 0.5),
                  ),
                  // Add visual indication for closed days
                  color: slotStyling.isGreyedOut
                      ? Colors.grey.shade200.withValues(alpha: 0.7)
                      : null,
                ),
                child: Stack(
                  children: [
                    TimeSlot(
                      dateTime: slotTime,
                      isBusinessHour: isBusinessHour && !slotStyling.isGreyedOut,
                      isLunchBreak: isLunchBreak,
                      isAvailable: !hasAppointment && slotStyling.isAvailable,
                      height: slotHeight,
                      onTap: slotStyling.isInteractive
                          ? () => widget.onTimeSlotTap?.call(slotTime, staffMember.id)
                          : null,
                      onLongPress: slotStyling.isInteractive
                          ? () => _showQuickActionMenu(slotTime, staffMember.id)
                          : null,
                    ),
                    // Add diagonal lines for closed periods with proper clipping
                    if (slotStyling.isGreyedOut)
                      ClipRect(
                        child: Container(
                          height: slotHeight,
                          child: CustomPaint(
                            painter: DiagonalLinesPainter(
                              color: Colors.grey.shade400,
                              spacing: 6.0,
                            ),
                            size: Size.infinite,
                          ),
                        ),
                      ),
                    // Add closure reason tooltip
                    if (slotStyling.disabledReason != null)
                      Positioned.fill(
                        child: Tooltip(
                          message: slotStyling.disabledReason!,
                          child: Container(),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),
          ...staffBlocks.map((block) {
                final start = DateTime.parse(block['startTime']).toLocal();
                final end = DateTime.parse(block['endTime']).toLocal();

                final topOffset = ((start.hour - openTime) * slotHeight) +
                    (start.minute / 60 * slotHeight);
                final duration = end.difference(start);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 1,
                  right: 1,
                  child: BlockTimeBlock(
                    block: block,
                    height: blockHeight,
                    onTap: () => widget.onBlockTap?.call(block),
                  ),
                );
          }),
          ...staffAppointments.map((appointment) {
                final startHour = appointment.startTime.hour;
                final startMinute = appointment.startTime.minute;

                final topOffset = ((startHour - openTime) * slotHeight) +
                    (startMinute / 60 * slotHeight);
                final duration =
                    appointment.endTime.difference(appointment.startTime);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 1,
                  right: 1,
                  child: AppointmentBlock(
                    appointment: appointment,
                    height: blockHeight,
                    isCompact: true,
                    onTap: () => widget.onAppointmentTap(appointment),
                  ),
                );
          }),
          if (_isToday(day))
            _buildCurrentTimeIndicator(openTime, slotHeight),
        ],
      ),
    );
  }

  List<DateTime> _getWeekDays(DateTime week) {
    // Find Monday of the week
    final monday = week.subtract(Duration(days: week.weekday - 1));
    return List.generate(7, (index) => monday.add(Duration(days: index)));
  }

  DateTime _dateKey(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Widget _buildCurrentTimeIndicator(int openTime, double slotHeight) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    // In full day mode, show indicator for any hour; in business mode, only during business hours
    final businessCloseTime = widget.businessHours['closeTime'] as int;
    final showFullDay = context.read<CalendarProvider>().showFullDay;

    if (!showFullDay && (currentHour < openTime || currentHour >= businessCloseTime)) {
      return const SizedBox.shrink();
    }

    if (showFullDay && (currentHour < openTime || currentHour >= 24)) {
      return const SizedBox.shrink();
    }

    final topOffset = ((currentHour - openTime) * slotHeight) +
        (currentMinute / 60 * slotHeight);

    return Positioned(
      top: topOffset,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: Colors.red,
        child: Row(
          children: [
            Container(
              width: 4,
              height: 4,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Enhanced diagonal lines painter for closed periods
class DiagonalLinesPainter extends CustomPainter {
  final Color color;
  final double spacing;
  final double strokeWidth;

  DiagonalLinesPainter({
    this.color = Colors.grey,
    this.spacing = 10.0,
    this.strokeWidth = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
