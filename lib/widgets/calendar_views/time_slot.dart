import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../providers/theme_provider.dart';

class TimeSlot extends StatelessWidget {
  final DateTime dateTime;
  final bool isBusinessHour;
  final bool isLunchBreak;
  final bool isAvailable;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final double height;
  final Widget? child;

  const TimeSlot({
    Key? key,
    required this.dateTime,
    required this.isBusinessHour,
    required this.isLunchBreak,
    required this.isAvailable,
    this.onTap,
    this.onLongPress,
    this.height = 60,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isBusinessHour && !isLunchBreak ? onTap : null,
      onLongPress: isBusinessHour && !isLunchBreak ? onLongPress : null,
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: _getBackgroundColor(context),
          border: Border(
            top: BorderSide(
              color: Theme.of(context).colorScheme.outline,
              width: 0.5,
            ),
            right: BorderSide(
              color: Theme.of(context).colorScheme.outline,
              width: 0.5,
            ),
          ),
        ),
        child: Stack(
          children: [
            // Background pattern for non-business hours with proper clipping
            if (!isBusinessHour || isLunchBreak)
              ClipRect(
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                  ),
                  child: CustomPaint(
                    painter: DiagonalLinesPainter(),
                    size: Size.infinite,
                  ),
                ),
              ),
            // Content
            if (child != null) child!,
            // Hover effect for available slots
            if (isBusinessHour && !isLunchBreak && isAvailable)
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onTap,
                  hoverColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  child: Container(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (!isBusinessHour) {
      return colorScheme.surfaceVariant.withValues(alpha: 0.3);
    }
    if (isLunchBreak) {
      return Colors.orange.withValues(alpha: 0.2);
    }
    if (!isAvailable) {
      return colorScheme.surfaceVariant.withValues(alpha: 0.5);
    }
    return colorScheme.surface;
  }
}

class DiagonalLinesPainter extends CustomPainter {
  const DiagonalLinesPainter();

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 1;

    const spacing = 10.0;
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class TimeLabel extends StatelessWidget {
  final DateTime time;
  final bool isCurrentHour;
  final double height;

  const TimeLabel({
    Key? key,
    required this.time,
    this.isCurrentHour = false,
    this.height = 60.0, // Default height, but should be passed from provider
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final hour = time.hour;
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final period = hour < 12 ? 'AM' : 'PM';

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          width: 60,
          height: height, // Use dynamic height from provider
          decoration: BoxDecoration(
            color: isCurrentHour
                ? (themeProvider.isDarkMode
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.2)
                    : const Color(0xFF2E7D32).withValues(alpha: 0.1))
                : null,
            border: Border(
              right: BorderSide(
                color: themeProvider.getBorderColor(context),
                width: 1,
              ),
              top: BorderSide(
                color: themeProvider.getBorderColor(context),
                width: 0.5,
              ),
            ),
          ),
          child: Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 0.5), // Match the top border width
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '$displayHour:00',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isCurrentHour ? FontWeight.bold : FontWeight.normal,
                  color: isCurrentHour
                      ? (themeProvider.isDarkMode
                          ? const Color(0xFF4CAF50)
                          : const Color(0xFF2E7D32))
                      : themeProvider.getSecondaryTextColor(context),
                ),
              ),
              Text(
                period,
                style: TextStyle(
                  fontSize: 10,
                  color: isCurrentHour
                      ? (themeProvider.isDarkMode
                          ? const Color(0xFF4CAF50)
                          : const Color(0xFF2E7D32))
                      : themeProvider.getTertiaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
      ),
        );
      },
    );
  }
}
