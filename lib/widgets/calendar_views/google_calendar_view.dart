import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';
import '../common/custom_bottom_sheet.dart';
import '../dialogs/appointment_details_dialog.dart';
import '../dialogs/block_time_details_sheet.dart';
import 'day_view.dart';
import 'week_view.dart';
import 'month_view.dart';

enum CalendarViewMode { day, week, month }

class GoogleCalendarView extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final DateTime selectedDate;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final ValueChanged<CalendarViewMode>? onViewModeChange;
  final ValueChanged<DateTime>? onDateChanged;
  final VoidCallback? onRefreshPressed;
  final VoidCallback? onSettingsPressed;

  const GoogleCalendarView({
    Key? key,
    required this.currentViewMode,
    required this.selectedDate,
    this.onTimeSlotTap,
    this.onViewModeChange,
    this.onDateChanged,
    this.onRefreshPressed,
    this.onSettingsPressed,
  }) : super(key: key);

  @override
  State<GoogleCalendarView> createState() => _GoogleCalendarViewState();
}

enum _SwipeDirection { next, previous }

class _GoogleCalendarViewState extends State<GoogleCalendarView> {
  late DateTime _selectedDate;
  _SwipeDirection _swipeDirection = _SwipeDirection.next;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
    });
  }

  @override
  void didUpdateWidget(GoogleCalendarView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentViewMode != widget.currentViewMode ||
        oldWidget.selectedDate != widget.selectedDate) {
      if (oldWidget.selectedDate != widget.selectedDate) {
        _selectedDate = widget.selectedDate;
      }
      // Defer data loading to avoid build-time state updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadDataForCurrentView();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _loadDataForCurrentView() {
    final provider = Provider.of<CalendarProvider>(context, listen: false);

    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        provider.fetchAppointmentsForDate(_selectedDate);
        provider.fetchBlockedTimesForDate(_selectedDate);
        break;
      case CalendarViewMode.week:
        _loadWeekData(provider);
        break;
      case CalendarViewMode.month:
        _loadMonthData(provider);
        break;
    }
  }

  void _loadWeekData(CalendarProvider provider) {
    final weekStart = _getWeekStart(_selectedDate);
    debugPrint('📅 Loading week data with optimized single API calls for week starting: ${weekStart.toString().split(' ')[0]}');

    // Use optimized methods that make single API calls for the entire week
    provider.fetchAppointmentsForWeek(weekStart);
    provider.fetchBlockedTimesForWeek(weekStart);
  }

  void _loadMonthData(CalendarProvider provider) {
    final monthStart = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final monthEnd = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
    debugPrint('📅 Loading month data with optimized single API calls for month: ${monthStart.toString().split(' ')[0]} to ${monthEnd.toString().split(' ')[0]}');

    // Use optimized methods that make single API calls for the entire month
    provider.fetchAppointmentsForDateRange(monthStart, monthEnd);
    provider.fetchBlockedTimesForDateRange(monthStart, monthEnd);
  }

  DateTime _getWeekStart(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Professional Apple Calendar-style header
        _buildHeader(),
        Expanded(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200), // Reduced from 500ms to eliminate white flash
            switchInCurve: Curves.easeOutCubic,
            switchOutCurve: Curves.easeOutCubic,
            transitionBuilder: (child, animation) {
              final beginOffset = _swipeDirection == _SwipeDirection.next
                  ? const Offset(1, 0)
                  : const Offset(-1, 0);
              final curvedAnimation =
                  CurvedAnimation(parent: animation, curve: Curves.easeOutCubic);
              return SlideTransition(
                position: Tween<Offset>(begin: beginOffset, end: Offset.zero)
                    .animate(curvedAnimation),
                child: child,
              );
            },
            child: Container(
              color: AppColors.appBackground, // Ensure consistent background during transitions
              child: _buildCalendarContent(
                key: ValueKey(
                    '${widget.currentViewMode}-${_selectedDate.toIso8601String()}'),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {

        return Container(
          height: 56, // Professional Apple Calendar-style header height (increased for larger icons)
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Professional padding
          decoration: BoxDecoration(
            color: AppColors.forestGreen,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                offset: const Offset(0, 1),
                blurRadius: 3,
              ),
            ],
          ),
          child: Row(
            children: [
              // Left side controls (where drawer icon would be)
              Row(
                children: [
                  // Refresh button
                  if (widget.onRefreshPressed != null)
                    IconButton(
                      onPressed: widget.onRefreshPressed,
                      icon: const Icon(Icons.refresh, color: Colors.white, size: 30),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
                      tooltip: 'Actualizează',
                    ),

                  // View mode selector
                  PopupMenuButton<CalendarViewMode>(
                    icon: const Icon(Icons.view_agenda, color: Colors.white, size: 30),
                    onSelected: widget.onViewModeChange,
                    padding: EdgeInsets.zero,
                    tooltip: 'Schimbă vizualizarea',
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: CalendarViewMode.day,
                        child: Text('Zi'),
                      ),
                      const PopupMenuItem(
                        value: CalendarViewMode.week,
                        child: Text('Săptămână'),
                      ),
                      const PopupMenuItem(
                        value: CalendarViewMode.month,
                        child: Text('Lună'),
                      ),
                    ],
                  ),
                ],
              ),

              // Month/Period dropdown with navigation (centered)
              Expanded(
                child: Row(
                  children: [
                    IconButton(
                      onPressed: _goToPrevious,
                      icon: const Icon(Icons.chevron_left, color: Colors.white, size: 30),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: _goToToday,
                        child: Text(
                          _getCompactPeriodText(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _goToNext,
                      icon: const Icon(Icons.chevron_right, color: Colors.white, size: 30),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
                    ),
                  ],
                ),
              ),

              // Compact date indicator (like "FRI 13")
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _getCompactDateText(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),


              // Right side - Settings button only
              if (widget.onSettingsPressed != null)
                IconButton(
                  onPressed: widget.onSettingsPressed,
                  icon: const Icon(Icons.settings, color: Colors.white, size: 30),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
                  tooltip: 'Setări',
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCalendarContent({Key? key}) {
    return KeyedSubtree(
      key: key,
      child: Consumer<CalendarProvider>(
        builder: (context, provider, child) {
          switch (widget.currentViewMode) {
            case CalendarViewMode.day:
              return _buildDayView(provider);
            case CalendarViewMode.week:
              return _buildWeekView(provider);
            case CalendarViewMode.month:
              return _buildMonthView(provider);
          }
        },
      ),
    );
  }

  Widget _buildDayView(CalendarProvider provider) {
    // Get fresh business hours each time to ensure updates are reflected
    final businessHours = provider.getBusinessHours();
    debugPrint('📅 DayView business hours: openTime=${businessHours['openTime']}, closeTime=${businessHours['closeTime']}');

    return DayView(
      selectedDate: _selectedDate,
      appointments: provider.getFilteredAppointmentsForDate(_selectedDate),
      onAppointmentTap: _showAppointmentDetails,
      onBlockTap: _showBlockTimeDetails,
      onTimeSlotTap: widget.onTimeSlotTap,
      businessHours: businessHours,
      onSwipeToNext: () => _changeDate(next: true),
      onSwipeToPrevious: () => _changeDate(next: false),
    );
  }

  Widget _buildWeekView(CalendarProvider provider) {
    // Get fresh business hours each time to ensure updates are reflected
    final businessHours = provider.getBusinessHours();
    debugPrint('📅 WeekView business hours: openTime=${businessHours['openTime']}, closeTime=${businessHours['closeTime']}');

    return WeekView(
      selectedWeek: _selectedDate,
      appointmentsByDay: _groupAppointmentsByDay(provider),
      onAppointmentTap: _showAppointmentDetails,
      onBlockTap: _showBlockTimeDetails,
      onTimeSlotTap: widget.onTimeSlotTap,
      onDayTap: _selectDay,
      businessHours: businessHours,
      onSwipeToNext: () => _changeDate(next: true),
      onSwipeToPrevious: () => _changeDate(next: false),
    );
  }

  Widget _buildMonthView(CalendarProvider provider) {
    return MonthView(
      selectedMonth: _selectedDate,
      appointmentsByDay: _groupAppointmentsByMonth(provider),
      onAppointmentTap: _showAppointmentDetails,
      onDayTap: _selectDay,
      onSwipeToNext: () => _changeDate(next: true),
      onSwipeToPrevious: () => _changeDate(next: false),
    );
  }

  Map<DateTime, List<Appointment>> _groupAppointmentsByDay(
      CalendarProvider provider) {
    final Map<DateTime, List<Appointment>> grouped = {};

    // For week view, get filtered appointments for each day from the cache
    final weekStart = _getWeekStart(_selectedDate);
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dayKey = DateTime(day.year, day.month, day.day);

      // Get filtered appointments for this specific day from cache
      grouped[dayKey] = provider.getFilteredAppointmentsForDate(day);
    }

    return grouped;
  }

  Map<DateTime, List<Appointment>> _groupAppointmentsByMonth(
      CalendarProvider provider) {
    final Map<DateTime, List<Appointment>> grouped = {};

    // For month view, get appointments for the entire month
    final firstDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final lastDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);

    // Include days from previous/next month that appear in the calendar grid
    final firstDayToShow = firstDayOfMonth.subtract(Duration(days: (firstDayOfMonth.weekday - 1) % 7));
    final lastDayToShow = firstDayToShow.add(const Duration(days: 41)); // 6 weeks = 42 days

    // Load appointments for each day in the visible range
    DateTime currentDay = firstDayToShow;
    while (currentDay.isBefore(lastDayToShow) || currentDay.isAtSameMomentAs(lastDayToShow)) {
      final dayKey = DateTime(currentDay.year, currentDay.month, currentDay.day);
      grouped[dayKey] = provider.getFilteredAppointmentsForDate(currentDay);
      currentDay = currentDay.add(const Duration(days: 1));
    }

    return grouped;
  }

  void _showAppointmentDetails(Appointment appointment) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AppointmentDetailsDialog(appointment: appointment);
      },
    );
  }

  void _showBlockTimeDetails(Map<String, dynamic> block) {
    CustomBottomSheet.show(
      context: context,
      title: 'Detalii blocare',
      isScrollControlled: true,
      child: BlockTimeDetailsSheet(
        block: block,
        isWeekView: widget.currentViewMode == CalendarViewMode.week,
      ),
    );
  }

  void _selectDay(DateTime day) {
    // Switch to day view when a day is selected from week view
    if (widget.currentViewMode != CalendarViewMode.day) {
      widget.onViewModeChange?.call(CalendarViewMode.day);
    }

    setState(() {
      _selectedDate = day;
    });

    widget.onDateChanged?.call(_selectedDate);

    // Data for the new day will be loaded when the view mode updates
  }

  void _changeDate({required bool next}) {
    final maxDate = DateTime.now().add(const Duration(days: 90));
    DateTime newDate;
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        newDate = next
            ? _selectedDate.add(const Duration(days: 1))
            : _selectedDate.subtract(const Duration(days: 1));
        break;
      case CalendarViewMode.week:
        newDate = next
            ? _selectedDate.add(const Duration(days: 7))
            : _selectedDate.subtract(const Duration(days: 7));
        break;
      case CalendarViewMode.month:
        newDate = DateTime(
          _selectedDate.year,
          _selectedDate.month + (next ? 1 : -1),
          1,
        );
        break;
    }

    if (next && newDate.isAfter(maxDate)) {
      return;
    }

    setState(() {
      _swipeDirection = next ? _SwipeDirection.next : _SwipeDirection.previous;
      _selectedDate = newDate;
    });

    widget.onDateChanged?.call(_selectedDate);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
    });
  }

  void _goToPrevious() {
    _changeDate(next: false);
  }

  void _goToNext() {
    _changeDate(next: true);
  }

  void _goToToday() {
    final today = DateTime.now();
    setState(() {
      _swipeDirection = today.isAfter(_selectedDate)
          ? _SwipeDirection.next
          : _SwipeDirection.previous;
      _selectedDate = today;
    });

    widget.onDateChanged?.call(_selectedDate);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
    });
  }

  // Helper methods for compact header
  String _getCompactPeriodText() {
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        return DateFormat('MMMM yyyy', 'ro').format(weekStart);
      case CalendarViewMode.month:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
    }
  }

  String _getCompactDateText() {
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        return DateFormat('EEE dd', 'ro').format(_selectedDate).toUpperCase();
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 6));
        return '${weekStart.day}-${weekEnd.day}';
      case CalendarViewMode.month:
        return DateFormat('MMM', 'ro').format(_selectedDate).toUpperCase();
    }
  }


}
