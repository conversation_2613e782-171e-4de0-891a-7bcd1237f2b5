import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../services/staff_service.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/theme_provider.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_block.dart';
import 'block_time_block.dart';
import 'time_slot.dart';
import '../dialogs/block_time_dialog.dart';

/// Custom scroll physics for smooth finger-following PageView behavior
class SmoothPageScrollPhysics extends ScrollPhysics {
  const SmoothPageScrollPhysics({ScrollPhysics? parent}) : super(parent: parent);

  @override
  SmoothPageScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return SmoothPageScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  SpringDescription get spring => const SpringDescription(
    mass: 0.8, // Much heavier mass for strict finger-following
    stiffness: 10.0, // Very low stiffness to prevent premature snapping
    damping: 2.0, // High damping to eliminate springiness
  );

  @override
  double get minFlingVelocity => 300.0; // Higher threshold to only detect intentional flings

  @override
  double get maxFlingVelocity => 2000.0; // Keep reasonable max velocity

  @override
  Tolerance get tolerance => const Tolerance(
    velocity: 0.01, // Extremely low velocity tolerance for precise tracking
    distance: 0.001, // Extremely low distance tolerance for precise tracking
  );

  @override
  double get dragStartDistanceMotionThreshold => 1.0; // Lower threshold for immediate response
}

class DayView extends StatefulWidget {
  final DateTime selectedDate;
  final List<Appointment> appointments;
  final Function(Appointment) onAppointmentTap;
  final Function(Map<String, dynamic>)? onBlockTap;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final Map<String, dynamic> businessHours;
  final VoidCallback? onSwipeToNext;
  final VoidCallback? onSwipeToPrevious;

  const DayView({
    Key? key,
    required this.selectedDate,
    required this.appointments,
    required this.onAppointmentTap,
    this.onBlockTap,
    this.onTimeSlotTap,
    required this.businessHours,
    this.onSwipeToNext,
    this.onSwipeToPrevious,
  }) : super(key: key);

  @override
  State<DayView> createState() => _DayViewState();
}

class _DayViewState extends State<DayView> {

  late ScrollController _horizontalScrollController;
  late ScrollController _verticalScrollController;
  late ScrollController _timeScrollController;
  PageController? _pageController;

  // PageView configuration for infinite scrolling
  static const int _initialPage = 10000; // Big middle index for infinite scrolling
  late DateTime _baseDate; // The date that corresponds to the initial page
  DateTime? _currentDate; // Track current displayed date
  DateTime? _currentWeekStart; // Track current week for optimization



  @override
  void initState() {
    super.initState();

    // Initialize base date and current date
    _baseDate = widget.selectedDate;
    _currentDate = widget.selectedDate;
    _currentWeekStart = _getWeekStart(widget.selectedDate);

    // Initialize PageController with optimized settings for smooth finger-following
    _pageController = PageController(
      initialPage: _initialPage,
      viewportFraction: 1.0, // Full page width
      keepPage: true, // Maintain page position
    );
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _timeScrollController = ScrollController();

    // Sync time scroll with main content scroll
    _verticalScrollController.addListener(() {
      if (_timeScrollController.hasClients) {
        _timeScrollController.jumpTo(_verticalScrollController.offset);
      }
    });


  }

  @override
  void dispose() {
    _pageController?.dispose();
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    _timeScrollController.dispose();
    super.dispose();
  }

  // Helper methods for PageView date mapping
  DateTime _getDateForPageIndex(int pageIndex) {
    final dayOffset = pageIndex - _initialPage;
    return _baseDate.add(Duration(days: dayOffset));
  }

  // Helper method to get week start (Monday)
  DateTime _getWeekStart(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  // Helper method to check if two dates are in the same week
  bool _isSameWeek(DateTime date1, DateTime date2) {
    final week1Start = _getWeekStart(date1);
    final week2Start = _getWeekStart(date2);
    return week1Start.isAtSameMomentAs(week2Start);
  }

  void _onPageChanged(int pageIndex) {
    final newDate = _getDateForPageIndex(pageIndex);
    final previousDate = _currentDate ?? widget.selectedDate;
    final newWeekStart = _getWeekStart(newDate);

    // Check if we're crossing week boundaries
    final isCrossWeekNavigation = _currentWeekStart != null &&
        !newWeekStart.isAtSameMomentAs(_currentWeekStart!);

    _currentDate = newDate;

    if (isCrossWeekNavigation) {
      // Cross-week navigation: trigger full view update
      _currentWeekStart = newWeekStart;
      print('📅 Cross-week navigation detected: ${_currentWeekStart} -> $newWeekStart');

      // Trigger full rebuild by calling parent callbacks
      if (newDate.isAfter(previousDate)) {
        widget.onSwipeToNext?.call();
      } else if (newDate.isBefore(previousDate)) {
        widget.onSwipeToPrevious?.call();
      }

      // Force rebuild of the entire view
      setState(() {});
    } else {
      // Within-week navigation: only update appointment content
      print('📅 Within-week navigation: ${previousDate.day} -> ${newDate.day}');

      // Trigger lightweight update - only appointment content changes
      if (newDate.isAfter(previousDate)) {
        widget.onSwipeToNext?.call();
      } else if (newDate.isBefore(previousDate)) {
        widget.onSwipeToPrevious?.call();
      }

      // Trigger minimal rebuild - only appointment content
      setState(() {});
    }
  }



  @override
  Widget build(BuildContext context) {
    // If PageController is not initialized yet, show loading
    if (_pageController == null) {
      return Center(child: CircularProgressIndicator());
    }

    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final currentDisplayDate = _currentDate ?? widget.selectedDate;
        final currentWeekStart = _getWeekStart(currentDisplayDate);

        // Determine time range based on hour view mode
        final businessOpenTime = widget.businessHours['openTime'] as int;
        final businessCloseTime = widget.businessHours['closeTime'] as int;
        final openTime = provider.showFullDay ? 0 : businessOpenTime;
        final closeTime = provider.showFullDay ? 24 : businessCloseTime;
        final totalHours = closeTime - openTime;
        final slotHeight = provider.timeSlotHeight;

        // Calculate staff layout
        final staff = provider.availableStaff;
        final selectedStaffIds = provider.selectedStaff;
        final visibleStaff = staff.where((s) => selectedStaffIds.contains(s.id)).toList();

        final screenWidth = MediaQuery.of(context).size.width;
        const timeColumnWidth = 60.0;
        final availableWidth = screenWidth - timeColumnWidth;

        // Calculate column width
        double staffColumnWidth;
        double totalCalendarWidth;

        if (visibleStaff.isEmpty) {
          staffColumnWidth = 120.0;
          totalCalendarWidth = staffColumnWidth;
        } else if (visibleStaff.length == 1) {
          staffColumnWidth = availableWidth;
          totalCalendarWidth = availableWidth;
        } else {
          final distributedWidth = availableWidth / visibleStaff.length;
          if (distributedWidth >= 120.0) {
            staffColumnWidth = distributedWidth;
            totalCalendarWidth = availableWidth;
          } else {
            staffColumnWidth = 120.0;
            totalCalendarWidth = visibleStaff.length * staffColumnWidth;
          }
        }

        return Container(
          color: Theme.of(context).colorScheme.background, // Use theme-aware background
          child: Column(
            children: [
              Expanded(
                child: GestureDetector(
                  onScaleUpdate: (details) {
                    // Pinch-to-zoom functionality
                    if (details.scale != 1.0) {
                      final currentHeight = provider.timeSlotHeight;
                      final newHeight = (currentHeight * details.scale).clamp(
                        provider.minTimeSlotHeight,
                        provider.maxTimeSlotHeight,
                      );
                      if ((newHeight - currentHeight).abs() > 2) {
                        provider.setTimeSlotHeight(newHeight);
                      }
                    }
                  },
                  child: Row(
                    children: [
                      // Fixed time labels column (static for within-week navigation)
                      _buildStaticTimeColumn(openTime, closeTime, totalHours, slotHeight, currentDisplayDate),
                      // Dynamic content area with PageView
                      Expanded(
                        child: Column(
                          children: [
                            // Static weekday header (updates only on cross-week navigation)
                            _buildStaticWeekdayHeader(currentWeekStart),
                            // Static staff header
                            _buildStaticStaffHeader(visibleStaff, staffColumnWidth),
                            // Dynamic appointment content (PageView)
                            Expanded(
                              child: _buildDynamicAppointmentContent(
                                provider, visibleStaff, staffColumnWidth, slotHeight,
                                totalHours, openTime, closeTime, totalCalendarWidth
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Static time column that doesn't change during within-week navigation
  Widget _buildStaticTimeColumn(int openTime, int closeTime, int totalHours, double slotHeight, DateTime currentDate) {
    return Container(
      width: 60,
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: [
          _buildTimeHeader(),
          Expanded(
            child: SingleChildScrollView(
              controller: _timeScrollController,
              physics: const NeverScrollableScrollPhysics(),
              child: Column(
                children: List.generate(totalHours, (index) {
                  final hour = openTime + index;
                  final time = DateTime(
                    currentDate.year,
                    currentDate.month,
                    currentDate.day,
                    hour,
                  );
                  final isCurrentHour = DateTime.now().hour == hour &&
                      DateTime.now().day == currentDate.day &&
                      DateTime.now().month == currentDate.month &&
                      DateTime.now().year == currentDate.year;

                  return TimeLabel(
                    time: time,
                    isCurrentHour: isCurrentHour,
                    height: slotHeight,
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Static weekday header that only updates on cross-week navigation
  Widget _buildStaticWeekdayHeader(DateTime weekStart) {
    final weekDays = List.generate(7, (index) => weekStart.add(Duration(days: index)));
    final currentDisplayDate = _currentDate ?? widget.selectedDate;

    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        border: Border(
          bottom: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
      ),
      child: Row(
        children: weekDays.map((day) {
          final isToday = _isToday(day);
          final isSelected = _isSameDay(day, currentDisplayDate);

          return Expanded(
            child: GestureDetector(
              onTap: () {
                // Navigate to selected day using PageView
                if (_pageController != null) {
                  final daysDifference = day.difference(_baseDate).inDays;
                  final targetPage = _initialPage + daysDifference;
                  _pageController!.animateToPage(
                    targetPage,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              },
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withOpacity(0.2)
                      : Colors.transparent,
                  border: Border(
                    right: BorderSide(
                      color: Colors.white.withOpacity(0.2),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      DateFormat('EEE', 'ro').format(day),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.white70,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      day.day.toString(),
                      style: TextStyle(
                        color: isToday
                            ? Colors.white
                            : (isSelected ? Colors.white : Colors.white70),
                        fontSize: 14,
                        fontWeight: isToday ? FontWeight.bold : FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // Static staff header
  Widget _buildStaticStaffHeader(List<StaffResponse> staff, double columnWidth) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Row(
        children: staff
            .map((staffMember) => Container(
                  width: columnWidth,
                  height: 56,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: Theme.of(context).colorScheme.outline),
                    ),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: StaffColors.getColorForStaff(staffMember.id),
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            staffMember.displayName,
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ))
            .toList(),
      ),
    );
  }

  // Dynamic appointment content with PageView
  Widget _buildDynamicAppointmentContent(
    CalendarProvider provider,
    List<StaffResponse> visibleStaff,
    double staffColumnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    double totalCalendarWidth,
  ) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        return false;
      },
      child: PageView.builder(
        controller: _pageController!,
        scrollDirection: Axis.horizontal,
        pageSnapping: true,
        physics: const SmoothPageScrollPhysics(),
        allowImplicitScrolling: false,
        padEnds: false,
        onPageChanged: _onPageChanged,
        itemBuilder: (context, pageIndex) {
          final currentDate = _getDateForPageIndex(pageIndex);
          return _buildAppointmentContentForDate(
            provider, currentDate, visibleStaff, staffColumnWidth,
            slotHeight, totalHours, openTime, closeTime, totalCalendarWidth
          );
        },
      ),
    );
  }

  // Build appointment content for a specific date (lightweight)
  Widget _buildAppointmentContentForDate(
    CalendarProvider provider,
    DateTime selectedDate,
    List<StaffResponse> visibleStaff,
    double staffColumnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    double totalCalendarWidth,
  ) {
    final lunchStart = widget.businessHours['lunchBreak']['start'] as int;
    final lunchEnd = widget.businessHours['lunchBreak']['end'] as int;
    final isWorkDay = provider.isWorkingDay(selectedDate);



    return SingleChildScrollView(
      controller: _horizontalScrollController,
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: totalCalendarWidth,
        child: SingleChildScrollView(
          controller: _verticalScrollController,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: visibleStaff
                .map((staffMember) => _buildStaffColumn(
                      provider,
                      staffMember,
                      staffColumnWidth,
                      slotHeight,
                      totalHours,
                      openTime,
                      closeTime,
                      lunchStart,
                      lunchEnd,
                      isWorkDay,
                      selectedDate,
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }





  Widget _buildTimeHeader() {
    return Container(
      width: 60,
      height: 56, // Match the height of staff headers
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          right: BorderSide(color: Theme.of(context).colorScheme.outline),
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Center(
        child: Text(
          'Ora',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  void _showQuickActionMenu(DateTime slotTime, String staffId) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading:  Icon(Icons.add),
              title: Text('Programare nouă'),
              onTap: () {
                Navigator.pop(context);
                widget.onTimeSlotTap?.call(slotTime, staffId);
              },
            ),
            ListTile(
              leading:  Icon(Icons.block),
              title: Text('Blochează timp'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (_) => BlockTimeDialog(
                    selectedDate: slotTime,
                    preselectedStaffId: staffId,
                    preselectedStartTime: TimeOfDay.fromDateTime(slotTime),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildStaffColumn(
    CalendarProvider provider,
    StaffResponse staffMember,
    double columnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    int lunchStart,
    int lunchEnd,
    bool isWorkDay,
    DateTime selectedDate,
  ) {
    // Get staff-specific working hours for this specific day
    final staffSettings = provider.getStaffWorkingHoursSettings(staffMember.id);
    int staffStartHour = openTime;
    int staffEndHour = closeTime;
    bool hasCustomSchedule = false;
    
    if (staffSettings != null) {
      final dayOfWeek = _getDayOfWeekString(selectedDate);
      final daySchedule = staffSettings.getScheduleForDay(dayOfWeek);
      
      if (daySchedule != null && daySchedule.isWorkingDay &&
          daySchedule.startTime != null && daySchedule.endTime != null) {
        // Parse staff start and end hours
        final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? openTime;
        final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? closeTime;

        // Use staff hours directly
        staffStartHour = startHour;
        staffEndHour = endHour;
        hasCustomSchedule = true;
      }
    }

    final staffAppointments = widget.appointments.where((appointment) {
      // First filter by date - only show appointments for the selected date
      final appointmentDate = DateTime(
        appointment.startTime.year,
        appointment.startTime.month,
        appointment.startTime.day,
      );
      final targetDate = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
      );

      if (!appointmentDate.isAtSameMomentAs(targetDate)) {
        return false;
      }

      // Then filter by staff member
      if (appointment.groomerId != null &&
          appointment.groomerId!.isNotEmpty) {
        return appointment.groomerId == staffMember.id;
      }

      String appointmentStaffName;
      try {
        appointmentStaffName =
            appointment.assignedGroomer?.isNotEmpty == true
                ? appointment.assignedGroomer!
                : 'Ana Popescu';
      } catch (e) {
        appointmentStaffName = 'Ana Popescu';
      }

      return appointmentStaffName == staffMember.name ||
          appointmentStaffName == staffMember.displayName;
    }).toList();

    // Debug logging to help troubleshoot
    print('📅 Date: ${selectedDate.toString().split(' ')[0]}');
    print('📅 Staff: ${staffMember.name}');
    print('📅 Total appointments passed to widget: ${widget.appointments.length}');
    print('📅 Filtered appointments for this staff/date: ${staffAppointments.length}');
    if (staffAppointments.isNotEmpty) {
      print('📅 Appointment times: ${staffAppointments.map((a) => '${a.startTime.hour}:${a.startTime.minute.toString().padLeft(2, '0')}-${a.endTime.hour}:${a.endTime.minute.toString().padLeft(2, '0')}').join(', ')}');
    }

    final dayBlocks = provider.getBlockedTimesForDate(selectedDate);
    final staffBlocks = dayBlocks.where((block) {
      final ids = (block['staffIds'] as List).cast<String>();
      return ids.contains(staffMember.id);
    }).toList();

    return SizedBox(
      width: columnWidth,
      child: Stack(
        children: [
          Column(
            children: List.generate(totalHours, (index) {
              final hour = openTime + index;
              final slotTime = DateTime(
                selectedDate.year,
                selectedDate.month,
                selectedDate.day,
                hour,
              );

              // Check if this hour is within staff working hours
              final isWithinStaffHours = hour >= staffStartHour && hour < staffEndHour;
              
              // Get comprehensive styling information
              final slotStyling = provider.getTimeSlotStyling(slotTime, staffMember.id);

              // Determine if this is a business hour (considering salon closure and staff availability)
              final isBusinessHour = provider.showFullDay
                  ? (slotStyling.isAvailable && isWithinStaffHours)
                  : (slotStyling.isAvailable && isWithinStaffHours);

              final isLunchBreak = hour >= lunchStart && hour < lunchEnd;
              final hasAppointment = staffAppointments.any((apt) =>
                  apt.startTime.hour <= hour && apt.endTime.hour > hour);

              // If outside staff hours, show greyed out slot
              if (!isWithinStaffHours) {
                return Container(
                  height: slotHeight,
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: Theme.of(context).colorScheme.outline),
                    ),
                    color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3),
                  ),
                  child: CustomPaint(
                    painter: DiagonalLinesPainter(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      spacing: 8.0,
                    ),
                    size: Size.infinite,
                  ),
                );
              }

              return Container(
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(color: Theme.of(context).colorScheme.outline),
                  ),
                  // Add visual indication for closed periods
                  color: slotStyling.isGreyedOut
                      ? Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.7)
                      : null,
                ),
                child: Stack(
                  children: [
                    TimeSlot(
                      dateTime: slotTime,
                      isBusinessHour: isBusinessHour && !slotStyling.isGreyedOut,
                      isLunchBreak: isLunchBreak,
                      isAvailable: !hasAppointment && slotStyling.isAvailable,
                      height: slotHeight,
                      onTap: slotStyling.isInteractive
                          ? () => widget.onTimeSlotTap?.call(slotTime, staffMember.id)
                          : null,
                      onLongPress: slotStyling.isInteractive
                          ? () => _showQuickActionMenu(slotTime, staffMember.id)
                          : null,
                    ),
                    // Add diagonal lines for closed periods with proper clipping
                    if (slotStyling.isGreyedOut)
                      ClipRect(
                        child: Container(
                          height: slotHeight,
                          child: CustomPaint(
                            painter: DiagonalLinesPainter(
                              color: Colors.grey.shade400,
                              spacing: 8.0,
                            ),
                            size: Size.infinite,
                          ),
                        ),
                      ),
                    // Add closure reason tooltip
                    if (slotStyling.disabledReason != null)
                      Positioned.fill(
                        child: Tooltip(
                          message: slotStyling.disabledReason!,
                          child: Container(),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),
          ...staffBlocks.map((block) {
                final start = DateTime.parse(block['startTime']).toLocal();
                final end = DateTime.parse(block['endTime']).toLocal();

                final topOffset = ((start.hour - openTime) * slotHeight) +
                    (start.minute / 60 * slotHeight);
                final duration = end.difference(start);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 2,
                  right: 2,
                  child: BlockTimeBlock(
                    block: block,
                    height: blockHeight,
                    onTap: () => widget.onBlockTap?.call(block),
                  ),
                );
          }).toList(),
          ...staffAppointments.map((appointment) {
                final startHour = appointment.startTime.hour;
                final startMinute = appointment.startTime.minute;

                final topOffset = ((startHour - openTime) * slotHeight) +
                    (startMinute / 60 * slotHeight);
                final duration =
                    appointment.endTime.difference(appointment.startTime);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 4, // Increased margin for better visual separation
                  right: 4, // Increased margin for better visual separation
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: 30, // Minimum height for readability
                      maxWidth: columnWidth - 8, // Ensure proper width constraints
                    ),
                    child: AppointmentBlock(
                      appointment: appointment,
                      height: blockHeight.clamp(30.0, double.infinity), // Ensure minimum height
                      onTap: () => widget.onAppointmentTap(appointment),
                      isCompact: false, // Use full layout in day view for better readability
                    ),
                  ),
                );
          }).toList(),
          if (_isToday(selectedDate))
            _buildCurrentTimeIndicator(openTime, slotHeight),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Widget _buildCurrentTimeIndicator(int openTime, double slotHeight) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    // In full day mode, show indicator for any hour; in business mode, only during business hours
    final businessCloseTime = widget.businessHours['closeTime'] as int;
    final showFullDay = context.read<CalendarProvider>().showFullDay;

    if (!showFullDay && (currentHour < openTime || currentHour >= businessCloseTime)) {
      return SizedBox.shrink();
    }

    if (showFullDay && (currentHour < openTime || currentHour >= 24)) {
      return SizedBox.shrink();
    }

    final topOffset = ((currentHour - openTime) * slotHeight) +
        (currentMinute / 60 * slotHeight);

    return Positioned(
      top: topOffset,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: Colors.red,
        child: Row(
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helper method to get day of week string
  String _getDayOfWeekString(DateTime date) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[date.weekday - 1];
  }
}

/// Enhanced diagonal lines painter for closed periods
class DiagonalLinesPainter extends CustomPainter {
  final Color color;
  final double spacing;
  final double strokeWidth;

  DiagonalLinesPainter({
    this.color = Colors.grey,
    this.spacing = 10.0,
    this.strokeWidth = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
