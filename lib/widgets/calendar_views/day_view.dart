import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../services/staff_service.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/theme_provider.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_block.dart';
import 'block_time_block.dart';
import 'time_slot.dart';
import '../dialogs/block_time_dialog.dart';

/// Custom scroll physics for smooth finger-following PageView behavior
class SmoothPageScrollPhysics extends ScrollPhysics {
  const SmoothPageScrollPhysics({ScrollPhysics? parent}) : super(parent: parent);

  @override
  SmoothPageScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return SmoothPageScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  SpringDescription get spring => const SpringDescription(
    mass: 0.3, // Even lighter mass for more responsive movement
    stiffness: 80.0, // Lower stiffness for smoother, less snappy transitions
    damping: 0.9, // Higher damping for more controlled movement
  );

  @override
  double get minFlingVelocity => 150.0; // Lower threshold for easier fling detection

  @override
  double get maxFlingVelocity => 2000.0; // Reasonable max velocity

  @override
  Tolerance get tolerance => const Tolerance(
    velocity: 0.3, // More sensitive to small movements
    distance: 0.05, // Finer distance tolerance for smoother tracking
  );

  @override
  double get dragStartDistanceMotionThreshold => 3.0; // Lower threshold for drag start
}

class DayView extends StatefulWidget {
  final DateTime selectedDate;
  final List<Appointment> appointments;
  final Function(Appointment) onAppointmentTap;
  final Function(Map<String, dynamic>)? onBlockTap;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final Map<String, dynamic> businessHours;
  final VoidCallback? onSwipeToNext;
  final VoidCallback? onSwipeToPrevious;

  const DayView({
    Key? key,
    required this.selectedDate,
    required this.appointments,
    required this.onAppointmentTap,
    this.onBlockTap,
    this.onTimeSlotTap,
    required this.businessHours,
    this.onSwipeToNext,
    this.onSwipeToPrevious,
  }) : super(key: key);

  @override
  State<DayView> createState() => _DayViewState();
}

class _DayViewState extends State<DayView> {

  late ScrollController _horizontalScrollController;
  late ScrollController _verticalScrollController;
  late ScrollController _timeScrollController;
  PageController? _pageController;

  // PageView configuration for infinite scrolling
  static const int _initialPage = 10000; // Big middle index for infinite scrolling
  late DateTime _baseDate; // The date that corresponds to the initial page
  DateTime? _currentDate; // Track current displayed date



  @override
  void initState() {
    super.initState();

    // Initialize base date and current date
    _baseDate = widget.selectedDate;
    _currentDate = widget.selectedDate;

    // Initialize PageController with optimized settings for smooth finger-following
    _pageController = PageController(
      initialPage: _initialPage,
      viewportFraction: 1.0, // Full page width
      keepPage: true, // Maintain page position
    );
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _timeScrollController = ScrollController();

    // Sync time scroll with main content scroll
    _verticalScrollController.addListener(() {
      if (_timeScrollController.hasClients) {
        _timeScrollController.jumpTo(_verticalScrollController.offset);
      }
    });


  }

  @override
  void dispose() {
    _pageController?.dispose();
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    _timeScrollController.dispose();
    super.dispose();
  }

  // Helper methods for PageView date mapping
  DateTime _getDateForPageIndex(int pageIndex) {
    final dayOffset = pageIndex - _initialPage;
    return _baseDate.add(Duration(days: dayOffset));
  }

  void _onPageChanged(int pageIndex) {
    final newDate = _getDateForPageIndex(pageIndex);
    final previousDate = _currentDate ?? widget.selectedDate;
    _currentDate = newDate;

    // Determine if we swiped forward or backward based on date comparison
    if (newDate.isAfter(previousDate)) {
      // Swiped to next day
      widget.onSwipeToNext?.call();
    } else if (newDate.isBefore(previousDate)) {
      // Swiped to previous day
      widget.onSwipeToPrevious?.call();
    }

    // Don't update _baseDate - keep it fixed to maintain consistent mapping
  }



  @override
  Widget build(BuildContext context) {
    // If PageController is not initialized yet, show the current day content directly
    if (_pageController == null) {
      return _buildDayContent(widget.selectedDate);
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        // Allow smooth scrolling without interference
        return false;
      },
      child: PageView.builder(
        controller: _pageController!,
        scrollDirection: Axis.horizontal,
        pageSnapping: true,
        physics: const SmoothPageScrollPhysics(), // Custom physics for smooth finger-following
        allowImplicitScrolling: false, // Prevent automatic scrolling
        padEnds: false, // Remove padding that can cause snapping issues
        onPageChanged: _onPageChanged,
        itemBuilder: (context, pageIndex) {
          final currentDate = _getDateForPageIndex(pageIndex);
          return _buildDayContent(currentDate);
        },
      ),
    );
  }

  Widget _buildDayContent(DateTime selectedDate) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Determine time range based on hour view mode
        final businessOpenTime = widget.businessHours['openTime'] as int;
        final businessCloseTime = widget.businessHours['closeTime'] as int;

        final openTime = provider.showFullDay ? 0 : businessOpenTime;
        final closeTime = provider.showFullDay ? 24 : businessCloseTime;

        final lunchStart = widget.businessHours['lunchBreak']['start'] as int;
        final lunchEnd = widget.businessHours['lunchBreak']['end'] as int;
        final staff = provider.availableStaff;
        final selectedStaffIds = provider.selectedStaff;
        final visibleStaff =
            staff.where((s) => selectedStaffIds.contains(s.id)).toList();

        // Load staff working hours on demand for visible staff (optimized approach)
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final staffIds = visibleStaff.map((s) => s.id).toList();
          if (staffIds.isNotEmpty) {
            provider.loadStaffWorkingHoursOnDemand(staffIds, reason: 'Day view visible staff');
          }
        });
        final workDays = List<int>.from(widget.businessHours['workDays']);
        final isWorkDay = provider.isWorkingDay(selectedDate);

        final slotHeight = provider.timeSlotHeight; // Dynamic height from provider
        final totalHours = closeTime - openTime;

        // Calculate dynamic column width to fill screen
        final screenWidth = MediaQuery.of(context).size.width;
        const timeColumnWidth = 60.0;
        final availableWidth = screenWidth - timeColumnWidth;

        // Calculate column width and total width
        double staffColumnWidth;
        double totalCalendarWidth;

        if (visibleStaff.isEmpty) {
          staffColumnWidth = 120.0;
          totalCalendarWidth = staffColumnWidth;
        } else if (visibleStaff.length == 1) {
          // Single staff member takes full available width
          staffColumnWidth = availableWidth;
          totalCalendarWidth = availableWidth;
        } else {
          // Multiple staff members: distribute width or use minimum width
          final distributedWidth = availableWidth / visibleStaff.length;
          if (distributedWidth >= 120.0) {
            // Can fit all staff in screen width
            staffColumnWidth = distributedWidth;
            totalCalendarWidth = availableWidth;
          } else {
            // Need horizontal scrolling
            staffColumnWidth = 120.0;
            totalCalendarWidth = visibleStaff.length * staffColumnWidth;
          }
        }

        return Container(
          color: AppColors.appBackground, // Consistent background
          child: Column(
            children: [
              // Combined header and calendar with synchronized scrolling
              Expanded(
                child: GestureDetector(
                  onScaleUpdate: (details) {
                    // Pinch-to-zoom functionality
                    if (details.scale != 1.0) {
                      final currentHeight = provider.timeSlotHeight;
                      final newHeight = (currentHeight * details.scale).clamp(
                        provider.minTimeSlotHeight,
                        provider.maxTimeSlotHeight,
                      );
                      if ((newHeight - currentHeight).abs() > 2) {
                        provider.setTimeSlotHeight(newHeight);
                      }
                    }
                  },
                child: Row(
                  children: [
                    // Fixed time labels column
                    Container(
                      width: 60,
                      color: AppColors.appBackground,
                      child: Column(
                        children: [
                          _buildTimeHeader(),
                          Expanded(
                            child: SingleChildScrollView(
                              controller: _timeScrollController,
                              physics: const NeverScrollableScrollPhysics(),
                              child: Column(
                                children: List.generate(totalHours, (index) {
                                  final hour = openTime + index;
                                  final time = DateTime(
                                    selectedDate.year,
                                    selectedDate.month,
                                    selectedDate.day,
                                    hour,
                                  );
                                  final isCurrentHour =
                                      DateTime.now().hour == hour &&
                                          DateTime.now().day ==
                                              selectedDate.day &&
                                          DateTime.now().month ==
                                              selectedDate.month &&
                                          DateTime.now().year ==
                                              selectedDate.year;

                                  return TimeLabel(
                                    time: time,
                                    isCurrentHour: isCurrentHour,
                                    height: slotHeight, // Pass dynamic height from provider
                                  );
                                }),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Scrollable calendar content
                    Expanded(
                      child: NotificationListener<OverscrollNotification>(
                        onNotification: (notification) {
                          // PageView now handles day navigation, so we only handle staff column scrolling
                          return false;
                        },
                        child: SingleChildScrollView(
                          controller: _horizontalScrollController,
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: totalCalendarWidth,
                            child: Column(
                              children: [
                                _buildWeekdayHeader(selectedDate),
                                _buildSynchronizedStaffHeader(
                                    visibleStaff, staffColumnWidth),
                                Expanded(
                                  child: SingleChildScrollView(
                                    controller: _verticalScrollController,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ...visibleStaff
                                            .map((staffMember) =>
                                                _buildStaffColumn(
                                                  provider,
                                                  staffMember,
                                                  staffColumnWidth,
                                                  slotHeight,
                                                  totalHours,
                                                  openTime,
                                                  closeTime,
                                                  lunchStart,
                                                  lunchEnd,
                                                  isWorkDay,
                                                  selectedDate,
                                                ))
                                            .toList(),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                ), // Close the GestureDetector for pinch-to-zoom
            ),
          ],
        ),
        );
      },
    );
  }

  Widget _buildWeekdayHeader(DateTime selectedDate) {
    // Get the week days (Monday to Sunday) for navigation
    final weekStart = selectedDate.subtract(Duration(days: selectedDate.weekday - 1));
    final weekDays = List.generate(7, (index) => weekStart.add(Duration(days: index)));

    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: AppColors.forestGreen,
        border: Border(
          bottom: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
      ),
      child: Row(
        children: [
          // Time column spacer
          Container(
            width: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Colors.white.withOpacity(0.3)),
              ),
            ),
          ),
          // Weekday navigation
          Expanded(
            child: Row(
              children: weekDays.map((day) {
                final isToday = _isToday(day);
                final isSelected = _isSameDay(day, selectedDate);

                return Expanded(
                  child: GestureDetector(
                    onTap: () {
                      // Navigate to selected day using PageView
                      if (_pageController != null) {
                        final daysDifference = day.difference(_baseDate).inDays;
                        final targetPage = _initialPage + daysDifference;
                        _pageController!.animateToPage(
                          targetPage,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    },
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white.withOpacity(0.2)
                            : Colors.transparent,
                        border: Border(
                          right: BorderSide(
                            color: Colors.white.withOpacity(0.2),
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            DateFormat('EEE', 'ro').format(day),
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.white70,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            day.day.toString(),
                            style: TextStyle(
                              color: isToday
                                  ? Colors.white
                                  : (isSelected ? Colors.white : Colors.white70),
                              fontSize: 14,
                              fontWeight: isToday ? FontWeight.bold : FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSynchronizedStaffHeader(
      List<StaffResponse> staff, double columnWidth) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          // Staff headers
          ...staff
              .map((staffMember) => Container(
                    width: columnWidth,
                    height: 56, // Fixed height to match time header
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: StaffColors.getColorForStaff(
                                  staffMember.id),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              staffMember.displayName,
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: AppColors.forestGreen,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildTimeHeader() {
    return Container(
      width: 60,
      height: 56, // Match the height of staff headers
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(
          right: BorderSide(color: Colors.grey.shade300),
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: const Center(
        child: Text(
          'Ora',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  void _showQuickActionMenu(DateTime slotTime, String staffId) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Programare nouă'),
              onTap: () {
                Navigator.pop(context);
                widget.onTimeSlotTap?.call(slotTime, staffId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Blochează timp'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (_) => BlockTimeDialog(
                    selectedDate: slotTime,
                    preselectedStaffId: staffId,
                    preselectedStartTime: TimeOfDay.fromDateTime(slotTime),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildStaffColumn(
    CalendarProvider provider,
    StaffResponse staffMember,
    double columnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    int lunchStart,
    int lunchEnd,
    bool isWorkDay,
    DateTime selectedDate,
  ) {
    // Get staff-specific working hours for this specific day
    final staffSettings = provider.getStaffWorkingHoursSettings(staffMember.id);
    int staffStartHour = openTime;
    int staffEndHour = closeTime;
    bool hasCustomSchedule = false;
    
    if (staffSettings != null) {
      final dayOfWeek = _getDayOfWeekString(selectedDate);
      final daySchedule = staffSettings.getScheduleForDay(dayOfWeek);
      
      // Debug logging
      print('📅 Staff ${staffMember.name} schedule for $dayOfWeek:');
      print('📅 Day schedule: ${daySchedule?.toJson()}');
      print('📅 Business hours: openTime=$openTime, closeTime=$closeTime');
      
      if (daySchedule != null && daySchedule.isWorkingDay && 
          daySchedule.startTime != null && daySchedule.endTime != null) {
        // Parse staff start and end hours
        final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? openTime;
        final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? closeTime;
        
        print('📅 Parsed hours: startHour=$startHour, endHour=$endHour');
        
        // Use staff hours directly
        staffStartHour = startHour;
        staffEndHour = endHour;
        hasCustomSchedule = true;
        
        print('📅 Final staff hours: staffStartHour=$staffStartHour, staffEndHour=$staffEndHour');
      } else {
        print('📅 Using business hours as fallback');
      }
    } else {
      print('📅 No staff settings found for ${staffMember.id}');
    }

    final staffAppointments = widget.appointments.where((appointment) {
      if (appointment.groomerId != null &&
          appointment.groomerId!.isNotEmpty) {
        return appointment.groomerId == staffMember.id;
      }

      String appointmentStaffName;
      try {
        appointmentStaffName =
            appointment.assignedGroomer?.isNotEmpty == true
                ? appointment.assignedGroomer!
                : 'Ana Popescu';
      } catch (e) {
        appointmentStaffName = 'Ana Popescu';
      }

      return appointmentStaffName == staffMember.name ||
          appointmentStaffName == staffMember.displayName;
    }).toList();

    final dayBlocks = provider.getBlockedTimesForDate(selectedDate);
    final staffBlocks = dayBlocks.where((block) {
      final ids = (block['staffIds'] as List).cast<String>();
      return ids.contains(staffMember.id);
    }).toList();

    return SizedBox(
      width: columnWidth,
      child: Stack(
        children: [
          Column(
            children: List.generate(totalHours, (index) {
              final hour = openTime + index;
              final slotTime = DateTime(
                selectedDate.year,
                selectedDate.month,
                selectedDate.day,
                hour,
              );

              // Check if this hour is within staff working hours
              final isWithinStaffHours = hour >= staffStartHour && hour < staffEndHour;
              
              // Get comprehensive styling information
              final slotStyling = provider.getTimeSlotStyling(slotTime, staffMember.id);

              // Determine if this is a business hour (considering salon closure and staff availability)
              final isBusinessHour = provider.showFullDay
                  ? (slotStyling.isAvailable && isWithinStaffHours)
                  : (slotStyling.isAvailable && isWithinStaffHours);

              final isLunchBreak = hour >= lunchStart && hour < lunchEnd;
              final hasAppointment = staffAppointments.any((apt) =>
                  apt.startTime.hour <= hour && apt.endTime.hour > hour);

              // If outside staff hours, show greyed out slot
              if (!isWithinStaffHours) {
                return Container(
                  height: slotHeight,
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade300),
                    ),
                    color: Colors.grey.shade200,
                  ),
                  child: CustomPaint(
                    painter: DiagonalLinesPainter(
                      color: Colors.grey.shade400,
                      spacing: 8.0,
                    ),
                    size: Size.infinite,
                  ),
                );
              }

              return Container(
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(color: Colors.grey.shade300),
                  ),
                  // Add visual indication for closed periods
                  color: slotStyling.isGreyedOut
                      ? Colors.grey.shade200.withValues(alpha: 0.7)
                      : null,
                ),
                child: Stack(
                  children: [
                    TimeSlot(
                      dateTime: slotTime,
                      isBusinessHour: isBusinessHour && !slotStyling.isGreyedOut,
                      isLunchBreak: isLunchBreak,
                      isAvailable: !hasAppointment && slotStyling.isAvailable,
                      height: slotHeight,
                      onTap: slotStyling.isInteractive
                          ? () => widget.onTimeSlotTap?.call(slotTime, staffMember.id)
                          : null,
                      onLongPress: slotStyling.isInteractive
                          ? () => _showQuickActionMenu(slotTime, staffMember.id)
                          : null,
                    ),
                    // Add diagonal lines for closed periods with proper clipping
                    if (slotStyling.isGreyedOut)
                      ClipRect(
                        child: Container(
                          height: slotHeight,
                          child: CustomPaint(
                            painter: DiagonalLinesPainter(
                              color: Colors.grey.shade400,
                              spacing: 8.0,
                            ),
                            size: Size.infinite,
                          ),
                        ),
                      ),
                    // Add closure reason tooltip
                    if (slotStyling.disabledReason != null)
                      Positioned.fill(
                        child: Tooltip(
                          message: slotStyling.disabledReason!,
                          child: Container(),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),
          ...staffBlocks.map((block) {
                final start = DateTime.parse(block['startTime']).toLocal();
                final end = DateTime.parse(block['endTime']).toLocal();

                final topOffset = ((start.hour - openTime) * slotHeight) +
                    (start.minute / 60 * slotHeight);
                final duration = end.difference(start);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 2,
                  right: 2,
                  child: BlockTimeBlock(
                    block: block,
                    height: blockHeight,
                    onTap: () => widget.onBlockTap?.call(block),
                  ),
                );
          }).toList(),
          ...staffAppointments.map((appointment) {
                final startHour = appointment.startTime.hour;
                final startMinute = appointment.startTime.minute;

                final topOffset = ((startHour - openTime) * slotHeight) +
                    (startMinute / 60 * slotHeight);
                final duration =
                    appointment.endTime.difference(appointment.startTime);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 4, // Increased margin for better visual separation
                  right: 4, // Increased margin for better visual separation
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: 30, // Minimum height for readability
                      maxWidth: columnWidth - 8, // Ensure proper width constraints
                    ),
                    child: AppointmentBlock(
                      appointment: appointment,
                      height: blockHeight.clamp(30.0, double.infinity), // Ensure minimum height
                      onTap: () => widget.onAppointmentTap(appointment),
                      isCompact: false, // Use full layout in day view for better readability
                    ),
                  ),
                );
          }).toList(),
          if (_isToday(selectedDate))
            _buildCurrentTimeIndicator(openTime, slotHeight),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Widget _buildCurrentTimeIndicator(int openTime, double slotHeight) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    // In full day mode, show indicator for any hour; in business mode, only during business hours
    final businessCloseTime = widget.businessHours['closeTime'] as int;
    final showFullDay = context.read<CalendarProvider>().showFullDay;

    if (!showFullDay && (currentHour < openTime || currentHour >= businessCloseTime)) {
      return const SizedBox.shrink();
    }

    if (showFullDay && (currentHour < openTime || currentHour >= 24)) {
      return const SizedBox.shrink();
    }

    final topOffset = ((currentHour - openTime) * slotHeight) +
        (currentMinute / 60 * slotHeight);

    return Positioned(
      top: topOffset,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: Colors.red,
        child: Row(
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helper method to get day of week string
  String _getDayOfWeekString(DateTime date) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[date.weekday - 1];
  }
}

/// Enhanced diagonal lines painter for closed periods
class DiagonalLinesPainter extends CustomPainter {
  final Color color;
  final double spacing;
  final double strokeWidth;

  DiagonalLinesPainter({
    this.color = Colors.grey,
    this.spacing = 10.0,
    this.strokeWidth = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
