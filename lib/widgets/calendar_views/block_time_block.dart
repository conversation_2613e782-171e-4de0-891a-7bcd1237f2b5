import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../config/theme/app_theme.dart';

class BlockTimeBlock extends StatelessWidget {
  final Map<String, dynamic> block;
  final double height;
  final VoidCallback? onTap;

  const BlockTimeBlock({
    Key? key,
    required this.block,
    required this.height,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final start = DateTime.parse(block['startTime']).toLocal();
    final end = DateTime.parse(block['endTime']).toLocal();
    final reason = (block['customReason'] ?? block['reason'] ?? '') as String;
    final duration = end.difference(start).inMinutes;

    final tooltip =
        '$reason\n${DateFormat.Hm().format(start)} - ${DateFormat.Hm().format(end)} ($duration min)';

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Tooltip(
          message: tooltip,
          preferBelow: false,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: height,
              margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
              decoration: BoxDecoration(
                color: themeProvider.isDarkMode
                    ? AppColors.darkBorder
                    : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: themeProvider.isDarkMode
                      ? AppColors.darkTextTertiary
                      : Colors.grey.shade500,
                ),
              ),
              child: CustomPaint(
                painter: _DiagonalLinesPainter(themeProvider: themeProvider),
                child: Center(
                  child: Text(
                    reason,
                    style: TextStyle(
                      color: themeProvider.getTextColor(context),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _DiagonalLinesPainter extends CustomPainter {
  final ThemeProvider? themeProvider;

  const _DiagonalLinesPainter({this.themeProvider});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = (themeProvider?.isDarkMode == true
          ? AppColors.darkTextTertiary
          : Colors.grey.shade500).withValues(alpha: 0.4)
      ..strokeWidth = 1;

    const spacing = 6.0;
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
