import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/theme_provider.dart';
import '../../config/theme/app_theme.dart';

class MonthView extends StatefulWidget {
  final DateTime selectedMonth;
  final Map<DateTime, List<Appointment>> appointmentsByDay;
  final Function(Appointment) onAppointmentTap;
  final Function(DateTime) onDayTap;
  final VoidCallback? onSwipeToNext;
  final VoidCallback? onSwipeToPrevious;

  const MonthView({
    super.key,
    required this.selectedMonth,
    required this.appointmentsByDay,
    required this.onAppointmentTap,
    required this.onDayTap,
    this.onSwipeToNext,
    this.onSwipeToPrevious,
  });

  @override
  State<MonthView> createState() => _MonthViewState();
}

class _MonthViewState extends State<MonthView> {
  late PageController _pageController;
  double _panStartX = 0;
  double _panUpdateX = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            _buildMonthHeader(),
            _buildWeekdayHeaders(),
            Expanded(
              child: GestureDetector(
                onPanStart: _onPanStart,
                onPanUpdate: _onPanUpdate,
                onPanEnd: _onPanEnd,
                child: _buildMonthGrid(provider),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMonthHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: widget.onSwipeToPrevious,
            icon: Icon(Icons.chevron_left,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 28),
          ),
          Text(
            DateFormat('MMMM yyyy', 'ro').format(widget.selectedMonth),
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            onPressed: widget.onSwipeToNext,
            icon: Icon(Icons.chevron_right,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 28),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = ['L', 'M', 'M', 'J', 'V', 'S', 'D'];
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Row(
        children: weekdays.map((day) {
          return Expanded(
            child: Center(
              child: Text(
                day,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: 14,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildMonthGrid(CalendarProvider provider) {
    final firstDayOfMonth = DateTime(widget.selectedMonth.year, widget.selectedMonth.month, 1);

    // Calculate the first day to show (start of week containing first day of month)
    final firstDayToShow = firstDayOfMonth.subtract(Duration(days: (firstDayOfMonth.weekday - 1) % 7));

    // Calculate total days to show (6 weeks = 42 days)
    const totalDays = 42;
    
    return Container(
      color: Theme.of(context).colorScheme.background, // Use theme-aware background
      child: GridView.builder(
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1.0,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        itemCount: totalDays,
        itemBuilder: (context, index) {
          final date = firstDayToShow.add(Duration(days: index));
          final isCurrentMonth = date.month == widget.selectedMonth.month;
          final isToday = _isSameDay(date, DateTime.now());
          final appointments = widget.appointmentsByDay[_dateKey(date)] ?? [];

          return _buildDayCell(
            date: date,
            isCurrentMonth: isCurrentMonth,
            isToday: isToday,
            appointments: appointments,
            provider: provider,
          );
        },
      ),
    );
  }

  Widget _buildDayCell({
    required DateTime date,
    required bool isCurrentMonth,
    required bool isToday,
    required List<Appointment> appointments,
    required CalendarProvider provider,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isWorkingDay = provider.isWorkingDay(date);
        final closureInfo = provider.getDateClosureInfo(date);
        final isHoliday = closureInfo.isRomanianHoliday;
        final isClosed = closureInfo.isSalonClosed;

        final backgroundColor = themeProvider.getCalendarCellColor(
          context,
          isCurrentMonth: isCurrentMonth,
          isToday: isToday,
          isWorkingDay: isWorkingDay && !isHoliday && !isClosed,
        );

        final textColor = themeProvider.getCalendarTextColor(
          context,
          isCurrentMonth: isCurrentMonth,
          isToday: isToday,
          isWorkingDay: isWorkingDay && !isHoliday && !isClosed,
        );

        final borderColor = themeProvider.getCalendarBorderColor(
          context,
          isToday: isToday,
        );

        return GestureDetector(
          onTap: isCurrentMonth ? () => widget.onDayTap(date) : null,
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              border: Border.all(
                color: borderColor,
                width: isToday ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // Day number
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                date.day.toString(),
                style: TextStyle(
                  color: textColor,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
            ),
            // Appointment indicators
            if (appointments.isNotEmpty && isCurrentMonth) ...[
              SizedBox(height: 2),
              Expanded(
                child: _buildAppointmentIndicators(appointments),
              ),
            ],
          ],
        ),
      ),
        );
      },
    );
  }

  Widget _buildAppointmentIndicators(List<Appointment> appointments) {
    // Show up to 3 appointment dots, with overflow indicator
    final maxVisible = 3;
    final visibleAppointments = appointments.take(maxVisible).toList();
    final hasOverflow = appointments.length > maxVisible;

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ...visibleAppointments.map((appointment) {
              return Container(
                margin: const EdgeInsets.symmetric(vertical: 1),
                height: 4,
                width: 20,
                decoration: BoxDecoration(
                  color: _getAppointmentColor(appointment),
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            }),
            if (hasOverflow) ...[
              SizedBox(height: 1),
              Text(
                '+${appointments.length - maxVisible}',
                style: TextStyle(
                  fontSize: 10,
                  color: themeProvider.getSecondaryTextColor(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Color _getAppointmentColor(Appointment appointment) {
    // Color code appointments by status - theme-aware
    switch (appointment.status.toLowerCase()) {
      case 'confirmed':
        return AppColors.darkAccent;
      case 'pending':
        return AppColors.darkWarning;
      case 'completed':
        return AppColors.darkInfo;
      case 'canceled':
        return AppColors.darkError;
      default:
        return AppColors.darkAccent;
    }
  }

  DateTime _dateKey(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  void _onPanStart(DragStartDetails details) {
    _panStartX = details.globalPosition.dx;
  }

  void _onPanUpdate(DragUpdateDetails details) {
    _panUpdateX = details.globalPosition.dx;
  }

  void _onPanEnd(DragEndDetails details) {
    final deltaX = _panUpdateX - _panStartX;
    const threshold = 100.0;

    if (deltaX > threshold) {
      // Swipe right - go to previous month
      widget.onSwipeToPrevious?.call();
    } else if (deltaX < -threshold) {
      // Swipe left - go to next month
      widget.onSwipeToNext?.call();
    }
  }
}
