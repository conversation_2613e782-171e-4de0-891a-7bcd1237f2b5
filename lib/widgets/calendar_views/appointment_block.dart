import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';

class AppointmentBlock extends StatelessWidget {
  final Appointment appointment;
  final double height;
  final VoidCallback? onTap;
  final bool isCompact;

  const AppointmentBlock({
    Key? key,
    required this.appointment,
    required this.height,
    this.onTap,
    this.isCompact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final appointmentColor = _getAppointmentColor(provider);

        return GestureDetector(
          onTap: onTap,
          child: Container(
            height: height,
            margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
            decoration: BoxDecoration(
              color: appointmentColor,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: appointmentColor.withValues(alpha: 0.8),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Ultra-compact display for very small blocks
                  if (height <= 25) ...[
                    Flexible(
                      child: _buildUltraCompactInfo(),
                    ),
                  ] else ...[
                    // Time range - show only in non-compact mode with sufficient space
                    if (!isCompact && height > 60) ...[
                      Text(
                        appointment.timeRange,
                        style: TextStyle(
                          color: _isAppointmentCanceled() ? Colors.grey.shade600 : Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 2),
                    ],

                    // Primary information: Client name and pet name
                    Flexible(
                      child: _buildClientPetInfo(isCompact),
                    ),

                    // Service information - prioritized display
                    if (height > 35) ...[
                      SizedBox(height: 2),
                      Flexible(
                        child: _buildServiceInfo(isCompact),
                      ),
                    ],

                    // Show "Anulată" for canceled appointments
                    if (_isAppointmentCanceled() && height > 45) ...[
                      SizedBox(height: 2),
                      Text(
                        'ANULATĂ',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ],
                  // Staff name removed to fix calendar alignment issues - using colors for identification
                  if (!isCompact && appointment.isPaid) ...[
                    SizedBox(height: 2),
                    Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 12,
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getAppointmentColor(CalendarProvider provider) {
    // Enhanced elegant color coding system
    final baseColor = _getBaseStaffColor(provider);

    // Apply subtle status-based modifications to the base color
    switch (appointment.status.toLowerCase()) {
      case 'canceled':
      case 'anulat':
      case 'cancelled':
        return Colors.grey.withOpacity(0.8); // Subtle grey for cancelled
      case 'pending':
      case 'in asteptare':
        return baseColor.withOpacity(0.6); // Reduced opacity for pending
      case 'confirmed':
      case 'confirmat':
        return baseColor; // Full color for confirmed
      case 'finalizat':
      case 'completed':
        return _darkenColor(baseColor, 0.1); // Slightly darker for completed
      case 'rescheduled':
        return _lightenColor(baseColor, 0.2); // Lighter for rescheduled
      default:
        return baseColor;
    }
  }

  Color _getBaseStaffColor(CalendarProvider provider) {
    // Get staff-based color with elegant fallbacks
    if (appointment.groomerId != null) {
      try {
        final staff = provider.availableStaff.firstWhere(
          (s) => s.id == appointment.groomerId,
        );
        return _getElegantStaffColor(provider.getStaffColor(staff.id));
      } catch (e) {
        // Fallback to name-based lookup
        try {
          final staff = provider.availableStaff.firstWhere(
            (s) => s.name == appointment.assignedGroomer,
          );
          return _getElegantStaffColor(provider.getStaffColor(staff.id));
        } catch (e) {
          return _getElegantStaffColor(const Color(0xFF2E7D32)); // Default forest green
        }
      }
    } else {
      // Name-based lookup for backward compatibility
      try {
        final staff = provider.availableStaff.firstWhere(
          (s) => s.name == appointment.assignedGroomer,
        );
        return _getElegantStaffColor(provider.getStaffColor(staff.id));
      } catch (e) {
        return _getElegantStaffColor(const Color(0xFF2E7D32)); // Default forest green
      }
    }
  }

  Color _getElegantStaffColor(Color originalColor) {
    // Make staff colors more subtle and professional
    return originalColor.withOpacity(0.85);
  }

  Color _darkenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0)).toColor();
  }

  Color _lightenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0)).toColor();
  }

  // Helper method to check if appointment is canceled
  bool _isAppointmentCanceled() {
    final status = appointment.status.toLowerCase();
    return status == 'canceled' || status == 'anulat' || status == 'cancelled';
  }

  /// Build client and pet information display
  Widget _buildClientPetInfo(bool isCompact) {
    final clientName = appointment.clientName.isNotEmpty ? appointment.clientName : 'Client necunoscut';
    final petName = appointment.petName.isNotEmpty ? appointment.petName : 'Animal necunoscut';

    if (isCompact) {
      // Compact format: "Client - Pet" on single line
      return Text(
        '$clientName - $petName',
        style: TextStyle(
          color: _isAppointmentCanceled() ? Colors.grey.shade600 : Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    } else {
      // Non-compact format: Better spacing and readability
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Client name (primary)
          Text(
            clientName,
            style: TextStyle(
              color: _isAppointmentCanceled() ? Colors.grey.shade600 : Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          // Pet name (secondary)
          if (petName.isNotEmpty) ...[
            SizedBox(height: 1),
            Text(
              '🐾 $petName',
              style: TextStyle(
                color: _isAppointmentCanceled() ? Colors.grey.shade500 : Colors.white70,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      );
    }
  }

  /// Build ultra-compact display for very small appointment blocks
  Widget _buildUltraCompactInfo() {
    final clientName = appointment.clientName.isNotEmpty ? appointment.clientName : 'Client';
    final petName = appointment.petName.isNotEmpty ? appointment.petName : 'Pet';
    final serviceName = appointment.service.isNotEmpty ? appointment.service : 'Serviciu';

    // Truncate names if too long
    final shortClient = clientName.length > 8 ? '${clientName.substring(0, 8)}...' : clientName;
    final shortPet = petName.length > 6 ? '${petName.substring(0, 6)}...' : petName;
    final shortService = serviceName.length > 10 ? '${serviceName.substring(0, 10)}...' : serviceName;

    return Tooltip(
      message: '${appointment.clientName} - ${appointment.petName}\n${appointment.service}\n${appointment.timeRange}',
      child: Text(
        '$shortClient - $shortPet | $shortService',
        style: TextStyle(
          color: _isAppointmentCanceled() ? Colors.grey.shade600 : Colors.white,
          fontSize: 9,
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Build service information display
  Widget _buildServiceInfo(bool isCompact) {
    final serviceName = appointment.service.isNotEmpty ? appointment.service : 'Serviciu general';

    return Tooltip(
      message: serviceName,
      child: Text(
        serviceName,
        style: TextStyle(
          color: _isAppointmentCanceled() ? Colors.grey.shade500 : Colors.white70,
          fontSize: isCompact ? 9 : 10,
          fontWeight: FontWeight.w500,
          fontStyle: FontStyle.italic,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}


