import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import '../../models/working_hours_settings.dart';

/// Quick schedule templates for common patterns
class ScheduleTemplates extends StatefulWidget {
  final Function(Map<String, DaySchedule>) onTemplateSelected;

  const ScheduleTemplates({
    Key? key,
    required this.onTemplateSelected,
  }) : super(key: key);

  @override
  State<ScheduleTemplates> createState() => _ScheduleTemplatesState();
}

class _ScheduleTemplatesState extends State<ScheduleTemplates> {
  int? _selectedIndex;
  int? _hoverIndex;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Șabloane rapide',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 12),
        Text(
          'Selectează un șablon pentru a configura rapid programul',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        SizedBox(height: 16),

        // Template grid
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1.4,
          children: _templates.asMap().entries.map((entry) {
            return _buildTemplateCard(entry.value, entry.key);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTemplateCard(ScheduleTemplate template, int index) {
    final isSelected = _selectedIndex == index;
    final isHovered = _hoverIndex == index;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoverIndex = index),
      onExit: (_) => setState(() => _hoverIndex = null),
      child: InkWell(
        onTap: () {
          setState(() => _selectedIndex = index);
          widget.onTemplateSelected(template.schedule);
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? template.color
                : isHovered
                    ? template.color.withOpacity(0.5)
                    : Colors.grey.shade200,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon and title
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: template.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    template.icon,
                    color: template.color,
                    size: 16,
                  ),
                ),
                SizedBox(width: 6),
                Expanded(
                  child: Text(
                    template.name,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            SizedBox(height: 8),

            // Description
            Expanded(
              child: Text(
                template.description,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            SizedBox(height: 8),

            // Working days indicator
            Wrap(
              spacing: 1,
              children: _weekDays.map((day) {
                final isWorking = template.schedule[day]?.isWorkingDay ?? false;
                return Container(
                  width: 14,
                  height: 14,
                  decoration: BoxDecoration(
                    color: isWorking
                        ? template.color
                        : Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Center(
                    child: Text(
                      _getDayInitial(day),
                      style: TextStyle(
                        fontSize: 7,
                        fontWeight: FontWeight.bold,
                        color: isWorking ? Colors.white : Colors.grey.shade400,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    ));
  }

  String _getDayInitial(String day) {
    switch (day) {
      case 'monday': return 'L';
      case 'tuesday': return 'Ma';
      case 'wednesday': return 'Mi';
      case 'thursday': return 'J';
      case 'friday': return 'V';
      case 'saturday': return 'S';
      case 'sunday': return 'D';
      default: return '';
    }
  }

  static const List<String> _weekDays = [
    'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
  ];

  static final List<ScheduleTemplate> _templates = [
    ScheduleTemplate(
      name: 'Program standard',
      description: 'Luni-Vineri 9:00-17:00\nSâmbătă 10:00-15:00',
      icon: Icons.business,
      color: const Color(0xFF2E7D32), // Forest green for templates
      schedule: {
        'monday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: '10:00',
          endTime: '15:00',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),

    ScheduleTemplate(
      name: 'Doar săptămâna',
      description: 'Luni-Vineri 8:00-18:00\nWeekend liber',
      icon: Icons.work,
      color: Colors.blue,
      schedule: {
        'monday': const DaySchedule(
          startTime: '08:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '08:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '08:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '08:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '08:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),

    ScheduleTemplate(
      name: 'Program extins',
      description: 'Luni-Sâmbătă 8:00-20:00\nProgram lung',
      icon: Icons.schedule,
      color: Colors.orange,
      schedule: {
        'monday': const DaySchedule(
          startTime: '08:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '08:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '08:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '08:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '08:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: '08:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),

    ScheduleTemplate(
      name: 'Weekend focus',
      description: 'Joi-Duminică\nPentru saloane weekend',
      icon: Icons.weekend,
      color: Colors.purple,
      schedule: {
        'monday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: '10:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'friday': const DaySchedule(
          startTime: '10:00',
          endTime: '20:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'saturday': const DaySchedule(
          startTime: '09:00',
          endTime: '19:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'sunday': const DaySchedule(
          startTime: '10:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),

    // Non-stop schedule template
    ScheduleTemplate(
      name: 'Program non-stop',
      description: 'Deschis permanent 24/7',
      icon: Icons.timelapse,
      color: Colors.redAccent,
      schedule: {
        'monday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'saturday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),
  ];
}

/// Model for schedule templates
class ScheduleTemplate {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final Map<String, DaySchedule> schedule;

  const ScheduleTemplate({
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.schedule,
  });
}
