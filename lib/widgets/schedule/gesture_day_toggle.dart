import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/theme/app_theme.dart';

/// Gesture-based day toggle with swipe interactions and visual feedback
class GestureDayToggle extends StatefulWidget {
  final String dayName;
  final bool isWorkingDay;
  final String? startTime;
  final String? endTime;
  final Function(bool) onToggle;
  final VoidCallback? onTap;

  const GestureDayToggle({
    Key? key,
    required this.dayName,
    required this.isWorkingDay,
    this.startTime,
    this.endTime,
    required this.onToggle,
    this.onTap,
  }) : super(key: key);

  @override
  State<GestureDayToggle> createState() => _GestureDayToggleState();
}

class _GestureDayToggleState extends State<GestureDayToggle> 
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _slideController;
  late AnimationController _colorController;
  
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<Color?> _colorAnimation;
  
  bool _isDragging = false;
  double _dragOffset = 0.0;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _colorController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.1, 0),
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.elasticOut));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize color animation here where Theme.of(context) is safe to use
    _colorAnimation = ColorTween(
      begin: Colors.grey.shade100,
      end: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
    ).animate(CurvedAnimation(parent: _colorController, curve: Curves.easeInOut));

    if (widget.isWorkingDay) {
      _colorController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(GestureDayToggle oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isWorkingDay != oldWidget.isWorkingDay) {
      if (widget.isWorkingDay) {
        _colorController.forward();
      } else {
        _colorController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _slideController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _slideAnimation,
        _colorAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SlideTransition(
            position: _slideAnimation,
            child: GestureDetector(
              onTap: () {
                _triggerHapticFeedback();
                widget.onTap?.call();
              },
              onTapDown: (_) => _scaleController.forward(),
              onTapUp: (_) => _scaleController.reverse(),
              onTapCancel: () => _scaleController.reverse(),
              onHorizontalDragStart: (details) {
                _isDragging = true;
                _dragOffset = 0.0;
              },
              onHorizontalDragUpdate: (details) {
                if (_isDragging) {
                  setState(() {
                    _dragOffset += details.delta.dx;
                  });
                  
                  // Trigger slide animation when drag exceeds threshold
                  if (_dragOffset.abs() > 50) {
                    _slideController.forward().then((_) {
                      _slideController.reverse();
                    });
                    
                    // Toggle state based on drag direction
                    final shouldBeWorking = _dragOffset > 0;
                    if (shouldBeWorking != widget.isWorkingDay) {
                      _triggerHapticFeedback();
                      widget.onToggle(shouldBeWorking);
                    }
                    
                    _isDragging = false;
                    _dragOffset = 0.0;
                  }
                }
              },
              onHorizontalDragEnd: (details) {
                _isDragging = false;
                _dragOffset = 0.0;
              },
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _colorAnimation.value,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: widget.isWorkingDay
                        ? const Color(0xFF2E7D32)
                        : Colors.grey.shade300,
                    width: widget.isWorkingDay ? 2 : 1,
                  ),
                  boxShadow: widget.isWorkingDay ? [
                    BoxShadow(
                      color: const Color(0xFF2E7D32).withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Day indicator
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: widget.isWorkingDay
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          _getShortDayName(widget.dayName),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: widget.isWorkingDay
                                ? Colors.white
                                : Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(width: 16),

                    // Day name and status
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.dayName,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: widget.isWorkingDay
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.black87,
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            widget.isWorkingDay
                                ? '${widget.startTime ?? '--:--'} - ${widget.endTime ?? '--:--'}'
                                : 'Zi liberă',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Toggle indicator
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: 50,
                      height: 28,
                      decoration: BoxDecoration(
                        color: widget.isWorkingDay
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(14),
                      ),
                      child: AnimatedAlign(
                        duration: const Duration(milliseconds: 200),
                        alignment: widget.isWorkingDay
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Container(
                          width: 24,
                          height: 24,
                          margin: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            widget.isWorkingDay ? Icons.check : Icons.close,
                            size: 14,
                            color: widget.isWorkingDay
                                ? Theme.of(context).colorScheme.primary
                                : Colors.grey.shade400,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(width: 8),

                    // Edit indicator
                    Icon(
                      Icons.edit,
                      size: 16,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _getShortDayName(String dayName) {
    switch (dayName.toLowerCase()) {
      case 'luni':
        return 'L';
      case 'marți':
        return 'Ma';
      case 'miercuri':
        return 'Mi';
      case 'joi':
        return 'J';
      case 'vineri':
        return 'V';
      case 'sâmbătă':
        return 'S';
      case 'duminică':
        return 'D';
      default:
        return dayName.substring(0, 1).toUpperCase();
    }
  }

  void _triggerHapticFeedback() {
    HapticFeedback.lightImpact();
  }
}
