import 'package:animaliaproject/services/salon_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_role.dart';
import '../models/user_salon_association.dart';
import '../services/auth/auth_service.dart';
import 'base_provider.dart';

/// Provider for managing user roles and permissions throughout the app
class RoleProvider extends BaseProvider {
  UserRole? _currentRole;
  GroomerPermissions? _permissions;
  List<String> _accessibleFeatures = [];

  // Getters
  UserRole? get currentRole => _currentRole;
  GroomerPermissions? get permissions => _permissions;
  List<String> get accessibleFeatures => _accessibleFeatures;

  // Role checks
  bool get isAdmin => _currentRole?.isAdmin ?? false;
  bool get canBeGroomer => _currentRole?.canBeGroomer ?? false;
  bool get isActiveGroomer => canBeGroomer && (_permissions?.isActive ?? false);
  bool get hasManagementAccess => isAdmin || (_permissions?.hasManagementAccess ?? false);

  // Salon association check
  bool get hasSalonAssociation => _permissions != null;

  // Fixed: Only allow client data access if user has valid permissions AND explicit access
  bool get canAccessClientData {
    if (isAdmin) return true;
    if (_permissions == null) return false; // No permissions = no access
    return _permissions!.canAccessClientData;
  }

  bool get canSeeFullClientDetails {
    if (isAdmin) return true;
    if (_permissions == null) return false; // No permissions = no access
    return _permissions!.canSeeFullClientDetails;
  }

  // Sales data access requires management access
  bool canViewSalesData() {
    return hasManagementAccess;
  }

  /// Initialize role and permissions
  @override
  Future<void> initialize() async {
    debugPrint('🔄 RoleProvider.initialize() started');
    await executeVoidAsync(
      () async {
        debugPrint('🔄 RoleProvider: Loading role and permissions...');
        await _loadRoleAndPermissions();
        debugPrint('🔄 RoleProvider: Loading accessible features...');
        await _loadAccessibleFeatures();
        debugPrint('✅ RoleProvider: Setting initialized to true');
        setInitialized(true);
        debugPrint('✅ RoleProvider.initialize() completed successfully');
        debugPrint('🔍 RoleProvider: hasSalonAssociation = $hasSalonAssociation');
      },
      errorMessage: 'Failed to initialize roles and permissions',
    );
  }

  /// Load user role and permissions from storage/API
  Future<void> _loadRoleAndPermissions() async {
    _currentRole = await _getCurrentUserRole();
    _permissions = await _loadPermissionsFromSalonAssociation();
  }

  /// Load list of accessible features
  Future<void> _loadAccessibleFeatures() async {
    _accessibleFeatures = await _getAccessibleFeatures();
  }

  /// Refresh role and permissions from server
  @override
  Future<void> refresh() async {
    debugPrint('🔄 RoleProvider.refresh() called');
    await executeVoidAsync(
      () async {
        debugPrint('🔄 RoleProvider: Starting role and permissions reload...');
        await _loadRoleAndPermissions();
        debugPrint('🔄 RoleProvider: Role and permissions loaded, hasSalonAssociation = $hasSalonAssociation');
        await _loadAccessibleFeatures();
        debugPrint('🔄 RoleProvider.refresh() completed successfully');
        debugPrint('🔍 RoleProvider: Final state - hasSalonAssociation = $hasSalonAssociation');
      },
      errorMessage: 'Failed to refresh roles and permissions',
    );
  }

  /// Clear all role data (call on logout)
  @override
  void clear() {
    _currentRole = null;
    _permissions = null;
    _accessibleFeatures = [];
    super.clear();
  }

  /// Check if user can access a specific feature
  bool canAccessFeature(String featureKey) {
    return _accessibleFeatures.contains(featureKey);
  }

  /// Permission check methods for UI
  bool canManageTeam() => canAccessFeature('team_management');
  bool canManageServices() => canAccessFeature('services_management');
  bool canViewReports() => canAccessFeature('reports');
  bool canManageSalonSettings() => canAccessFeature('salon_settings');
  bool canInviteGroomers() => hasManagementAccess;
  bool canViewAllAppointments() => canAccessFeature('calendar_all');
  bool canOnlyViewOwnAppointments() => canAccessFeature('calendar_own') && !canAccessFeature('calendar_all');
  bool canManageBusinessHours() => hasManagementAccess;
  bool canSendSmsReminders() => hasManagementAccess;
  // bool canViewSalesData() => canAccessFeature('sales');
  bool canManageReviews() => canAccessFeature('reviews');

  /// Get role display information
  String get roleDisplayName {
    if (isAdmin) {
      return 'Administrator';
    } else if (isActiveGroomer && _permissions != null) {
      return _permissions!.groomerRole.displayName;
    } else {
      return _currentRole?.displayName ?? 'Necunoscut';
    }
  }

  String get roleDescription {
    if (isAdmin) {
      return 'Acces complet la toate funcționalitățile';
    } else if (isActiveGroomer && _permissions != null) {
      return _permissions!.groomerRole.description;
    } else {
      return _currentRole?.description ?? '';
    }
  }

  /// Get permission display information
  String get clientDataPermissionName => _permissions?.clientDataPermission.displayName ?? 'Necunoscut';
  String get clientDataPermissionDescription => _permissions?.clientDataPermission.description ?? '';

  /// Check if user has pending invitations
  Future<bool> hasPendingInvitations() async {
    try {
      final userId = await AuthService.getCurrentUserId();
      if (userId == null) return false;

      // This would be implemented to check for pending salon invitations
      // For now, return false as placeholder
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Accept salon invitation
  Future<bool> acceptSalonInvitation(String invitationId) async {
    try {
      // Implementation would call API to accept invitation
      // and then refresh permissions
      await refresh();
      return true;
    } catch (e) {
      setError(e.toString());
      return false;
    }
  }

  /// Decline salon invitation
  Future<bool> declineSalonInvitation(String invitationId) async {
    try {
      // Implementation would call API to decline invitation
      return true;
    } catch (e) {
      setError(e.toString());
      return false;
    }
  }

  /// Clear error (inherited from BaseProvider)
  // clearError() method is already available from BaseProvider

  /// Get current user role from AuthService
  Future<UserRole?> _getCurrentUserRole() async {
    try {
      final userRole = await AuthService.getCurrentUserRole();
      if (userRole != null) {
        return UserRole.fromString(userRole);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting user role: $e');
      return null;
    }
  }

  /// Load permissions directly from salon associations
  Future<GroomerPermissions?> _loadPermissionsFromSalonAssociation() async {
    try {
      debugPrint('🔄 RoleProvider: Loading permissions from salon association...');
      final userId = await AuthService.getCurrentUserId();
      final salonId = await AuthService.getCurrentSalonId();

      debugPrint('🔍 RoleProvider: userId = $userId, salonId = $salonId');

      if (userId == null) {
        debugPrint('🔍 RoleProvider: Missing userId for permissions');
        return null;
      }

      // Get user's salon associations first
      debugPrint('🔄 RoleProvider: Fetching user salons from API...');
      final salonResponse = await SalonService.getUserSalons();

      if (!salonResponse.success || salonResponse.data == null) {
        debugPrint('❌ RoleProvider: Failed to fetch user salons: ${salonResponse.error}');
        return null;
      }

      final userSalons = salonResponse.data!;

      // If user has no salons, return null
      if (userSalons.isEmpty) {
        debugPrint('🔍 RoleProvider: User has no salon associations');
        if (salonId != null) {
          // Clear invalid salon ID
          debugPrint('🔄 RoleProvider: Clearing invalid salon ID');
          await AuthService.updateCurrentSalonId(null);
        }
        return null;
      }

      // If no current salon ID is set, or current salon ID is invalid, switch to first available salon
      if (salonId == null || !userSalons.any((salon) => salon.salonId == salonId)) {
        final newSalonId = userSalons.first.salonId;
        debugPrint('🔄 RoleProvider: Current salon ID invalid or missing, switching to: $newSalonId');
        await AuthService.updateCurrentSalonId(newSalonId);
        // Update local variable for the rest of this method
        final updatedSalonId = newSalonId;

        // Find the salon association for the new salon ID
        final salonAssociation = userSalons.firstWhere((salon) => salon.salonId == updatedSalonId);
        return _createPermissionsFromAssociation(userId, updatedSalonId, salonAssociation);
      }

      // Current salon ID is valid, find the association
      final salonAssociation = userSalons.firstWhere(
        (salon) => salon.salonId == salonId,
        orElse: () => throw Exception('Salon association not found'),
      );

      return _createPermissionsFromAssociation(userId, salonId, salonAssociation);
    } catch (e) {
      debugPrint('❌ RoleProvider: Error loading permissions from salon association: $e');
      return null;
    }
  }

  /// Create GroomerPermissions from salon association
  GroomerPermissions _createPermissionsFromAssociation(
    String userId,
    String salonId,
    UserSalonAssociation salonAssociation,
  ) {
    debugPrint('✅ RoleProvider: Found salon association for salon: $salonId');
    debugPrint('🔍 RoleProvider: Role = ${salonAssociation.groomerRole}, Permission = ${salonAssociation.clientDataPermission}');

    final permissions = GroomerPermissions(
      userId: userId,
      salonId: salonId,
      groomerRole: salonAssociation.groomerRole,
      clientDataPermission: salonAssociation.clientDataPermission,
      isActive: true,
      updatedAt: DateTime.now(),
    );

    debugPrint('✅ RoleProvider: Created permissions for salon: $salonId');
    return permissions;
  }

  /// Get list of features accessible to current user
  Future<List<String>> _getAccessibleFeatures() async {
    final List<String> features = [];

    // Debug logging for troubleshooting
    debugPrint('🔍 RoleProvider._getAccessibleFeatures() called');
    debugPrint('🔍 hasManagementAccess: $hasManagementAccess');
    debugPrint('🔍 _permissions: $_permissions');
    debugPrint('🔍 _permissions?.groomerRole: ${_permissions?.groomerRole}');
    debugPrint('🔍 hasSalonAssociation: $hasSalonAssociation');

    // If user has no salon association, they get no features except basic profile access
    if (!hasSalonAssociation) {
      debugPrint('🔍 No salon association - user needs to create or join a salon');
      return features; // Return empty features list
    }

    // Calendar access logic - avoid circular dependency
    // Check permissions directly instead of using canViewAllAppointments()
    if (hasManagementAccess || (_permissions?.groomerRole == GroomerRole.chiefGroomer)) {
      features.add('calendar_all');
      debugPrint('🔍 Added calendar_all access');
    } else {
      features.add('calendar_own');
      debugPrint('🔍 Added calendar_own access');
    }

    if (canAccessClientData) {
      features.add('clients');
      debugPrint('🔍 Added clients access');
    }

    if (canViewSalesData()) {
      features.add('sales');
      debugPrint('🔍 Added sales access');
    }

    // Team management - check permissions directly
    if (hasManagementAccess) {
      features.add('team_management');
      debugPrint('🔍 Added team_management access');
    }

    // Services management - check permissions directly
    if (hasManagementAccess) {
      features.add('services_management');
      debugPrint('🔍 Added services_management access');
    }

    // Reports - check permissions directly
    if (hasManagementAccess) {
      features.add('reports');
      debugPrint('🔍 Added reports access');
    }

    // Salon settings - check permissions directly
    if (hasManagementAccess) {
      features.add('salon_settings');
      debugPrint('🔍 Added salon_settings access');
    }

    if (hasManagementAccess) {
      features.add('sms_reminders');
      debugPrint('🔍 Added sms_reminders access');
    }

    // Reviews - check permissions directly
    if (hasManagementAccess) {
      features.add('reviews');
      debugPrint('🔍 Added reviews access');
    }

    debugPrint('🔍 Final accessible features: $features');
    return features;
  }
}
