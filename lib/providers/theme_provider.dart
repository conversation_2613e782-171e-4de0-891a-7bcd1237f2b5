import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/theme/app_theme.dart';

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  
  ThemeMode _themeMode = ThemeMode.light;
  bool _isInitialized = false;

  ThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isInitialized => _isInitialized;

  // Light theme data
  ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.forestGreen,
      brightness: Brightness.light,
    ),
    primaryColor: AppColors.forestGreen,
    scaffoldBackgroundColor: AppColors.appBackground,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.forestGreen,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: false,
    ),
    cardTheme: CardThemeData(
      elevation: AppDimensions.elevationLow,
      shape: RoundedRectangleBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
      ),
      color: Colors.white,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.forestGreen,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.forestGreen, width: 2),
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.forestGreen;
        }
        return Colors.grey;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.forestGreen.withValues(alpha: 0.5);
        }
        return Colors.grey.withValues(alpha: 0.3);
      }),
    ),
  );

  // Dark theme data
  ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.dark(
      primary: AppColors.forestGreenLight,
      onPrimary: AppColors.darkPrimary,
      secondary: AppColors.forestGreenLight,
      onSecondary: AppColors.darkPrimary,
      surface: AppColors.darkCard,
      onSurface: AppColors.darkTextPrimary,
      background: AppColors.darkSecondary,
      onBackground: AppColors.darkTextPrimary,
      error: AppColors.errorDark,
      onError: AppColors.darkPrimary,
      outline: AppColors.darkBorder,
      outlineVariant: AppColors.darkBorder,
      surfaceVariant: AppColors.darkTertiary,
      onSurfaceVariant: AppColors.darkTextSecondary,
    ),
    primaryColor: AppColors.forestGreenLight,
    scaffoldBackgroundColor: AppColors.darkSecondary,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.darkTertiary,
      foregroundColor: AppColors.darkTextPrimary,
      elevation: 0,
      centerTitle: false,
      surfaceTintColor: Colors.transparent,
    ),
    cardTheme: CardThemeData(
      elevation: AppDimensions.elevationLow,
      shape: RoundedRectangleBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
      ),
      color: AppColors.darkCard,
      surfaceTintColor: Colors.transparent,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.forestGreenLight,
        foregroundColor: AppColors.darkPrimary,
        disabledBackgroundColor: AppColors.darkBorder,
        disabledForegroundColor: AppColors.darkTextTertiary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
        elevation: 2,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.darkBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.darkBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.forestGreenLight, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.errorDark),
      ),
      fillColor: AppColors.darkTertiary,
      filled: true,
      hintStyle: const TextStyle(color: AppColors.darkTextTertiary),
      labelStyle: const TextStyle(color: AppColors.darkTextSecondary),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.forestGreenLight,
        side: const BorderSide(color: AppColors.forestGreenLight),
        disabledForegroundColor: AppColors.darkTextTertiary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.forestGreenLight,
        disabledForegroundColor: AppColors.darkTextTertiary,
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.forestGreen;
        }
        return Colors.grey;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.forestGreen.withValues(alpha: 0.5);
        }
        return Colors.grey.withValues(alpha: 0.3);
      }),
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.bold),
      displayMedium: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.bold),
      displaySmall: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.bold),
      headlineLarge: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w600),
      headlineMedium: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w600),
      headlineSmall: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w600),
      titleLarge: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w500),
      titleMedium: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(color: AppColors.darkTextPrimary),
      bodyMedium: TextStyle(color: AppColors.darkTextPrimary),
      bodySmall: TextStyle(color: AppColors.darkTextSecondary),
      labelLarge: TextStyle(color: AppColors.darkTextPrimary, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(color: AppColors.darkTextSecondary),
      labelSmall: TextStyle(color: AppColors.darkTextTertiary),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.darkSecondary,
      selectedItemColor: AppColors.forestGreenLight,
      unselectedItemColor: AppColors.darkTextTertiary,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    dividerTheme: const DividerThemeData(
      color: AppColors.darkBorder,
      thickness: 1,
    ),
    dialogTheme: DialogThemeData(
      backgroundColor: AppColors.darkCard,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),
    snackBarTheme: const SnackBarThemeData(
      backgroundColor: AppColors.darkTertiary,
      contentTextStyle: TextStyle(color: AppColors.darkTextPrimary),
      actionTextColor: AppColors.forestGreenLight,
    ),
  );

  /// Initialize theme from saved preferences
  Future<void> initializeTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      if (savedTheme != null) {
        _themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.light,
        );
      }
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing theme: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// Toggle between light and dark theme
  Future<void> toggleTheme() async {
    _themeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    
    await _saveTheme();
    notifyListeners();
  }

  /// Set specific theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _saveTheme();
      notifyListeners();
    }
  }

  /// Save theme preference to storage
  Future<void> _saveTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, _themeMode.toString());
    } catch (e) {
      debugPrint('Error saving theme: $e');
    }
  }

  /// Get theme-aware colors
  Color getBackgroundColor(BuildContext context) {
    return isDarkMode ? AppColors.darkSecondary : AppColors.appBackground;
  }

  Color getCardColor(BuildContext context) {
    return isDarkMode ? AppColors.darkCard : Colors.white;
  }

  Color getTextColor(BuildContext context) {
    return isDarkMode ? AppColors.darkTextPrimary : Colors.black87;
  }

  Color getSecondaryTextColor(BuildContext context) {
    return isDarkMode ? AppColors.darkTextSecondary : Colors.grey.shade600;
  }

  Color getTertiaryTextColor(BuildContext context) {
    return isDarkMode ? AppColors.darkTextTertiary : Colors.grey.shade500;
  }

  Color getBorderColor(BuildContext context) {
    return isDarkMode ? AppColors.darkBorder : Colors.grey.shade300;
  }

  Color getSurfaceColor(BuildContext context) {
    return isDarkMode ? AppColors.darkTertiary : AppColors.softIvory;
  }

  Color getElevatedSurfaceColor(BuildContext context) {
    return isDarkMode ? AppColors.darkSurface : Colors.white;
  }

  // Calendar-specific theme-aware colors
  Color getCalendarBackgroundColor(BuildContext context) {
    return isDarkMode ? AppColors.darkSecondary : Colors.white;
  }

  Color getCalendarTodayColor(BuildContext context) {
    return isDarkMode ? AppColors.calendarTodayDark : AppColors.calendarTodayLight;
  }

  Color getCalendarSelectedColor(BuildContext context) {
    return isDarkMode ? AppColors.calendarSelectedDark : AppColors.calendarSelectedLight;
  }

  Color getCalendarCellColor(BuildContext context, {bool isCurrentMonth = true, bool isToday = false, bool isWorkingDay = true}) {
    if (isToday) {
      return getCalendarTodayColor(context);
    }

    if (!isCurrentMonth) {
      return isDarkMode ? AppColors.darkTertiary.withValues(alpha: 0.3) : Colors.grey.shade50;
    }

    if (!isWorkingDay) {
      return isDarkMode ? AppColors.darkBorder.withValues(alpha: 0.5) : Colors.grey.shade200;
    }

    return isDarkMode ? AppColors.darkCard : Colors.white;
  }

  Color getCalendarTextColor(BuildContext context, {bool isCurrentMonth = true, bool isToday = false, bool isWorkingDay = true}) {
    if (isToday) {
      return isDarkMode ? AppColors.darkTextPrimary : AppColors.forestGreen;
    }

    if (!isCurrentMonth) {
      return isDarkMode ? AppColors.darkTextTertiary : Colors.grey.shade400;
    }

    if (!isWorkingDay) {
      return isDarkMode ? AppColors.darkTextTertiary : Colors.grey.shade500;
    }

    return isDarkMode ? AppColors.darkTextPrimary : Colors.black87;
  }

  Color getCalendarBorderColor(BuildContext context, {bool isToday = false}) {
    if (isToday) {
      return isDarkMode ? AppColors.forestGreenLight : AppColors.forestGreen;
    }
    return isDarkMode ? AppColors.darkBorder : Colors.grey.shade300;
  }
}
