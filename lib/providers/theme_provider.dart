import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/theme/app_theme.dart';

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  
  ThemeMode _themeMode = ThemeMode.light;
  bool _isInitialized = false;

  ThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isInitialized => _isInitialized;

  // Light theme data
  ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.forestGreen,
      brightness: Brightness.light,
    ),
    primaryColor: AppColors.forestGreen,
    scaffoldBackgroundColor: AppColors.appBackground,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.forestGreen,
      
      elevation: 0,
      centerTitle: false,
    ),
    cardTheme: CardThemeData(
      elevation: AppDimensions.elevationLow,
      shape: RoundedRectangleBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
      ),
      color: Colors.white,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.forestGreen,
        
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.forestGreen, width: 2),
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.forestGreen;
        }
        return Colors.grey;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.forestGreen.withValues(alpha: 0.5);
        }
        return Colors.grey.withValues(alpha: 0.3);
      }),
    ),
  );

  // Clean Dark Theme - Material 3 based
  ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.dark(
      // Primary colors
      primary: AppColors.darkAccent,
      onPrimary: Colors.white,
      primaryContainer: AppColors.darkAccentVariant,
      onPrimaryContainer: Colors.white,

      // Secondary colors
      secondary: AppColors.darkAccent,
      onSecondary: Colors.white,
      secondaryContainer: AppColors.darkSurfaceVariant,
      onSecondaryContainer: AppColors.darkText,

      // Surface colors
      surface: AppColors.darkSurface,
      onSurface: AppColors.darkText,
      surfaceVariant: AppColors.darkSurfaceVariant,
      onSurfaceVariant: AppColors.darkTextSecondary,

      // Background colors
      background: AppColors.darkBackground,
      onBackground: AppColors.darkText,

      // Error colors
      error: AppColors.darkError,
      onError: Colors.white,

      // Outline colors
      outline: AppColors.darkBorder,
      outlineVariant: AppColors.darkBorder,
    ),
    scaffoldBackgroundColor: AppColors.darkBackground,
    // App Bar Theme
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.darkSurface,
      foregroundColor: AppColors.darkText,
      elevation: 0,
      centerTitle: false,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: AppColors.darkText,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    // Card Theme
    cardTheme: CardThemeData(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: AppColors.darkSurface,
      surfaceTintColor: Colors.transparent,
    ),

    // Button Themes - Let Material 3 handle most of it
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      ),
    ),
    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.darkSurfaceVariant,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: AppColors.darkBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: AppColors.darkBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: AppColors.darkAccent, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: AppColors.darkError),
      ),
      hintStyle: TextStyle(color: AppColors.darkTextTertiary),
      labelStyle: TextStyle(color: AppColors.darkTextSecondary),
    ),

    // Navigation and Bottom Bar
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: AppColors.darkSurface,
      selectedItemColor: AppColors.darkAccent,
      unselectedItemColor: AppColors.darkTextSecondary,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    // Additional Theme Components
    dividerTheme: DividerThemeData(
      color: AppColors.darkBorder,
      thickness: 1,
    ),

    dialogTheme: DialogThemeData(
      backgroundColor: AppColors.darkSurface,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),

    snackBarTheme: SnackBarThemeData(
      backgroundColor: AppColors.darkSurfaceVariant,
      contentTextStyle: TextStyle(color: AppColors.darkText),
      actionTextColor: AppColors.darkAccent,
    ),
  );

  /// Initialize theme from saved preferences
  Future<void> initializeTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      if (savedTheme != null) {
        _themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.light,
        );
      }
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing theme: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// Toggle between light and dark theme
  Future<void> toggleTheme() async {
    _themeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    
    await _saveTheme();
    notifyListeners();
  }

  /// Set specific theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _saveTheme();
      notifyListeners();
    }
  }

  /// Save theme preference to storage
  Future<void> _saveTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, _themeMode.toString());
    } catch (e) {
      debugPrint('Error saving theme: $e');
    }
  }

  /// Simple theme-aware color helpers
  /// Use Theme.of(context).colorScheme for most cases

  // Only keep essential helpers that aren't covered by Material 3
  Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).colorScheme.background;
  }

  Color getSurfaceColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  Color getPrimaryColor(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  Color getTextColor(BuildContext context) {
    return Theme.of(context).colorScheme.onBackground;
  }

  // Additional helper methods for compatibility
  Color getBorderColor(BuildContext context) {
    return Theme.of(context).colorScheme.outline;
  }

  Color getSecondaryTextColor(BuildContext context) {
    return Theme.of(context).colorScheme.onSurfaceVariant;
  }

  Color getTertiaryTextColor(BuildContext context) {
    return Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7);
  }

  // Calendar-specific helpers
  Color getCalendarBackgroundColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  Color getCalendarCellColor(BuildContext context, {bool isCurrentMonth = true, bool isToday = false, bool isWorkingDay = true}) {
    if (isToday) {
      return Theme.of(context).colorScheme.primaryContainer;
    }

    if (!isCurrentMonth) {
      return Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3);
    }

    if (!isWorkingDay) {
      return Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.5);
    }

    return Theme.of(context).colorScheme.surface;
  }

  Color getCalendarTextColor(BuildContext context, {bool isCurrentMonth = true, bool isToday = false, bool isWorkingDay = true}) {
    if (isToday) {
      return Theme.of(context).colorScheme.onPrimaryContainer;
    }

    if (!isCurrentMonth) {
      return Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5);
    }

    if (!isWorkingDay) {
      return Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7);
    }

    return Theme.of(context).colorScheme.onSurface;
  }

  Color getCalendarBorderColor(BuildContext context, {bool isToday = false}) {
    if (isToday) {
      return Theme.of(context).colorScheme.primary;
    }
    return Theme.of(context).colorScheme.outline;
  }
}
