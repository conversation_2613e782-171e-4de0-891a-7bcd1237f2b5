import 'package:flutter/material.dart';

// Export all theme-related classes for easy access
export 'app_dimensions.dart';
export '../../core/constants/app_strings.dart';
export '../../widgets/common/standard_form_field.dart';

class AppColors {
  // Light theme colors
  static const cream = Color(0xFFFEE6C9);       // Panel background (light end of gradient)
  static const peach = Color(0xFFF5C399);       // Panel mid-gradient; form/card bg hover
  static const warmOrange = Color(0xFFEC9C68);  // Panel dark gradient end; underline accents
  static const softIvory = Color(0xFFFFF8EE);   // Cards & input backgrounds
  static const forestGreen = Color(0xFF2E7D32); // Updated to match specification - primary accent
  static const logoBrown = Color(0xFFA85D39);   // Dog illustration, subtle text accents
  static const taupe = Color(0xFFC38A6F);       // Form-field borders & placeholder text
  static const white = Color(0xFFFFFFFF);       // Body background, cards, typography
  static const appBackground = Color(0xFFFFEED3); // App background color

  // Modern Dark Theme Colors (Discord/GitHub inspired)
  static const darkBackground = Color(0xFF1E1E1E);     // Main background - warm dark gray
  static const darkSurface = Color(0xFF2D2D30);        // Cards, elevated surfaces - slightly lighter
  static const darkSurfaceVariant = Color(0xFF3E3E42); // Secondary surfaces - even lighter
  static const darkBorder = Color(0xFF484848);         // Borders and dividers - subtle

  // Dark theme text colors - high contrast for readability
  static const darkText = Color(0xFFE1E1E1);           // Primary text - soft white
  static const darkTextSecondary = Color(0xFFB3B3B3);  // Secondary text - medium gray
  static const darkTextTertiary = Color(0xFF8A8A8A);   // Tertiary text - light gray

  // Accent colors - keeping the forest green theme
  static const darkAccent = Color(0xFF4CAF50);         // Primary accent - forest green
  static const darkAccentVariant = Color(0xFF66BB6A);  // Lighter variant for hover states

  // Status colors for dark theme
  static const darkSuccess = Color(0xFF4CAF50);        // Success green
  static const darkWarning = Color(0xFFFF9800);        // Warning orange
  static const darkError = Color(0xFFF44336);          // Error red
  static const darkInfo = Color(0xFF2196F3);           // Info blue

  // Compatibility aliases for existing code
  static const forestGreenLight = darkAccent;          // Alias for compatibility
  static const darkTertiary = darkSurfaceVariant;      // Alias for compatibility
  static const darkTextPrimary = darkText;             // Alias for compatibility
  static const darkCard = darkSurface;                 // Alias for compatibility
  static const warningDark = darkWarning;              // Alias for compatibility
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.forestGreen,
      scaffoldBackgroundColor: AppColors.white,
      colorScheme: ColorScheme.light(
        primary: AppColors.forestGreen,
        secondary: AppColors.logoBrown,
        surface: AppColors.softIvory,
        background: AppColors.white,
        error: Colors.red.shade700,
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.w600,
        ),
        bodyLarge: TextStyle(color: Colors.black87),
        bodyMedium: TextStyle(color: Colors.black87),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.softIvory,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.taupe),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.taupe),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.logoBrown, width: 2),
        ),
        labelStyle: TextStyle(color: AppColors.taupe),
        hintStyle: TextStyle(color: AppColors.taupe),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.forestGreen,
        foregroundColor: AppColors.white,
        elevation: 0,
      ),
    );
  }
}