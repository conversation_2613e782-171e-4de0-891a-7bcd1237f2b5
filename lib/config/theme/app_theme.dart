import 'package:flutter/material.dart';

// Export all theme-related classes for easy access
export 'app_dimensions.dart';
export '../../core/constants/app_strings.dart';
export '../../widgets/common/standard_form_field.dart';

class AppColors {
  // Light theme colors
  static const cream = Color(0xFFFEE6C9);       // Panel background (light end of gradient)
  static const peach = Color(0xFFF5C399);       // Panel mid-gradient; form/card bg hover
  static const warmOrange = Color(0xFFEC9C68);  // Panel dark gradient end; underline accents
  static const softIvory = Color(0xFFFFF8EE);   // Cards & input backgrounds
  static const forestGreen = Color(0xFF2E7D32); // Updated to match specification - primary accent
  static const logoBrown = Color(0xFFA85D39);   // Dog illustration, subtle text accents
  static const taupe = Color(0xFFC38A6F);       // Form-field borders & placeholder text
  static const white = Color(0xFFFFFFFF);       // Body background, cards, typography
  static const appBackground = Color(0xFFFFEED3); // App background color

  // Dark theme colors - Apple Calendar inspired
  static const darkPrimary = Color(0xFF000000);      // Rich black primary background
  static const darkSecondary = Color(0xFF1C1C1E);    // Apple-style primary background
  static const darkTertiary = Color(0xFF2C2C2E);     // Apple-style secondary background
  static const darkQuaternary = Color(0xFF3A3A3C);   // Apple-style tertiary background
  static const darkCard = Color(0xFF2C2C2E);         // Card backgrounds in dark mode
  static const darkSurface = Color(0xFF3A3A3C);      // Elevated surfaces
  static const darkBorder = Color(0xFF48484A);       // Borders and dividers

  // Dark theme text colors
  static const darkTextPrimary = Color(0xFFFFFFFF);    // Primary text
  static const darkTextSecondary = Color(0xFFE5E5E7);  // Secondary text
  static const darkTextTertiary = Color(0xFF8E8E93);   // Tertiary text/placeholders

  // Accent colors that work in both themes
  static const forestGreenLight = Color(0xFF4CAF50);   // Lighter variant for dark backgrounds
  static const forestGreenDark = Color(0xFF2E7D32);    // Original for light backgrounds

  // Status colors for both themes
  static const successLight = Color(0xFF4CAF50);
  static const successDark = Color(0xFF66BB6A);
  static const warningLight = Color(0xFFFF9800);
  static const warningDark = Color(0xFFFFB74D);
  static const errorLight = Color(0xFFF44336);
  static const errorDark = Color(0xFFEF5350);

  // Calendar specific colors
  static const calendarTodayLight = Color(0xFFE8F5E8);
  static const calendarTodayDark = Color(0xFF2C4A2C);
  static const calendarSelectedLight = Color(0xFFE3F2FD);
  static const calendarSelectedDark = Color(0xFF3A4A5C);
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.forestGreen,
      scaffoldBackgroundColor: AppColors.white,
      colorScheme: ColorScheme.light(
        primary: AppColors.forestGreen,
        secondary: AppColors.logoBrown,
        surface: AppColors.softIvory,
        background: AppColors.white,
        error: Colors.red.shade700,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(
          color: AppColors.forestGreen,
          fontWeight: FontWeight.w600,
        ),
        bodyLarge: TextStyle(color: Colors.black87),
        bodyMedium: TextStyle(color: Colors.black87),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.softIvory,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.taupe),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.taupe),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.logoBrown, width: 2),
        ),
        labelStyle: const TextStyle(color: AppColors.taupe),
        hintStyle: const TextStyle(color: AppColors.taupe),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.forestGreen,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.logoBrown,
          side: const BorderSide(color: AppColors.logoBrown),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.logoBrown,
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.forestGreen,
        foregroundColor: AppColors.white,
        elevation: 0,
      ),
    );
  }
}