class Breeds {
  static const List<String> dogBreeds = [
    'Labrador Retriever',
    'Golden Retriever',
    '<PERSON><PERSON><PERSON>\u0103nesc German',
    'Bulldog Englez',
    'Bulldog Francez',
    '<PERSON><PERSON>',
    'Pitbull Terrier',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    // todo de adauagt toate rasele
    'Chihuahua',
    'Yorkshire Terrier',
    'Caniche Toy',
    'Caniche Mini',
    'Caniche Mediu',
    'Caniche Standard',
    'Caniche Royal',
    'Bichon <PERSON>\u00e9',
    '<PERSON>ich<PERSON> Maltez',
    '<PERSON><PERSON>zu',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Husky Siberian',
    'Border Collie',
    'Pomeranian',
    'Australian Shepherd',
    'Cocker Spaniel',
    'Jack Russell Terrier',
    'A<PERSON>ta Inu',
    'Basset Hound',
    'Ciob\u0103nesc de Berna',
    'Bloodhound',
    'Bull Terrier',
    '<PERSON>e Corso',
    'Cavalier King <PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>\u021bian',
    '<PERSON><PERSON>',
    'Dog German',
    'English Cocker Spaniel',
    'Fox Terrier',
    '<PERSON><PERSON> Irlandez',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    'Malamut de Alaska',
    'Mastiff Tibetan',
    '<PERSON> Nova',
    'Pechinez',
    'Pinscher Pitic',
    'Pointer German',
    'Samoyed',
    'Schnauzer Mini',
    'Schnauzer Standard',
    'Schnauzer Gigant',
    'Shar Pei',
    'Ciob\u0103nesc Shetland',
    'Shiba Inu',
    'Springer Spaniel Englez',
    'Staffordshire Bull Terrier',
    'Vizsla Unguresc',
    'Weimaraner',
    'West Highland White Terrier',
    'Whippet',
    'Buldog American',
    'Collie',
    'Ciob\u0103nesc Belgian',
    'Ciob\u0103nesc Rom\u00e2nesc Mioritic',
    'Ciob\u0103nesc Carpatin',
    'Dogo Argentino',
    'Saluki',
    'Greyhound',
    'Cavapoo',
    'Cockapoo',
    'Labradoodle',
    'Goldendoodle',
    'Maltipoo',
    'Bernedoodle',
    'Sheepadoodle',
    'Pitsky',
    'Pitbull Terrier American',
    'Alaskan Klee Kai',
    'Basenji',
    'Brittany',
    'Havanese',
    'Lagotto Romagnolo',
    'Pumi',
    'Xoloitzcuintli',
    'Bouvier de Flandra',
    'Keeshond',
    'Lhasa Apso',
    'Manchester Terrier',
    'Otterhound',
    'Papillon',
    'Griffon de Vend\u00e9e',
    'Pharaoh Hound',
    'Pudelpointer',
    'Puli',
    'Ciob\u0103nesc de Pirinei',
    'Rat Terrier',
    'Redbone Coonhound',
    'Thai Ridgeback',
    'Spaniel Tibetan',
    'Terrier Tibetan',
    'Utonagan',
    'Boerboel',
    'Metis'
  ];

  static const List<String> catBreeds = [
    'Persan\u0103',
    'Maine Coon',
    'Siamez\u0103',
    'British Shorthair',
    'Ragdoll',
    'Bengalez\u0103',
    'Abisinian\u0103',
    'Birman\u0103',
    'Oriental Shorthair',
    'Devon Rex',
    'Sphynx',
    'Scottish Fold',
    'Russian Blue',
    'Norvegian de P\u0103dure',
    'Exotic Shorthair',
    'Cornish Rex',
    'Selkirk Rex',
    'Munchkin',
    'American Shorthair',
    'Angora Turceasc\u0103',
    'Birmanez\u0103',
    'Himalayan\u0103',
    'Tonkinese',
    'Somalez\u0103',
    'Balinez\u0103',
    'Egyptian Mau',
    'Manx',
    'Burmilla',
    'Chartreux',
    'Siberian\u0103',
    'Ocicat',
    'Metis'
  ];

  static const List<String> otherBreeds = [
    'Metis',
    'Necunoscut'
  ];

  static List<String> forSpecies(String species) {
    switch (species) {
      case 'dog':
        return dogBreeds;
      case 'cat':
        return catBreeds;
      default:
        return otherBreeds;
    }
  }

  static List<String> get all => [
        ...dogBreeds,
        ...catBreeds,
        ...otherBreeds,
      ];
}
