import 'package:flutter/foundation.dart';

// Conditional import for platform-specific functionality
import 'dart:io' if (dart.library.html) 'dart:html' as platform;

class NetworkUtils {
  // Get the local IP address of the computer
  static Future<String?> getLocalIpAddress() async {
    if (kIsWeb) {
      // On web, we can't get the local IP address due to security restrictions
      // Return localhost as fallback
      debugPrint('Local IP address not available on web platform');
      return 'localhost';
    }

    try {
      // Get all network interfaces (only available on non-web platforms)
      final interfaces = await platform.NetworkInterface.list();

      for (final interface in interfaces) {
        // Skip loopback interfaces
        if (interface.name.toLowerCase().contains('lo')) continue;

        for (final address in interface.addresses) {
          // Look for IPv4 addresses that are not loopback
          if (address.type == platform.InternetAddressType.IPv4 &&
              !address.isLoopback &&
              !address.address.startsWith('169.254')) { // Skip link-local addresses
            return address.address;
          }
        }
      }
    } catch (e) {
      debugPrint('Error getting local IP address: $e');
    }

    return null;
  }

  // Get all available IP addresses
  static Future<List<String>> getAllIpAddresses() async {
    final List<String> addresses = [];

    try {
      final interfaces = await NetworkInterface.list();

      for (final interface in interfaces) {
        for (final address in interface.addresses) {
          if (address.type == InternetAddressType.IPv4 && !address.isLoopback) {
            addresses.add('${address.address} (${interface.name})');
          }
        }
      }
    } catch (e) {
      debugPrint('Error getting IP addresses: $e');
    }

    return addresses;
  }

  // Test if a server is reachable
  static Future<bool> testServerConnection(String host, int port) async {
    try {
      final socket = await Socket.connect(host, port, timeout: const Duration(seconds: 5));
      await socket.close();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get network interface information
  static Future<Map<String, dynamic>> getNetworkInfo() async {
    final info = <String, dynamic>{};

    try {
      final interfaces = await NetworkInterface.list();
      final interfaceInfo = <String, List<String>>{};

      for (final interface in interfaces) {
        final addresses = <String>[];
        for (final address in interface.addresses) {
          if (address.type == InternetAddressType.IPv4) {
            addresses.add(address.address);
          }
        }
        if (addresses.isNotEmpty) {
          interfaceInfo[interface.name] = addresses;
        }
      }

      info['interfaces'] = interfaceInfo;
      info['primaryIp'] = await getLocalIpAddress();
      info['allAddresses'] = await getAllIpAddresses();
    } catch (e) {
      info['error'] = e.toString();
    }

    return info;
  }

  // Print network configuration for debugging
  static Future<void> printNetworkConfig() async {
    debugPrint('\n🌐 Network Configuration:');
    debugPrint('=' * 50);

    final primaryIp = await getLocalIpAddress();
    if (primaryIp != null) {
      debugPrint('📱 Primary IP Address: $primaryIp');
      debugPrint('🔗 Mobile Server URL: http://$primaryIp:8080');
    } else {
      debugPrint('❌ Could not determine primary IP address');
    }

    debugPrint('\n📋 All Network Interfaces:');
    final addresses = await getAllIpAddresses();
    for (final address in addresses) {
      debugPrint('   • $address');
    }

    debugPrint('\n💡 Instructions for Mobile Testing:');
    debugPrint('1. Update lib/config/api_config.dart');
    debugPrint('2. Change serverHost to: \'$primaryIp\'');
    debugPrint('3. Ensure your mobile device is on the same WiFi network');
    debugPrint('4. Start the mock server with: dart lib/main_server.dart');
    debugPrint('5. Run the Flutter app on your mobile device');
    debugPrint('=' * 50);
  }
}
