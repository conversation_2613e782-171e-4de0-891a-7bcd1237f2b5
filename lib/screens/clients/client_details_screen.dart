import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../models/pet.dart';
import '../../models/review.dart';
import '../../models/subscription.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/client_provider.dart';
import '../../services/client/client_service.dart';
import '../../config/theme/app_theme.dart';
import '../../widgets/clients/client_header_widget.dart';
import '../../widgets/common/custom_bottom_sheet.dart';
import '../../widgets/common/custom_sliver_app_bar.dart';
import '../../widgets/lists/client_tabs_widget.dart';
import 'add_pet_screen.dart';
import 'edit_client_screen.dart';

class ClientDetailsScreen extends StatefulWidget {
  final Client client;

  const ClientDetailsScreen({
    super.key,
    required this.client,
  });

  @override
  State<ClientDetailsScreen> createState() => _ClientDetailsScreenState();
}

class _ClientDetailsScreenState extends State<ClientDetailsScreen> {
  Client? _currentClient;
  final List<Subscription> _subscriptions = [];
  final List<Review> _reviews = [];
  final List<Appointment> _appointments = [];
  List<Pet> _pets = [];

  bool _isLoadingPets = true;
  bool _isLoadingAppointments = true;

  @override
  void initState() {
    super.initState();
    _currentClient = widget.client;

    // Use post frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadClientData();
      _refreshClientFromProvider();
    });
  }

  /// Refresh client data from ClientProvider to get latest information
  void _refreshClientFromProvider() {
    debugPrint('🔄 ClientDetailsScreen: Refreshing client data from provider...');
    final clientProvider = context.read<ClientProvider>();
    final updatedClient = clientProvider.getClientById(widget.client.id);

    if (updatedClient != null && mounted) {
      setState(() {
        _currentClient = updatedClient;
      });
      debugPrint('✅ ClientDetailsScreen: Client data refreshed from provider');
    } else {
      debugPrint('⚠️ ClientDetailsScreen: Client not found in provider, using original data');
    }
  }

  Future<void> _loadClientData() async {
    await Future.wait([
      _loadPets(),
      _loadAppointments(),
    ]);
  }

  Future<void> _loadPets() async {
    try {
      debugPrint('🔄 ClientDetailsScreen: Loading pets for client: ${_currentClient?.id}');
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      // Get pets for this client using HTTP service
      final pets = await calendarProvider.calendarService.getPetsForClient(_currentClient?.id ?? widget.client.id);

      if (mounted) {
        setState(() {
          _pets = pets;
          _isLoadingPets = false;
        });
        debugPrint('✅ ClientDetailsScreen: Loaded ${pets.length} pets');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingPets = false;
        });
      }
      debugPrint('❌ ClientDetailsScreen: Error loading client pets: $e');
    }
  }

  Future<void> _loadAppointments() async {
    try {
      debugPrint('🔄 ClientDetailsScreen: Loading appointments for client: ${_currentClient?.id}');
      final response = await ClientService.getClientAppointments(_currentClient?.id ?? widget.client.id);

      if (mounted) {
        setState(() {
          if (response.success && response.data != null) {
            _appointments
              ..clear()
              ..addAll(response.data!);
          } else {
            _appointments.clear();
          }
          _isLoadingAppointments = false;
        });
        debugPrint('✅ ClientDetailsScreen: Loaded ${_appointments.length} appointments');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAppointments = false;
        });
      }
      debugPrint('❌ ClientDetailsScreen: Error loading client appointments: $e');
    }
  }

  void _showOptionsMenu() {
    CustomBottomSheet.show(
      context: context,
      title: 'Opțiuni Client',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading:  Icon(Icons.delete_forever, color: Colors.red),
            title: Text('Șterge Client'),
            subtitle: Text('Șterge definitiv clientul și toate datele asociate'),
            onTap: () {
              Navigator.pop(context);
              _showDeleteConfirmation();
            },
          ),
          ListTile(
            leading:  Icon(Icons.block, color: Colors.orange),
            title: Text('Blochează Client'),
            subtitle: Text('Împiedică programarea de noi întâlniri'),
            onTap: () {
              Navigator.pop(context);
              _blockClient();
            },
          ),
          ListTile(
            leading:  Icon(Icons.favorite, color: Colors.pink),
            title: Text('Adaugă la Favorite'),
            subtitle: Text('Marchează ca client preferat'),
            onTap: () {
              Navigator.pop(context);
              _favoriteClient();
            },
          ),
        ],
      ),
    );
  }

  void _blockClient() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.client.name} a fost blocat'),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Anulează',
          textColor: Colors.white,
          onPressed: () {
            // Undo block action
          },
        ),
      ),
    );
  }

  void _favoriteClient() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.client.name} a fost adăugat la favorite'),
        backgroundColor: Colors.pink,
        action: SnackBarAction(
          label: 'Anulează',
          textColor: Colors.white,
          onPressed: () {
            // Undo favorite action
          },
        ),
      ),
    );
  }

  void _editClient() async {
    debugPrint('🔄 ClientDetailsScreen: Opening edit screen for client: ${_currentClient?.name}');

    final result = await Navigator.of(context).push<Client>(
      MaterialPageRoute(
        builder: (context) => EditClientScreen(client: _currentClient ?? widget.client),
      ),
    );

    // If client was successfully updated, refresh the data from provider
    if (result != null && mounted) {
      debugPrint('✅ ClientDetailsScreen: Client ${result.name} updated successfully');

      // Refresh client provider to get latest data
      final clientProvider = context.read<ClientProvider>();
      await clientProvider.refresh();

      // Update local client data
      _refreshClientFromProvider();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Clientul ${result.name} a fost actualizat cu succes!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Confirmare Ștergere'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Ești sigur că vrei să ștergi clientul "${_currentClient?.name ?? widget.client.name}"?'),
              SizedBox(height: 16),
              Text(
                'Această acțiune va șterge:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Toate datele clientului'),
              Text('• Toate animalele asociate'),
              Text('• Istoricul programărilor'),
              Text('• Recenziile și abonamentele'),
              SizedBox(height: 16),
              Text(
                'Această acțiune nu poate fi anulată!',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteClient();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                
              ),
              child: Text('Șterge'),
            ),
          ],
        );
      },
    );
  }

  /// Delete the client
  Future<void> _deleteClient() async {
    final clientId = _currentClient?.id ?? widget.client.id;
    final clientName = _currentClient?.name ?? widget.client.name;

    debugPrint('🗑️ ClientDetailsScreen: Starting client deletion process...');
    debugPrint('📍 ClientDetailsScreen: Client ID: $clientId');
    debugPrint('📍 ClientDetailsScreen: Client Name: $clientName');

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Se șterge clientul...'),
            ],
          ),
        );
      },
    );

    try {
      // Delete client using ClientProvider
      final clientProvider = context.read<ClientProvider>();
      final success = await clientProvider.deleteClient(clientId);

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success && mounted) {
        debugPrint('✅ ClientDetailsScreen: Client deleted successfully');

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Clientul "$clientName" a fost șters cu succes!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );

        // Navigate back to clients list
        Navigator.of(context).pop();
      } else if (mounted) {
        debugPrint('❌ ClientDetailsScreen: Failed to delete client');

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la ștergerea clientului "$clientName"'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ ClientDetailsScreen: Exception during client deletion: $e');

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la ștergerea clientului: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddPetDialog() async {
    final result = await Navigator.of(context).push<Pet>(
      MaterialPageRoute(
        builder: (context) => AddPetScreen(
          clientId: widget.client.id,
          clientName: widget.client.name,
        ),
      ),
    );

    // If a pet was successfully added, refresh the pets list
    if (result != null) {
      _loadPets();
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayClient = _currentClient ?? widget.client;

    return DetailScreenScaffold(
      headerContent: ClientHeaderWidget(client: displayClient),
      actions: [], // Removed edit button from top
      body: Column(
        children: [
          // Tabs section
          Expanded(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                child: ClientTabsWidget(
                  client: displayClient,
                  appointments: _appointments,
                  subscriptions: _subscriptions,
                  reviews: _reviews,
                  pets: _pets,
                  isLoading: _isLoadingPets || _isLoadingAppointments,
                  onAddPet: _showAddPetDialog,
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: SpeedDial(
        animatedIcon: AnimatedIcons.menu_close,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        activeBackgroundColor: Theme.of(context).colorScheme.secondary,
        spacing: 3,
        childPadding: const EdgeInsets.all(5),
        spaceBetweenChildren: 4,
        tooltip: 'Acțiuni',
        children: [
          SpeedDialChild(
            child:  Icon(Icons.delete_forever),
            backgroundColor: Theme.of(context).colorScheme.error,
            foregroundColor: Theme.of(context).colorScheme.onError,
            label: 'Șterge Client',
            onTap: _showDeleteConfirmation,
          ),
          SpeedDialChild(
            child:  Icon(Icons.pets),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: 'Adaugă Animal',
            onTap: _showAddPetDialog,
          ),
          SpeedDialChild(
            child:  Icon(Icons.edit),
            backgroundColor: AppColors.white,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: 'Editează Client',
            onTap: _editClient,
          ),
          SpeedDialChild(
            child:  Icon(Icons.more_vert),
            backgroundColor: AppColors.white,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: 'Opțiuni Client',
            onTap: _showOptionsMenu,
          ),
        ],
      ),
    );
  }
}
