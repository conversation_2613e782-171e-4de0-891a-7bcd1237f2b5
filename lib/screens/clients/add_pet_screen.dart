import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../models/pet.dart';
import '../../services/client/pet_service.dart';
import '../../services/api_service.dart';
import '../../models/image_upload_response.dart';
import '../../config/theme/app_theme.dart';
import '../../widgets/common/custom_bottom_sheet.dart';
import '../../utils/breeds.dart';

class AddPetScreen extends StatefulWidget {
  final String clientId;
  final String clientName;

  const AddPetScreen({
    super.key,
    required this.clientId,
    required this.clientName,
  });

  @override
  State<AddPetScreen> createState() => _AddPetScreenState();
}

class _AddPetScreenState extends State<AddPetScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _breedController = TextEditingController();
  final _colorController = TextEditingController();
  final _weightController = TextEditingController();
  final _microchipController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedSpecies = 'dog';
  String _selectedGender = 'male';
  DateTime _birthDate = DateTime.now().subtract(const Duration(days: 365));
  bool _isLoading = false;
  String? _errorMessage;
  File? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();

  final List<Map<String, dynamic>> _species = [
    {'value': 'dog', 'label': 'Câine', 'icon': '🐕'},
    {'value': 'cat', 'label': 'Pisică', 'icon': '🐱'},
    {'value': 'other', 'label': 'Altul', 'icon': '🐾'},
  ];

  final List<Map<String, dynamic>> _genders = [
    {'value': 'male', 'label': 'Mascul', 'icon': '♂️'},
    {'value': 'female', 'label': 'Femelă', 'icon': '♀️'},
  ];


  @override
  void dispose() {
    _nameController.dispose();
    _breedController.dispose();
    _colorController.dispose();
    _weightController.dispose();
    _microchipController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  List<String> get _currentBreedSuggestions {
    return Breeds.forSpecies(_selectedSpecies);
  }


  Future<void> _pickImage() async {
    CustomBottomSheet.show(
      context: context,
      title: 'Selectează sursa imaginii',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.camera_alt, color: AppColors.forestGreen),
            title: const Text('Camera'),
            onTap: () {
              Navigator.pop(context);
              _getImageFromSource(ImageSource.camera);
            },
          ),
          ListTile(
            leading: const Icon(Icons.photo_library, color: AppColors.forestGreen),
            title: const Text('Galerie'),
            onTap: () {
              Navigator.pop(context);
              _getImageFromSource(ImageSource.gallery);
            },
          ),
          if (_selectedImage != null)
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Șterge imaginea'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedImage = null;
                });
              },
            ),
        ],
      ),
    );
  }

  Future<void> _getImageFromSource(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la selectarea imaginii: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _birthDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.forestGreen,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _birthDate) {
      setState(() {
        _birthDate = picked;
      });
    }
  }

  Future<void> _savePet() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Handle image upload if selected
      String photoUrl = '';
      if (_selectedImage != null) {
        final uploadResponse = await ApiService.uploadFile<ImageUploadResponse>(
          '/api/images/upload',
          _selectedImage!.path,
          fromJson: (data) =>
              ImageUploadResponse.fromJson(Map<String, dynamic>.from(data)),
        );
        if (uploadResponse.success && uploadResponse.data != null) {
          photoUrl = uploadResponse.data!.url;
        }
      }

      final pet = Pet(
        id: '', // Will be generated by server
        name: _nameController.text.trim(),
        species: _selectedSpecies,
        breed: _breedController.text.trim().isEmpty ? 'Metis' : _breedController.text.trim(),
        gender: _selectedGender,
        birthDate: _birthDate,
        weight: double.tryParse(_weightController.text.trim()) ?? 0.1,
        color: _colorController.text.trim().isEmpty ? 'Necunoscut' : _colorController.text.trim(),
        ownerId: widget.clientId,
        microchipNumber: _microchipController.text.trim(),
        notes: _notesController.text.trim(),
        photoUrl: photoUrl,
      );

      final response = await PetService.addPetToClient(widget.clientId, pet);

      if (mounted) {
        if (response.success && response.data != null) {
          // Success - return the created pet
          Navigator.of(context).pop(response.data);
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text('${pet.name} a fost adăugat cu succes!')),
                ],
              ),
              backgroundColor: AppColors.forestGreen,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          );
        } else {
          // Handle error
          setState(() {
            _errorMessage = response.message ?? 'Eroare la salvarea animalului';
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Eroare de conexiune: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Numele este obligatoriu';
    }
    if (value.trim().length < 2) {
      return 'Numele trebuie să aibă cel puțin 2 caractere';
    }
    return null;
  }


  Widget _buildPhotoSection() {
    return Center(
      child: GestureDetector(
        onTap: _pickImage,
        child: Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(60),
            border: Border.all(
              color: AppColors.forestGreen.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: _selectedImage != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(58),
                  child: Image.file(
                    _selectedImage!,
                    fit: BoxFit.cover,
                  ),
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_a_photo,
                      size: 40,
                      color: AppColors.forestGreen.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Adaugă fotografie',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.forestGreen.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.pets,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Informații de bază',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Name field
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nume animal *',
                hintText: 'ex: Rex, Mimi, Charlie',
                prefixIcon: Icon(Icons.pets),
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
              validator: _validateName,
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // Species selection
            const Text(
              'Specie *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.forestGreen,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: _species.map((species) {
                final isSelected = _selectedSpecies == species['value'];
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedSpecies = species['value'];
                          // Clear breed when species changes
                          _breedController.clear();
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected ? AppColors.forestGreen : Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSelected ? AppColors.forestGreen : Colors.grey[300]!,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              species['icon'],
                              style: const TextStyle(fontSize: 24),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              species['label'],
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.black87,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Breed autocomplete field
            _buildBreedAutocomplete(),
            const SizedBox(height: 16),

            // Gender selection
            const Text(
              'Gen *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.forestGreen,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: _genders.map((gender) {
                final isSelected = _selectedGender == gender['value'];
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedGender = gender['value'];
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected ? AppColors.forestGreen : Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSelected ? AppColors.forestGreen : Colors.grey[300]!,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              gender['icon'],
                              style: const TextStyle(fontSize: 24),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              gender['label'],
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.black87,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Adaugă animal pentru ${widget.clientName}'),
        backgroundColor: AppColors.forestGreen,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              ),
            )
          else
            TextButton(
              onPressed: _savePet,
              child: const Text(
                'Salvează',
                style: TextStyle(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Container(
        color: AppColors.appBackground,
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Error message
              if (_errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red.shade600),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red.shade600),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Photo section
              _buildPhotoSection(),
              const SizedBox(height: 24),

              // Basic info card
              _buildBasicInfoCard(),
              const SizedBox(height: 16),

              // Physical characteristics card
              _buildPhysicalCharacteristicsCard(),
              const SizedBox(height: 16),

              // Additional info card
              _buildAdditionalInfoCard(),
              const SizedBox(height: 24),

              // Save button
              _buildSaveButton(),
              const SizedBox(height: 16),

              // Info text
              _buildInfoText(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBreedAutocomplete() {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return _currentBreedSuggestions;
        }
        return _currentBreedSuggestions.where((String option) {
          return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
        });
      },
      onSelected: (String selection) {
        _breedController.text = selection;
      },
      fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
        // Sync the autocomplete controller with our breed controller
        if (controller.text != _breedController.text) {
          controller.text = _breedController.text;
        }

        return TextFormField(
          controller: controller,
          focusNode: focusNode,
          onEditingComplete: onEditingComplete,
          onChanged: (value) {
            _breedController.text = value;
          },
          decoration: const InputDecoration(
            labelText: 'Rasă',
            hintText: 'Începeți să tastați pentru sugestii...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.words,
          enabled: !_isLoading,
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final String option = options.elementAt(index);
                  return InkWell(
                    onTap: () => onSelected(option),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: Text(
                        option,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPhysicalCharacteristicsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.monitor_weight,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Caracteristici fizice',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Birth date
            GestureDetector(
              onTap: _selectBirthDate,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[400]!),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.cake, color: AppColors.forestGreen),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Data nașterii',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            '${_birthDate.day}/${_birthDate.month}/${_birthDate.year}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                    const Icon(Icons.calendar_today, color: Colors.grey),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Weight and Color in a row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    decoration: const InputDecoration(
                      labelText: 'Greutate (kg) *',
                      hintText: 'ex: 25.5',
                      prefixIcon: Icon(Icons.monitor_weight),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    enabled: !_isLoading,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _colorController,
                    decoration: const InputDecoration(
                      labelText: 'Culoare',
                      hintText: 'ex: Maro, Negru',
                      prefixIcon: Icon(Icons.palette),
                      border: OutlineInputBorder(),
                    ),
                    textCapitalization: TextCapitalization.words,
                    enabled: !_isLoading,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Informații suplimentare',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Microchip field
            TextFormField(
              controller: _microchipController,
              decoration: const InputDecoration(
                labelText: 'Număr microchip',
                hintText: 'ex: 123456789012345',
                prefixIcon: Icon(Icons.memory),
                border: OutlineInputBorder(),
              ),
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // Notes field
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Observații',
                hintText: 'Informații suplimentare despre animal',
                prefixIcon: Icon(Icons.note_outlined),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _savePet,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : const Icon(Icons.pets),
        label: Text(_isLoading ? 'Se salvează...' : 'Adaugă animalul'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.forestGreen,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoText() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.forestGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppColors.forestGreen,
            size: 20,
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              'Câmpurile marcate cu * sunt obligatorii. Fotografiile și informațiile suplimentare pot fi adăugate mai târziu.',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.forestGreen,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
