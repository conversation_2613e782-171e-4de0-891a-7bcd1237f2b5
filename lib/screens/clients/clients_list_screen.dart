import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/client.dart';
import '../../providers/client_provider.dart';
import '../../config/theme/app_theme.dart';
import 'add_client_screen.dart';
import 'client_details_screen.dart';

class ClientsListScreen extends StatefulWidget {
  const ClientsListScreen({super.key});

  @override
  State<ClientsListScreen> createState() => _ClientsListScreenState();
}

class _ClientsListScreenState extends State<ClientsListScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);

    // Initialize or refresh the client provider to ensure current salon data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final clientProvider = context.read<ClientProvider>();
      if (!clientProvider.isInitialized) {
        debugPrint('🔄 ClientsListScreen: Initializing ClientProvider...');
        clientProvider.initialize();
      } else {
        debugPrint('🔄 ClientsListScreen: Refreshing ClientProvider for current salon...');
        clientProvider.refresh();
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final clientProvider = context.read<ClientProvider>();
    clientProvider.searchClients(_searchController.text);
  }

  void _navigateToClientDetails(Client client) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ClientDetailsScreen(client: client),
      ),
    );
  }

  void _addNewClient() async {
    final result = await Navigator.of(context).push<Client>(
      MaterialPageRoute(
        builder: (context) => const AddClientScreen(),
      ),
    );

    // If a client was successfully created, refresh the provider
    if (result != null && mounted) {
      final clientProvider = context.read<ClientProvider>();
      await clientProvider.refresh();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Clientul a fost adăugat cu succes!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            action: SnackBarAction(
              label: 'Vezi detalii',
              textColor: AppColors.white,
              onPressed: () => _navigateToClientDetails(result),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClientProvider>(
      builder: (context, clientProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text('Clienți'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: AppColors.white,
            elevation: 0,
            actions: [
              IconButton(
                icon:  Icon(Icons.person_add),
                onPressed: _addNewClient,
                tooltip: 'Adaugă client nou',
              ),
            ],
          ),
          body: Container(
            color: AppColors.appBackground,
            child: Column(
              children: [
                // Search field
                Container(
                  color: Theme.of(context).colorScheme.primary,
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'Caută client',
                      hintText: 'Nume, telefon sau email...',
                      prefixIcon:  Icon(Icons.search, color: Theme.of(context).colorScheme.primary),
                      suffixIcon: clientProvider.searchQuery.isNotEmpty
                          ? IconButton(
                              icon:  Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                              },
                            )
                          : null,
                      filled: true,
                      fillColor: AppColors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: AppColors.logoBrown, width: 2),
                      ),
                    ),
                  ),
                ),

                // Add Client button and Results header
                if (!clientProvider.isLoading)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                    child: Column(
                      children: [
                        // Prominent Add Client button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _addNewClient,
                            icon:  Icon(Icons.person_add),
                            label: Text('Adaugă client nou'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: AppColors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        // Results header (only show if there are clients)
                        if (clientProvider.filteredClients.isNotEmpty) ...[
                          SizedBox(height: 16),
                          Row(
                            children: [
                              Icon(
                                Icons.people,
                                color: Theme.of(context).colorScheme.primary,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Clienți găsiți (${clientProvider.filteredClients.length})',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),

                // Clients list
                Expanded(
                  child: _buildClientsList(clientProvider),
                ),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton(
            heroTag: "clients_add_fab",
            onPressed: _addNewClient,
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: AppColors.white,
            child:  Icon(Icons.person_add),
          ),
        );
      },
    );
  }

  Widget _buildClientsList(ClientProvider clientProvider) {
    if (clientProvider.isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).colorScheme.primary,
        ),
      );
    }

    if (clientProvider.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            SizedBox(height: 16),
            Text(
              clientProvider.error ?? 'A apărut o eroare',
              style: TextStyle(
                fontSize: 16,
                color: Colors.red[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => clientProvider.refresh(),
              icon:  Icon(Icons.refresh),
              label: Text('Încearcă din nou'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: AppColors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (clientProvider.filteredClients.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              clientProvider.searchQuery.isEmpty ? Icons.people_outline : Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16),
            Text(
              clientProvider.searchQuery.isEmpty
                  ? 'Nu există clienți înregistrați'
                  : 'Nu s-au găsit clienți pentru "${clientProvider.searchQuery}"',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (clientProvider.searchQuery.isEmpty) ...[
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: _addNewClient,
                child:  Icon(Icons.person_add),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: clientProvider.filteredClients.length,
      itemBuilder: (context, index) {
        final client = clientProvider.filteredClients[index];
        return _buildClientCard(client);
      },
    );
  }

  Widget _buildClientCard(Client client) {
    return Card(
      key: ValueKey(client.id),
      margin: const EdgeInsets.only(bottom: 8.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToClientDetails(client),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Profile avatar
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Center(
                  child: Text(
                    client.name.isNotEmpty ? client.name[0].toUpperCase() : '?',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              SizedBox(width: 16),

              // Client info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      client.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.phone,
                          size: 16,
                          color: AppColors.taupe,
                        ),
                        SizedBox(width: 4),
                        Text(
                          client.phone,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.taupe,
                          ),
                        ),
                      ],
                    ),
                    if (client.email.isNotEmpty) ...[
                      SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(
                            Icons.email,
                            size: 16,
                            color: AppColors.taupe,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              client.email,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.taupe,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (client.petCount > 0) ...[
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.pets,
                            size: 16,
                            color: AppColors.logoBrown,
                          ),
                          SizedBox(width: 4),
                          Text(
                            '${client.petCount} ${client.petCount == 1 ? 'animal' : 'animale'}',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.logoBrown,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Arrow indicator
              Icon(
                Icons.arrow_forward_ios,
                color: AppColors.taupe,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
