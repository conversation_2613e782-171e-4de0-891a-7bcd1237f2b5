import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../services/analytics_insights_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/feature_toggle_service.dart';
import '../../services/screen_time_service.dart';
import '../../widgets/calendar_views/google_calendar_view.dart';
import '../../widgets/dialogs/appointment_details_dialog.dart';
import '../../widgets/dialogs/block_time_dialog.dart';
import '../../widgets/drawers/calendar_settings_drawer.dart';
import 'new_appointment_screen.dart';

class CalendarScreen extends StatefulWidget {
  const CalendarScreen({super.key});

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  // Make fields final and use setState to update them
  final String _screenName = 'calendar_screen';
  CalendarViewMode _currentViewMode = CalendarViewMode.week;
  bool _monthlyViewEnabled = false;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();

    // Add screen time tracking
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScreenTimeService.startScreenTime(_screenName);
    });

    // Track screen initialization
    AnalyticsInsightsService.trackFeatureDiscovery(
      featureName: 'calendar',
      discoveryMethod: 'navigation',
      discoveryData: {'view_mode': _currentViewMode.toString()},
    );

    _loadFeatureToggles();

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final calendarProvider =
          Provider.of<CalendarProvider>(context, listen: false);
      debugPrint('📅 CalendarScreen: Initializing calendar data...');
      _initializeCalendar(calendarProvider);
    });
  }

  @override
  void dispose() {
    ScreenTimeService.endScreenTime(_screenName);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Refresh calendar data when screen becomes active (e.g., after salon switch)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final calendarProvider =
          Provider.of<CalendarProvider>(context, listen: false);
      debugPrint(
          '📅 CalendarScreen: Dependencies changed, refreshing calendar data...');
      _loadCalendarData(calendarProvider);
    });
  }

  /// Initialize calendar with preferences and data
  Future<void> _initializeCalendar(CalendarProvider calendarProvider) async {
    debugPrint('📅 CalendarScreen: Starting calendar initialization...');

    // Initialize preferences first
    await calendarProvider.initializePreferences();

    // Load calendar data
    await _loadCalendarData(calendarProvider);

    debugPrint('📅 Calendar initialization completed');
  }

  /// Load calendar data with retry mechanism for new salons
  Future<void> _loadCalendarData(CalendarProvider calendarProvider) async {
    debugPrint('📅 Loading calendar data...');

    // Check if user has salon association before loading data
    final currentSalonId = await AuthService.getCurrentSalonId();
    if (currentSalonId == null) {
      debugPrint('📅 No salon association - skipping calendar data loading');
      return;
    }

    // Load staff with retry mechanism (important for new salons)
    await _loadStaffWithRetry(calendarProvider);

    // Load other data
    await calendarProvider.loadServices();
    await calendarProvider.loadWorkingHours();

    // Load staff working hours for selected staff (optimized approach)
    if (calendarProvider.selectedStaff.isNotEmpty) {
      await calendarProvider.loadStaffWorkingHoursOnDemand(
        calendarProvider.selectedStaff,
        reason: 'Initial calendar load'
      );
    }

    // Load appointments and block times for today
    final today = DateTime.now();
    debugPrint('📅 Loading appointments and block times for today: ${today.toString().split(' ')[0]}');
    await calendarProvider.fetchAppointmentsForDate(today, forceRefresh: true);
    await calendarProvider.fetchBlockedTimesForDate(today, forceRefresh: true);

    debugPrint('📅 Calendar data loading completed');
  }

  /// Load staff with retry mechanism for new salon owners
  Future<void> _loadStaffWithRetry(CalendarProvider calendarProvider) async {
    const maxAttempts = 2;
    const delayBetweenAttempts = Duration(milliseconds: 500);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      debugPrint('📅 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        debugPrint('📅 Staff loaded successfully on attempt $attempt');
        return;
      }

      if (attempt < maxAttempts) {
        debugPrint('📅 No staff found, retrying...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    debugPrint(
        '📅 Staff loading completed (${calendarProvider.availableStaff.length} staff members)');
  }

  Future<void> _loadFeatureToggles() async {
    final monthlyViewEnabled =
        await FeatureToggleService.isMonthlyViewEnabled();
    if (mounted) {
      setState(() {
        _monthlyViewEnabled = monthlyViewEnabled;
      });
    }
  }




  /// Refresh staff data and working hours
  void _refreshStaffData() async {
    debugPrint('🔄 CalendarScreen: Manual staff data and working hours refresh requested');

    final calendarProvider =
        Provider.of<CalendarProvider>(context, listen: false);

    // Refresh staff data (names, roles, etc.)
    await calendarProvider.refreshStaffData();

    // Refresh staff working hours using new batch API
    await calendarProvider.refreshStaffWorkingHours(reason: 'Manual refresh button - using batch API');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Datele echipei și programul de lucru au fost actualizate (batch API)'),
          backgroundColor: AppColors.forestGreen,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }


  void _showBlockTimeScreen() {
    final selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BlockTimeDialog(
          selectedDate: selectedDate,
        );
      },
    );
  }

  void _openAppointmentDetails(Appointment appointment) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AppointmentDetailsDialog(appointment: appointment);
      },
    );
  }

  void _showNewAppointmentScreen(
      [DateTime? selectedDate, String? groomerId]) async {
    final date = selectedDate ?? DateTime.now();

    // Track appointment creation flow start
    AnalyticsInsightsService.trackAppointmentCreationFlow(
      'started',
      stepData: {
        'selected_date': date.toIso8601String(),
        'preselected_groomer': groomerId,
        'initiated_from': 'calendar_screen',
      },
    );

    final newAppointment = await Navigator.of(context).push<Appointment?>(
      MaterialPageRoute(
        builder: (context) => NewAppointmentScreen(
          selectedDate: DateTime(date.year, date.month, date.day), // Date only
          selectedDateTime: selectedDate, // Full datetime with time component
          preselectedStaffId: groomerId,
        ),
      ),
    );

    if (mounted && newAppointment != null) {
      // Track appointment creation result
      if (true) {
        debugPrint('✅ Appointment added successfully');
        AnalyticsInsightsService.trackAppointmentCreationFlow(
          'completed',
          stepData: {'success': true, 'completion_source': 'calendar_screen'},
        );

        setState(() {
          _selectedDate = DateTime(
            newAppointment.startTime.year,
            newAppointment.startTime.month,
            newAppointment.startTime.day,
          );
        });

        final calendarProvider =
            Provider.of<CalendarProvider>(context, listen: false);
        calendarProvider.fetchAppointmentsForDate(_selectedDate, forceRefresh: true);
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _openAppointmentDetails(newAppointment);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      endDrawer: CalendarSettingsDrawer(
        currentViewMode: _currentViewMode,
        monthlyViewEnabled: _monthlyViewEnabled,
        onViewModeChanged: (mode) {
          setState(() {
            _currentViewMode = mode;
          });
        },
      ),
      // Professional AppBar with Apple Calendar styling
      appBar: AppBar(
        backgroundColor: AppColors.forestGreen,
        elevation: 0,
        toolbarHeight: 0, // Hide the default toolbar, use custom header
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        top: false, // AppBar handles status bar spacing
        child: SizedBox.expand(
          child: Container(
            color: AppColors.appBackground,
            child: GoogleCalendarView(
              currentViewMode: _currentViewMode,
              selectedDate: _selectedDate,
              onTimeSlotTap: (dateTime, staffId) =>
                  _showNewAppointmentScreen(dateTime, staffId),
              onViewModeChange: (mode) {
                setState(() {
                  _currentViewMode = mode;
                });
              },
              onDateChanged: (date) {
                setState(() {
                  _selectedDate = date;
                });
              },
              onRefreshPressed: _refreshStaffData,
              onSettingsPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
            ),
          ),
        ),
      ),
      floatingActionButton: Consumer<CalendarProvider>(
        builder: (context, provider, child) {
          final appointmentCount = _getAppointmentCountForCurrentView(provider);

          return Badge(
            isLabelVisible: appointmentCount > 0,
            label: Text(
              appointmentCount.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.red,
            child: SpeedDial(
              animatedIcon: AnimatedIcons.menu_close,
              backgroundColor: AppColors.forestGreen,
              foregroundColor: AppColors.white,
              activeBackgroundColor: AppColors.logoBrown,
              spacing: 3,
              childPadding: const EdgeInsets.all(5),
              spaceBetweenChildren: 4,
              children: [
                SpeedDialChild(
                  child: const Icon(Icons.block),
                  backgroundColor: AppColors.white,
                  foregroundColor: AppColors.forestGreen,
                  label: 'Blochează timp',
                  onTap: _showBlockTimeScreen,
                ),
                SpeedDialChild(
                  child: const Icon(Icons.add),
                  backgroundColor: AppColors.white,
                  foregroundColor: AppColors.forestGreen,
                  label: 'Programare nouă',
                  onTap: () => _showNewAppointmentScreen(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  int _getAppointmentCountForCurrentView(CalendarProvider provider) {
    switch (_currentViewMode) {
      case CalendarViewMode.day:
        return provider.getFilteredAppointmentsForDate(_selectedDate).length;
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        int totalCount = 0;
        for (int i = 0; i < 7; i++) {
          final day = weekStart.add(Duration(days: i));
          totalCount += provider.getFilteredAppointmentsForDate(day).length;
        }
        return totalCount;
      case CalendarViewMode.month:
        final firstDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
        final lastDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
        int totalCount = 0;
        DateTime currentDay = firstDayOfMonth;
        while (currentDay.isBefore(lastDayOfMonth) || currentDay.isAtSameMomentAs(lastDayOfMonth)) {
          totalCount += provider.getFilteredAppointmentsForDate(currentDay).length;
          currentDay = currentDay.add(const Duration(days: 1));
        }
        return totalCount;
    }
  }

  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

}
