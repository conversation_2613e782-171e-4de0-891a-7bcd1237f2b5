
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';


import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';
import '../../services/appointment/calendar_service.dart';
import '../../services/error_message_service.dart';
import '../../models/appointment_alternative.dart';
import '../../widgets/dialogs/conflict_resolution_dialog.dart';
import '../../widgets/new_appointment/appointment_footer.dart';
import '../../widgets/new_appointment/appointment_form_constants.dart';
import '../../widgets/new_appointment/appointment_form_data.dart';
import '../../widgets/new_appointment/appointment_form_logic.dart';
import '../../widgets/new_appointment/client_selection_widget.dart';
import '../../widgets/new_appointment/coworker_selection_widget.dart';
import '../../widgets/new_appointment/multiple_services_widget.dart';
import '../../widgets/new_appointment/notes_repetition_widget.dart';
import '../../widgets/new_appointment/pet_selection_widget.dart';

class NewAppointmentScreen extends StatefulWidget {
  final DateTime selectedDate;
  final String? preselectedStaffId;
  final DateTime? selectedDateTime; // New parameter for specific time selection

  const NewAppointmentScreen({
    super.key,
    required this.selectedDate,
    this.preselectedStaffId,
    this.selectedDateTime, // Optional specific time from calendar
  });

  @override
  State<NewAppointmentScreen> createState() => _NewAppointmentScreenState();
}

class _NewAppointmentScreenState extends State<NewAppointmentScreen> {
  late AppointmentFormData _formData;
  late AppointmentFormLogic _formLogic;
  bool _isLoading = false;
  Future<AppointmentCreationResult>? _addAppointmentFuture;
  bool _conflictDialogShown = false;

  @override
  void initState() {
    super.initState();
    _initializeFormData();
    _initializeFormLogic();
    _loadInitialData();
  }

  void _initializeFormData() {
    final initialStartTime = _calculateInitialStartTime();
    // Calculate initial end time based on default service duration
    final defaultServiceDuration = 0;
    final initialEndTime = initialStartTime.add(Duration(minutes: defaultServiceDuration));

    _formData = AppointmentFormData(
      appointmentDate: widget.selectedDate,
      startTime: initialStartTime,
      endTime: initialEndTime,
    );
  }

  void _initializeFormLogic() {
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    _formLogic = AppointmentFormLogic(calendarProvider);
  }

  DateTime _calculateInitialStartTime() {
    // Use selectedDateTime if provided (from calendar time slot click), otherwise use current time
    final baseDateTime = widget.selectedDateTime ?? DateTime.now();

    final selectedDateTime = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
      widget.selectedDate.day,
      baseDateTime.hour,
      baseDateTime.minute,
    );

    // Round up to the nearest 15-minute interval
    final minutes = selectedDateTime.minute;
    final roundedMinutes = ((minutes / 15).ceil() * 15) % 60;
    final additionalHour = ((minutes / 15).ceil() * 15) ~/ 60;

    var startTime = DateTime(
      selectedDateTime.year,
      selectedDateTime.month,
      selectedDateTime.day,
      selectedDateTime.hour + additionalHour,
      roundedMinutes,
    );

    // Ensure start time is within business hours
    if (startTime.hour < AppointmentFormConstants.businessStartHour) {
      startTime = DateTime(startTime.year, startTime.month, startTime.day,
          AppointmentFormConstants.businessStartHour, 0);
    } else if (startTime.hour >= AppointmentFormConstants.businessEndHour) {
      if (widget.selectedDate.isAfter(DateTime.now())) {
        startTime = DateTime(widget.selectedDate.year, widget.selectedDate.month,
            widget.selectedDate.day, AppointmentFormConstants.businessStartHour, 0);
      } else {
        startTime = DateTime(startTime.year, startTime.month, startTime.day + 1,
            AppointmentFormConstants.businessStartHour, 0);
      }
    }

    return startTime;
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() => _isLoading = true);
      try {
        await _formLogic.loadInitialData(_formData, preselectedStaffId: widget.preselectedStaffId);
        // Sync end time with default service duration after services are loaded
        await _formLogic.updateEndTimeBasedOnServices(_formData);
      } finally {
        if (mounted) setState(() => _isLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_addAppointmentFuture != null) {
      return _buildAppointmentCreationScreen();
    }

    return _buildFormScreen();
  }

  Widget _buildAppointmentCreationScreen() {
    return Scaffold(
      appBar: AppBar(
        title: Text('Programare nouă'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: AppColors.white,
        automaticallyImplyLeading: false,
      ),
      body: Container(
        color: AppColors.appBackground,
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: FutureBuilder<AppointmentCreationResult>(
              future: _addAppointmentFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return _buildLoadingContent();
                } else if (snapshot.hasError) {
                  return _buildErrorContent(snapshot.error.toString());
                } else if (snapshot.hasData) {
                  final result = snapshot.data!;
                  if (result.success) {
                    // Only navigate away on success
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        Navigator.of(context).pop(result.appointment);
                      }
                    });
                  }
                  return _buildResultContent(result);
                } else {
                  return _buildErrorContent('Nu s-a putut crea programarea');
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(color: AppColors.forestGreen),
        const SizedBox(height: 20),
        const Text(
          'Creăm programarea...',
          style: TextStyle(fontSize: 18),
        ),
      ],
    );
  }

  Widget _buildErrorContent(String error) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
         Icon(Icons.error, color: Colors.red, size: 64),
        SizedBox(height: 16),
        Text(
          'Eroare: $error',
          style: TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 24),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text('Închide'),
        ),
      ],
    );
  }

  Widget _buildResultContent(AppointmentCreationResult result) {
    if (result.success) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
           Icon(Icons.check_circle, color: Colors.green, size: 64),
          SizedBox(height: 16),
          Text(
            'Programarea a fost adăugată cu succes!',
            style: TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(result.appointment),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text('Închide'),
          ),
        ],
      );
    } else {
      // Show specific error message for business logic errors
      final errorMessage = result.userFriendlyError;
      final suggestion = ErrorMessageService.getSuggestedAction(result.errorCode);

      debugPrint('🔍 Checking conflict dialog conditions:');
      debugPrint('  - isSchedulingConflict: ${result.isSchedulingConflict}');
      debugPrint('  - conflictDialogShown: $_conflictDialogShown');
      debugPrint('  - alternatives count: ${result.alternatives.length}');
      debugPrint('  - errorCode: ${result.errorCode}');

      if (result.isSchedulingConflict && !_conflictDialogShown) {
        debugPrint('✅ Showing conflict resolution dialog');
        _conflictDialogShown = true;
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final alt = await showDialog<AppointmentAlternative>(
            context: context,
            builder: (ctx) => ConflictResolutionDialog(
              alternatives: result.alternatives,
              onTryDifferentTime: () {
                Navigator.of(ctx).pop();
                setState(() {
                  _addAppointmentFuture = null;
                  _conflictDialogShown = false;
                });
              },
              onContactSalon: () {
                Navigator.of(ctx).pop();
              },
              onAddToWaitingList: () {
                Navigator.of(ctx).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Adăugat la lista de așteptare')),
                );
                setState(() {
                  _addAppointmentFuture = null;
                  _conflictDialogShown = false;
                });
              },
              onSelectAlternative: (alternative) {
                Navigator.of(ctx).pop(alternative);
              },
            ),
          );
          if (alt != null) {
            debugPrint('🔄 Alternative selected: ${alt.startTime} - ${alt.endTime} (Staff: ${alt.staffName})');
            setState(() {
              _formData.startTime = alt.startTime;
              _formData.endTime = alt.endTime;
              _formData.assignedStaffId = alt.staffId;
              // CRITICAL FIX: Update appointmentDate to match the alternative date
              _formData.appointmentDate = DateTime(
                alt.startTime.year,
                alt.startTime.month,
                alt.startTime.day,
              );
              debugPrint('✅ Form data updated - Date: ${_formData.appointmentDate}, Time: ${_formData.startTime} - ${_formData.endTime}');
              _addAppointmentFuture = _formLogic.createAppointment(_formData);
              _conflictDialogShown = false;
            });
          }
        });
      }

      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            result.isSchedulingConflict ? Icons.schedule : Icons.error,
            color: result.isSchedulingConflict ? Colors.orange : Colors.red,
            size: 64,
          ),
          SizedBox(height: 16),
          Text(
            errorMessage,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          if (suggestion != null) ...[
            SizedBox(height: 12),
            Text(
              suggestion,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Go back to form to try again
              setState(() {
                _addAppointmentFuture = null;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text('OK'),
          ),
        ],
      );
    }
  }

  Widget _buildFormScreen() {
    return Scaffold(
      appBar: AppBar(
        title: Text('Programare nouă'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: AppColors.white,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDateHeader(),
                  SizedBox(height: 24),
                  _buildClientSection(),
                  SizedBox(height: 24),
                  _buildPetSection(),
                  SizedBox(height: 24),
                  _buildServicesSection(),
                  SizedBox(height: 24),
                  _buildTimeSection(),
                  SizedBox(height: 24),
                  _buildCoworkerSection(),
                  SizedBox(height: 24),
                  _buildNotesRepetitionSection(),
                  SizedBox(height: 100), // Space for footer
                ],
              ),
            ),
          ),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildDateHeader() {
    return Card(
      child: InkWell(
        onTap: () => _showDatePicker(),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(Icons.calendar_today, color: Theme.of(context).colorScheme.primary),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Data programării',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      DateFormat('dd MMMM yyyy', 'ro').format(_formData.appointmentDate),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.edit, color: Theme.of(context).colorScheme.primary, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  void _showDatePicker() async {
    // Normalize dates to compare only date parts (without time)
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    final appointmentDate = DateTime(
      _formData.appointmentDate.year,
      _formData.appointmentDate.month,
      _formData.appointmentDate.day,
    );

    // Use today as initial date if appointment date is in the past
    final initialDate = appointmentDate.isBefore(todayDate) ? todayDate : appointmentDate;

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: todayDate,
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null && selectedDate != _formData.appointmentDate) {
      setState(() {
        _formLogic.handleAppointmentDateChange(_formData, selectedDate);
      });
    }
  }

  Widget _buildClientSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ClientSelectionWidget(
          formData: _formData,
          isLoadingClients: _isLoading,
          onClientTypeChanged: (isExisting) {
            setState(() {
              _formLogic.handleClientTypeChange(_formData, isExisting);
            });
          },
          onClientSelected: (client) async {
            debugPrint('🔄 NewAppointmentScreen: Client selected: ${client.name} (${client.id})');

            // Update UI immediately to show client selection
            setState(() {
              _formLogic.handleClientTypeChange(_formData, true);
              _formData.updateClientData(client);
            });

            // Load pets asynchronously and update UI when complete
            try {
              await _formLogic.loadPetsForClient(_formData, client.id);
              debugPrint('✅ NewAppointmentScreen: Pets loaded, updating UI');
              setState(() {
                // Trigger UI rebuild with loaded pets
              });
            } catch (e) {
              debugPrint('❌ NewAppointmentScreen: Error loading pets: $e');
              setState(() {
                // Update UI even if pet loading failed
              });
            }
          },
          onClientNameChanged: (name) {
            setState(() => _formData.clientName = name);
          },
          onClientPhoneChanged: (phone) {
            setState(() => _formData.clientPhone = phone);
          },
        ),
      ),
    );
  }

  Widget _buildPetSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: PetSelectionWidget(
          formData: _formData,
          onPetSelected: (pet) {
            setState(() {
              _formLogic.handlePetSelection(_formData, pet);
            });
          },
          onPetNameChanged: (name) {
            setState(() => _formData.petName = name);
          },
          onPetBreedChanged: (breed) {
            setState(() {
              _formLogic.handlePetBreedChange(_formData, breed);
            });
          },
          onAddNewPet: () {
            setState(() {
              _formLogic.handleToggleNewPet(_formData);
            });
          },
          onPriceUpdateNeeded: () {
            // Force UI update to recalculate prices based on new breed/size
            setState(() {});
            debugPrint('💰 Price update triggered by breed change - new size: ${_formData.petSize}');
          },
        ),
      ),
    );
  }

  Widget _buildServicesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: MultipleServicesWidget(
          formData: _formData,
          onAddService: (service) async {
            await _formLogic.handleAddService(_formData, service);
            setState(() {
              // Trigger UI update after async operation completes
            });
          },
          onRemoveService: (service) async {
            await _formLogic.handleRemoveService(_formData, service);
            setState(() {
              // Trigger UI update after async operation completes
            });
          },
        ),
      ),
    );
  }

  Widget _buildTimeSection() {
    // Generate time options for both start and end time
    final startTimeOptions = AppointmentFormConstants.generateTimeOptions(_formData.appointmentDate);
    final endTimeOptions = AppointmentFormConstants.generateTimeOptions(_formData.appointmentDate);

    // Always ensure current end time is included in options
    if (!endTimeOptions.contains(_formData.endTime)) {
      endTimeOptions.add(_formData.endTime);
      endTimeOptions.sort();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Programare timp',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                // Show duration information
                Consumer<CalendarProvider>(
                  builder: (context, provider, child) {
                    final serviceDetails = provider.getServiceDetails();
                    final serviceDuration = _formData.getTotalDurationFromDetails(serviceDetails);
                    final actualDuration = _formData.getActualDuration();

                    // Check if user has manually adjusted the time
                    final isManuallyAdjusted = serviceDuration != actualDuration;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (isManuallyAdjusted) ...[
                          // Show both when manually adjusted
                          Text(
                            'Servicii: ${serviceDuration}min',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            'Programare: ${actualDuration}min',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ] else ...[
                          // Show single duration when they match
                          Text(
                            'Durata: ${actualDuration}min',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ],
                    );
                  },
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<DateTime>(
                    value: startTimeOptions.contains(_formData.startTime) ? _formData.startTime : null,
                    decoration: InputDecoration(
                      labelText: 'Ora început',
                      border: OutlineInputBorder(),
                    ),
                    isExpanded: true,
                    items: startTimeOptions.map((time) {
                      return DropdownMenuItem<DateTime>(
                        value: time,
                        child: Text(AppointmentFormConstants.formatTime(time)),
                      );
                    }).toList(),
                    onChanged: (value) async {
                      if (value != null) {
                        await _formLogic.handleStartTimeChange(_formData, value);
                        setState(() {
                          // Trigger UI update after async operation completes
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<DateTime>(
                    value: endTimeOptions.contains(_formData.endTime) ? _formData.endTime : null,
                    decoration: InputDecoration(
                      labelText: 'Ora sfârșit',
                      border: OutlineInputBorder(),
                    ),
                    isExpanded: true,
                    items: endTimeOptions.map((time) {
                      return DropdownMenuItem<DateTime>(
                        value: time,
                        child: Text(AppointmentFormConstants.formatTime(time)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _formData.endTime = value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoworkerSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CoworkerSelectionWidget(
          formData: _formData,
          onStaffChanged: (staffId) {
            setState(() {
              _formLogic.handleStaffChange(_formData, staffId);
            });
          },
        ),
      ),
    );
  }

  Widget _buildNotesRepetitionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: NotesRepetitionWidget(
          formData: _formData,
          onNotesVisibilityChanged: (showNotes) {
            setState(() {
              _formLogic.handleNotesVisibilityChange(_formData, showNotes);
            });
          },
          onRepetitionVisibilityChanged: (showRepetition) {
            setState(() {
              _formLogic.handleRepetitionVisibilityChange(_formData, showRepetition);
            });
          },
          onRepetitionFrequencyChanged: (frequency) {
            setState(() {
              _formLogic.handleRepetitionFrequencyChange(_formData, frequency);
            });
          },
          onNotesChanged: (notes) {
            setState(() => _formData.notes = notes);
          },
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return AppointmentFooter(
      formData: _formData,
      onCreateAppointment: _handleCreateAppointment,
      onPaidStatusChanged: (isPaid) {
        setState(() => _formData.isPaid = isPaid);
      },
    );
  }

  void _handleCreateAppointment() async {
    final validationError = _formLogic.validateForm(_formData);
    if (validationError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(validationError)),
      );
      return;
    }

    setState(() {
      _conflictDialogShown = false;
      _addAppointmentFuture = _formLogic.createAppointment(_formData);
    });
  }
}
