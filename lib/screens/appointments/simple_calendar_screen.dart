import 'package:flutter/material.dart';
import '../../models/appointment.dart';
import '../../services/appointment/appointment_service.dart';
import '../../config/theme/app_theme.dart';
import '../../utils/date_formatter.dart';

class SimpleCalendarScreen extends StatefulWidget {
  const SimpleCalendarScreen({Key? key}) : super(key: key);

  @override
  State<SimpleCalendarScreen> createState() => _SimpleCalendarScreenState();
}

enum CalendarViewMode { day, week, month }

class _SimpleCalendarScreenState extends State<SimpleCalendarScreen> {
  List<Appointment> _appointments = [];
  bool _isLoading = true;
  CalendarViewMode _viewMode = CalendarViewMode.day;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    try {
      final response = await AppointmentService.getAppointments();
      
      if (mounted) {
        setState(() {
          if (response.success && response.data != null) {
            _appointments = response.data!;
          } else {
            _appointments = [];
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      debugPrint('Error loading appointments: $e');
    }
  }

  List<Appointment> get _todayAppointments {
    final today = DateTime.now();
    return _appointments.where((appointment) {
      return appointment.startTime.year == today.year &&
             appointment.startTime.month == today.month &&
             appointment.startTime.day == today.day;
    }).toList();
  }

  List<Appointment> get _upcomingAppointments {
    final now = DateTime.now();
    return _appointments.where((appointment) {
      return appointment.startTime.isAfter(now);
    }).toList()..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppColors.forestGreen),
            )
          : RefreshIndicator(
              onRefresh: _loadAppointments,
              color: AppColors.forestGreen,
              child: CustomScrollView(
                slivers: [
                  SliverAppBar(
                    expandedHeight: 120,
                    floating: false,
                    pinned: true,
                    backgroundColor: AppColors.forestGreen,
                    actions: [
                      // View mode toggle button
                      PopupMenuButton<CalendarViewMode>(
                        icon: const Icon(Icons.view_module, color: Colors.white),
                        tooltip: 'Schimbă vizualizarea',
                        onSelected: (CalendarViewMode mode) {
                          setState(() {
                            _viewMode = mode;
                          });
                        },
                        itemBuilder: (BuildContext context) => [
                          PopupMenuItem<CalendarViewMode>(
                            value: CalendarViewMode.day,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.view_day,
                                  color: _viewMode == CalendarViewMode.day ? AppColors.forestGreen : Colors.grey,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Zi',
                                  style: TextStyle(
                                    color: _viewMode == CalendarViewMode.day ? AppColors.forestGreen : Colors.black,
                                    fontWeight: _viewMode == CalendarViewMode.day ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PopupMenuItem<CalendarViewMode>(
                            value: CalendarViewMode.week,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.view_week,
                                  color: _viewMode == CalendarViewMode.week ? AppColors.forestGreen : Colors.grey,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Săptămână',
                                  style: TextStyle(
                                    color: _viewMode == CalendarViewMode.week ? AppColors.forestGreen : Colors.black,
                                    fontWeight: _viewMode == CalendarViewMode.week ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PopupMenuItem<CalendarViewMode>(
                            value: CalendarViewMode.month,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.calendar_month,
                                  color: _viewMode == CalendarViewMode.month ? AppColors.forestGreen : Colors.grey,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Lună',
                                  style: TextStyle(
                                    color: _viewMode == CalendarViewMode.month ? AppColors.forestGreen : Colors.black,
                                    fontWeight: _viewMode == CalendarViewMode.month ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                    flexibleSpace: FlexibleSpaceBar(
                      title: const Text(
                        'Calendar',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      background: Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.forestGreen,
                              Color(0xFF2E7D32),
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.calendar_today,
                            color: Colors.white,
                            size: 48,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTodaySection(),
                          const SizedBox(height: 24),
                          _buildUpcomingSection(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton(
        heroTag: "calendar_add_fab",
        onPressed: () {
          // Navigate to add appointment screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Add appointment functionality coming soon!'),
              backgroundColor: AppColors.forestGreen,
            ),
          );
        },
        backgroundColor: AppColors.forestGreen,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildTodaySection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.today,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Astăzi - ${DateFormatter.formatDate(DateTime.now())}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_todayAppointments.isEmpty)
              const Text(
                'Nu aveți programări astăzi',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              )
            else
              ..._todayAppointments.map((appointment) => _buildAppointmentCard(appointment)),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingSection() {
    final upcoming = _upcomingAppointments.take(5).toList();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Programări viitoare',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (upcoming.isEmpty)
              const Text(
                'Nu aveți programări viitoare',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              )
            else
              ...upcoming.map((appointment) => _buildAppointmentCard(appointment)),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentCard(Appointment appointment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: _getStatusColor(appointment.status),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  appointment.clientName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${appointment.service} • ${appointment.petName}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormatter.formatAppointmentTime(appointment.startTime),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.forestGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            _getStatusIcon(appointment.status),
            color: _getStatusColor(appointment.status),
            size: 20,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return AppColors.forestGreen;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
      case 'canceled':
      case 'anulat':
        return Colors.grey.shade500; // Changed from red to grey
      case 'no_show':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return Icons.schedule;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'no_show':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }
}
