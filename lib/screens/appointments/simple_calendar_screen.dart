import 'package:flutter/material.dart';
import '../../models/appointment.dart';
import '../../services/appointment/appointment_service.dart';
import '../../config/theme/app_theme.dart';
import '../../utils/date_formatter.dart';

class SimpleCalendarScreen extends StatefulWidget {
  const SimpleCalendarScreen({Key? key}) : super(key: key);

  @override
  State<SimpleCalendarScreen> createState() => _SimpleCalendarScreenState();
}

enum CalendarViewMode { day, week, month }

class _SimpleCalendarScreenState extends State<SimpleCalendarScreen> {
  List<Appointment> _appointments = [];
  bool _isLoading = true;
  CalendarViewMode _viewMode = CalendarViewMode.day;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    try {
      final response = await AppointmentService.getAppointments();
      
      if (mounted) {
        setState(() {
          if (response.success && response.data != null) {
            _appointments = response.data!;
          } else {
            _appointments = [];
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      debugPrint('Error loading appointments: $e');
    }
  }

  List<Appointment> get _todayAppointments {
    final today = DateTime.now();
    return _appointments.where((appointment) {
      return appointment.startTime.year == today.year &&
             appointment.startTime.month == today.month &&
             appointment.startTime.day == today.day;
    }).toList();
  }

  List<Appointment> get _upcomingAppointments {
    final now = DateTime.now();
    return _appointments.where((appointment) {
      return appointment.startTime.isAfter(now);
    }).toList()..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(color: Theme.of(context).colorScheme.primary),
            )
          : RefreshIndicator(
              onRefresh: _loadAppointments,
              color: Theme.of(context).colorScheme.primary,
              child: CustomScrollView(
                slivers: [
                  SliverAppBar(
                    expandedHeight: 120,
                    floating: false,
                    pinned: true,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    actions: [
                      // View mode toggle button
                      PopupMenuButton<CalendarViewMode>(
                        icon:  Icon(Icons.view_module, color: Colors.white),
                        tooltip: 'Schimbă vizualizarea',
                        onSelected: (CalendarViewMode mode) {
                          setState(() {
                            _viewMode = mode;
                          });
                        },
                        itemBuilder: (BuildContext context) => [
                          PopupMenuItem<CalendarViewMode>(
                            value: CalendarViewMode.day,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.view_day,
                                  color: _viewMode == CalendarViewMode.day ? Theme.of(context).colorScheme.primary : Colors.grey,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'Zi',
                                  style: TextStyle(
                                    color: _viewMode == CalendarViewMode.day ? Theme.of(context).colorScheme.primary : Colors.black,
                                    fontWeight: _viewMode == CalendarViewMode.day ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PopupMenuItem<CalendarViewMode>(
                            value: CalendarViewMode.week,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.view_week,
                                  color: _viewMode == CalendarViewMode.week ? Theme.of(context).colorScheme.primary : Colors.grey,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'Săptămână',
                                  style: TextStyle(
                                    color: _viewMode == CalendarViewMode.week ? Theme.of(context).colorScheme.primary : Colors.black,
                                    fontWeight: _viewMode == CalendarViewMode.week ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PopupMenuItem<CalendarViewMode>(
                            value: CalendarViewMode.month,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.calendar_month,
                                  color: _viewMode == CalendarViewMode.month ? Theme.of(context).colorScheme.primary : Colors.grey,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'Lună',
                                  style: TextStyle(
                                    color: _viewMode == CalendarViewMode.month ? Theme.of(context).colorScheme.primary : Colors.black,
                                    fontWeight: _viewMode == CalendarViewMode.month ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                    flexibleSpace: FlexibleSpaceBar(
                      title: Text(
                        'Calendar',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      background: Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.forestGreen,
                              Color(0xFF2E7D32),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.calendar_today,
                            color: Colors.white,
                            size: 48,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTodaySection(),
                          SizedBox(height: 24),
                          _buildUpcomingSection(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton(
        heroTag: "calendar_add_fab",
        onPressed: () {
          // Navigate to add appointment screen
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Add appointment functionality coming soon!'),
              backgroundColor: AppColors.forestGreen,
            ),
          );
        },
        backgroundColor: AppColors.forestGreen,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildTodaySection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.today,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Astăzi - ${DateFormatter.formatDate(DateTime.now())}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            if (_todayAppointments.isEmpty)
              Text(
                'Nu aveți programări astăzi',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              )
            else
              ..._todayAppointments.map((appointment) => _buildAppointmentCard(appointment)),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingSection() {
    final upcoming = _upcomingAppointments.take(5).toList();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Programări viitoare',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            if (upcoming.isEmpty)
              Text(
                'Nu aveți programări viitoare',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              )
            else
              ...upcoming.map((appointment) => _buildAppointmentCard(appointment)),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentCard(Appointment appointment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: _getStatusColor(appointment.status),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  appointment.clientName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '${appointment.service} • ${appointment.petName}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  DateFormatter.formatAppointmentTime(appointment.startTime),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            _getStatusIcon(appointment.status),
            color: _getStatusColor(appointment.status),
            size: 20,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return Theme.of(context).colorScheme.primary;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
      case 'canceled':
      case 'anulat':
        return Theme.of(context).colorScheme.onSurfaceVariant;
      case 'no_show':
        return Colors.orange;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return Icons.schedule;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'no_show':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }
}
