import 'package:flutter/material.dart';

import '../../../models/sms_settings.dart';
import '../../../services/sms_settings_service.dart';
import '../../../config/theme/app_theme.dart';
import '../../../widgets/permission_guard.dart';

class SmsRemindersScreen extends StatefulWidget {
  const SmsRemindersScreen({Key? key}) : super(key: key);

  @override
  State<SmsRemindersScreen> createState() => _SmsRemindersScreenState();
}

class _SmsRemindersScreenState extends State<SmsRemindersScreen> {
  bool _appointmentConfirmations = true;
  bool _dayBeforeReminders = true;
  bool _followUpMessages = false;
  String _selectedProvider = 'Orange';

  SmsSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  final List<Map<String, dynamic>> _smsProviders = [
    {
      'name': 'Orange România',
      'id': 'orange',
      'cost': '0.05 RON/SMS',
      'features': ['API Integration', 'Bulk SMS', 'Delivery Reports'],
      'recommended': true,
    },
    {
      'name': 'Vodafone România',
      'id': 'vodafone',
      'cost': '0.06 RON/SMS',
      'features': ['API Integration', 'Bulk SMS', 'Analytics'],
      'recommended': false,
    },
    {
      'name': 'Telekom România',
      'id': 'telekom',
      'cost': '0.07 RON/SMS',
      'features': ['API Integration', 'Premium Support'],
      'recommended': false,
    },
    {
      'name': 'SMS Gateway România',
      'id': 'smsgateway',
      'cost': '0.04 RON/SMS',
      'features': ['API Integration', 'Bulk SMS', 'Templates', 'Scheduling'],
      'recommended': true,
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadSmsSettings();
  }

  /// Load SMS settings from backend
  Future<void> _loadSmsSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SmsSettingsService.getSmsSettings();

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _appointmentConfirmations = response.data!.appointmentConfirmations;
          _dayBeforeReminders = response.data!.dayBeforeReminders;
          _followUpMessages = response.data!.followUpMessages;
          _selectedProvider = response.data!.selectedProvider ?? 'Orange';
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load SMS settings';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading SMS settings: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appBackground,
      appBar: AppBar(
        title: Text(
          'SMS Reminders',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
       
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
          ? Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.primary))
          : _error != null
            ? _buildErrorView()
            : Column(
                children: [
                  // Scrollable content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // SMS Settings Card
                          _buildSmsSettingsCard(),
                          SizedBox(height: 24),

                          // SMS Providers Section
                          _buildSectionHeader('Furnizori SMS'),
                          SizedBox(height: 16),
                          ..._smsProviders.map((provider) => _buildProviderCard(provider)).toList(),
                          SizedBox(height: 24),

                          // Message Templates Section
                          _buildSectionHeader('Template-uri Mesaje'),
                          SizedBox(height: 16),
                          _buildMessageTemplatesCard(),
                          SizedBox(height: 24),

                          SizedBox(height: 80), // Space for fixed button
                        ],
                      ),
                    ),
                  ),
                  // Fixed save button at bottom
                  _buildFixedSaveButton(),
                ],
              ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildSmsSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setări Notificări SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'Confirmări programări',
              subtitle: 'Trimite SMS de confirmare la programare',
              value: _appointmentConfirmations,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('appointmentConfirmations', value),
            ),
            _buildSwitchTile(
              title: 'Reminder cu o zi înainte',
              subtitle: 'Trimite SMS cu 24h înainte de programare',
              value: _dayBeforeReminders,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('dayBeforeReminders', value),
            ),
            _buildSwitchTile(
              title: 'Mesaje de follow-up',
              subtitle: 'Trimite SMS după finalizarea serviciului',
              value: _followUpMessages,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('followUpMessages', value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildProviderCard(Map<String, dynamic> provider) {
    final isSelected = _selectedProvider == provider['id'];
    final isRecommended = provider['recommended'] as bool;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
          ? BorderSide(color: Theme.of(context).colorScheme.primary, width: 2)
          : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => setState(() => _selectedProvider = provider['id']),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      provider['name'],
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? Theme.of(context).colorScheme.primary : Colors.black87,
                      ),
                    ),
                  ),
                  if (isRecommended)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'RECOMANDAT',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  SizedBox(width: 8),
                  Radio<String>(
                    value: provider['id'],
                    groupValue: _selectedProvider,
                    onChanged: _isSaving ? null : (value) => _updateSetting('selectedProvider', value!),
                    activeColor: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                'Cost: ${provider['cost']}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: (provider['features'] as List<String>).map((feature) =>
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      feature,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageTemplatesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Template-uri Mesaje',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildTemplateItem(
              'Confirmare programare',
              'Bună ziua! Programarea dumneavoastră la {salon_name} pentru {pet_name} este confirmată pe {date} la ora {time}. Vă așteptăm!',
            ),
            SizedBox(height: 12),
            _buildTemplateItem(
              'Reminder 24h',
              'Vă reamintim că mâine, {date} la ora {time}, aveți programare la {salon_name} pentru {pet_name}. Pentru reprogramări: {phone}',
            ),
            SizedBox(height: 12),
            _buildTemplateItem(
              'Follow-up',
              'Mulțumim că ați ales {salon_name}! Sperăm că {pet_name} este mulțumit(ă) de serviciile noastre. Evaluați experiența: {review_link}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateItem(String title, String content) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildFixedSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
    );
  }

  /// Update a specific SMS setting
  Future<void> _updateSetting(String settingKey, dynamic value) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Update local state first for immediate UI feedback
      setState(() {
        switch (settingKey) {
          case 'appointmentConfirmations':
            _appointmentConfirmations = value as bool;
            break;
          case 'dayBeforeReminders':
            _dayBeforeReminders = value as bool;
            break;
          case 'followUpMessages':
            _followUpMessages = value as bool;
            break;
          case 'selectedProvider':
            _selectedProvider = value as String;
            break;
        }
      });

      // Create update request with current values
      final request = UpdateSmsSettingsRequest(
        appointmentConfirmations: _appointmentConfirmations,
        dayBeforeReminders: _dayBeforeReminders,
        followUpMessages: _followUpMessages,
        selectedProvider: _selectedProvider,
      );

      // Send to backend
      final response = await SmsSettingsService.updateSmsSettings(request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar('Setările SMS au fost actualizate cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la actualizarea setărilor SMS');
        // Revert local state
        await _loadSmsSettings();
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea setărilor: $e');
      // Revert local state
      await _loadSmsSettings();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }



  /// Show success message
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
       
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea setărilor SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadSmsSettings,
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: 16),
            Text(
              'Acces restricționat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Doar groomer-ii șefi pot configura setările SMS pentru salon.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Înapoi'),
            ),
          ],
        ),
      ),
    );
  }
}
