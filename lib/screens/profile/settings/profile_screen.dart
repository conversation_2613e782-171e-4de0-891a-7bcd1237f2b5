import 'package:animaliaproject/screens/profile/settings/services_management_screen.dart';
import 'package:animaliaproject/screens/profile/settings/sms_reminders_screen.dart';
import 'package:animaliaproject/screens/profile/settings/working_hours_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../../config/theme/app_theme.dart';
import '../../../models/user_role.dart';
import '../../../models/user_salon_association.dart';
import '../../../providers/auth_provider.dart';
import '../../../providers/calendar_provider.dart';
import '../../../providers/locale_provider.dart';
import '../../../providers/role_provider.dart';
import '../../../providers/theme_provider.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/feature_toggle_service.dart';
import '../../../services/salon_service.dart';
import '../../../services/url_launcher_service.dart';
import '../../../utils/formatters/phone_number_utils.dart';
import '../../../widgets/cards/revolut_salon_switcher.dart';
import '../../../widgets/common/standard_form_field.dart';
import '../../../widgets/onboarding/salon_onboarding_widget.dart';
import '../../../widgets/permission_guard.dart';
import '../../auth/login_screen.dart';
import '../../reports/reviews_screen.dart';
import '../salon_creation_screen.dart';
import '../team/team_management_screen.dart';
import '../user_invitations_screen.dart';
import 'notification_settings_screen.dart';
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _themeSelectionEnabled = false;
  bool _translationsEnabled = false;
  bool _reviewsEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadFeatureToggles();
  }

  Future<void> _loadFeatureToggles() async {
    final themeSelectionEnabled = await FeatureToggleService.isThemeSelectionEnabled();
    final translationsEnabled = await FeatureToggleService.isTranslationsEnabled();
    final reviewsEnabled = await FeatureToggleService.isReviewsEnabled();

    if (mounted) {
      setState(() {
        _themeSelectionEnabled = themeSelectionEnabled;
        _translationsEnabled = translationsEnabled;
        _reviewsEnabled = reviewsEnabled;
      });
    }
  }

  // Open privacy policy function
  Future<void> _openPrivacyPolicy(BuildContext context) async {
    const privacyPolicyUrl = 'https://animalia-programari.ro/privacy-policy.html';

    final success = await UrlLauncherService.openWebUrl(privacyPolicyUrl);
    if (!success && context.mounted) {
      UrlLauncherService.showLaunchError(context, 'politica de confidențialitate');
    }
  }

  // Open terms and conditions function
  Future<void> _openTermsAndConditions(BuildContext context) async {
    const termsUrl = 'https://animalia-programari.ro/terms-of-service.html';

    final success = await UrlLauncherService.openWebUrl(termsUrl);
    if (!success && context.mounted) {
      UrlLauncherService.showLaunchError(context, 'termenii și condițiile');
    }
  }

  // Delete account function
  Future<void> _deleteAccount(BuildContext context) async {
    final confirmationResult = await _showDeleteAccountConfirmation(context);
    if (confirmationResult != null && confirmationResult['confirmed'] == true) {
      final confirmationText = confirmationResult['text'] as String;

      // Show loading dialog
      if (context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Deleting account...'),
              ],
            ),
          ),
        );
      }

      try {
        final response = await AuthService.deleteAccount(confirmationText);

        if (context.mounted) {
          Navigator.of(context).pop(); // Close loading dialog

          if (response.success) {
            // Account deleted successfully - navigate to login
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
              (route) => false,
            );

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Account deleted successfully'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          } else {
            // Show error message with better handling for database errors
            String errorMessage = response.error ?? 'Unknown error occurred';

            // Check if it's a database casting error
            if (errorMessage.contains('cannot cast type bytea to boolean')) {
              errorMessage = 'Database configuration error. Please contact support.';
            } else if (errorMessage.contains('JDBC exception')) {
              errorMessage = 'Database error occurred. Please try again later or contact support.';
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to delete account: $errorMessage'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      } catch (e) {
        if (context.mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    }
  }

  Future<Map<String, dynamic>?> _showDeleteAccountConfirmation(BuildContext context) async {
    final TextEditingController confirmController = TextEditingController();

    return showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Account',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Warning: All your data will be permanently deleted and cannot be recovered.',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Type "confirm" to proceed:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              TextField(
                controller: confirmController,
                decoration: InputDecoration(
                  hintText: 'confirm',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop({'confirmed': false}),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final text = confirmController.text.trim();
                if (text.toLowerCase() == 'confirm') {
                  Navigator.of(context).pop({
                    'confirmed': true,
                    'text': text,
                  });
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Please type "confirm" to proceed'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                
              ),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // Logout function
  Future<void> _logout(BuildContext context) async {
    // Log out using the auth provider
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.signOut();

    if (context.mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const LoginScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Profil & Setări',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            elevation: 0,
            actions: [
              IconButton(
                icon:  Icon(Icons.logout),
                onPressed: () => _logout(context),
                tooltip: 'Deconectare',
              ),
            ],
          ),
          body: roleProvider.hasSalonAssociation
              ? _buildProfileWithSalon(context)
              : _buildOnboardingFlow(context),
        );
      },
    );
  }

  /// Build the normal profile screen for users with salon association
  Widget _buildProfileWithSalon(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Salon Management Section (now includes user name)
          _buildSalonManagementSection(),
          SizedBox(height: 24),

          // Management Section (Admin/Chief Groomer only)
          PermissionGuard.management(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Gestionare'),
                SizedBox(height: 16),
                _buildManagementOptions(context),
                SizedBox(height: 24),
              ],
            ),
          ),

          // Business Section (Admin/Chief Groomer only)
          PermissionGuard.management(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Business'),
                SizedBox(height: 16),
                _buildBusinessOptions(context),
                SizedBox(height: 24),
              ],
            ),
          ),

          // Settings Section
          _buildSectionHeader('Setări'),
          SizedBox(height: 16),
          _buildSettingsOptions(context),
          SizedBox(height: 24),

          // Application Section
          _buildSectionHeader('Aplicație'),
          SizedBox(height: 16),
          _buildApplicationOptions(context),
          SizedBox(height: 24),

          // Logout Section
          _buildLogoutSection(context),
          SizedBox(height: 24),

          // Delete Account Section
          _buildDeleteAccountSection(context),
        ],
      ),
    );
  }

  /// Build the onboarding flow for users without salon association
  Widget _buildOnboardingFlow(BuildContext context) {
    return const SalonOnboardingWidget();
  }





  Widget _buildSalonManagementSection() {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Saloane'),
            SizedBox(height: 16),

            // Show different content based on salon association
            FutureBuilder<String?>(
              future: AuthService.getCurrentSalonId(),
              builder: (context, snapshot) {
                final hasSalonId = snapshot.data != null && snapshot.data!.isNotEmpty;

                if (hasSalonId) {
                  // User has salon association - show consolidated salon management card
                  return _buildConsolidatedSalonCard(roleProvider);
                } else {
                  // User has no salon association - show create salon option
                  return Column(
                    children: [
                      _buildCreateSalonCard(),
                      SizedBox(height: 12),
                      _buildPendingInvitationsCard(),
                    ],
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildCreateSalonCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.add_business,
                color: Theme.of(context).colorScheme.primary,
                size: 32,
              ),
            ),
            SizedBox(height: 16),
             Text(
              'Creează-ți propriul salon',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Începe-ți afacerea de toaletaj și invită alți groomeri să se alăture echipei tale',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: Builder(
                builder: (context) => ElevatedButton.icon(
                  onPressed: () => _navigateToCreateSalon(context),
                icon:  Icon(Icons.add),
                label: Text(
                  'Creează Salon Nou',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                 
                  
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
              ),
            ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildConsolidatedSalonCard(RoleProvider roleProvider) {
    return FutureBuilder<List<UserSalonAssociation>>(
      future: SalonService.getUserSalons().then((response) => response.data ?? []),
      builder: (context, snapshot) {
        final salons = snapshot.data ?? [];
        final currentSalon = salons.isNotEmpty
            ? salons.firstWhere((s) => s.isCurrentSalon, orElse: () => salons.first)
            : null;
        final hasMultipleSalons = salons.length > 1;

        return Card(
          elevation: 3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _handleSalonCardTap(context, currentSalon, hasMultipleSalons, roleProvider),
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with user name and salon info
                      FutureBuilder<String?>(
                        future: AuthService.getCurrentUserName(),
                        builder: (context, snapshot) {
                          final userName = snapshot.data ?? 'Utilizator';
                          return Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.person,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  size: 24,
                                ),
                              ),
                              SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      userName,
                                      style: TextStyle(
                                        fontSize: 22,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).colorScheme.onPrimary,
                                      ),
                                    ),
                                    SizedBox(height: 2),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.business,
                                          color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                                          size: 16,
                                        ),
                                        SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            currentSalon?.salon.name ?? 'Salon de Toaletaj',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 2),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.location_on,
                                          color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7),
                                          size: 14,
                                        ),
                                        SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            currentSalon?.salon.address ?? 'Adresa salon',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              if (hasMultipleSalons) ...[
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.swap_horiz,
                                        color: Theme.of(context).colorScheme.onPrimary,
                                        size: 16,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        '${salons.length}',
                                        style: TextStyle(
                                          color: Theme.of(context).colorScheme.onPrimary,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          );
                        },
                      ),

                      SizedBox(height: 20),

                      // Metrics row
                      Row(
                        children: [
                          Expanded(
                            child: _buildMetricItem(
                              'Clienți',
                              '${currentSalon?.clientCount ?? 0}',
                              Icons.people,
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.3),
                          ),
                          Expanded(
                            child: Consumer<CalendarProvider>(
                              builder: (context, calendarProvider, child) {
                                // Get today's appointments count
                                final todayAppointments = calendarProvider.appointments.where((appointment) {
                                  final appointmentDate = DateTime(
                                    appointment.startTime.year,
                                    appointment.startTime.month,
                                    appointment.startTime.day,
                                  );
                                  final today = DateTime(
                                    DateTime.now().year,
                                    DateTime.now().month,
                                    DateTime.now().day,
                                  );
                                  return appointmentDate.isAtSameMomentAs(today);
                                }).length;

                                return _buildMetricItem(
                                  'Programări',
                                  '$todayAppointments',
                                  Icons.calendar_today,
                                );
                              },
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.3),
                          ),
                          Expanded(
                            child: _buildMetricItem(
                              'Status',
                              'Deschis', // todo Mock data - replace with real data
                              Icons.schedule,
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 16),

                      // Role and permissions row
                      LayoutBuilder(
                        builder: (context, constraints) {
                          final availableWidth = constraints.maxWidth;
                          final isNarrowScreen = availableWidth < 350;

                          return Column(
                            children: [
                              // First row with role and permission badges
                              Row(
                                children: [
                                  Flexible(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            _getRoleIcon(currentSalon?.groomerRole),
                                            color: Theme.of(context).colorScheme.onPrimary,
                                            size: 14,
                                          ),
                                          SizedBox(width: 4),
                                          Flexible(
                                            child: Text(
                                              currentSalon?.roleDisplayName ?? 'Groomer',
                                              style: TextStyle(
                                                color: Theme.of(context).colorScheme.onPrimary,
                                                fontWeight: FontWeight.w600,
                                                fontSize: 12,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Flexible(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        currentSalon?.permissionDisplayName ?? 'Acces Limitat',
                                        style: TextStyle(
                                          color: Theme.of(context).colorScheme.onPrimary,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              // Second row with action hint
                              SizedBox(height: 8),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.touch_app,
                                    color: Colors.white.withValues(alpha: 0.6),
                                    size: 14,
                                  ),
                                  SizedBox(width: 4),
                                  Flexible(
                                    child: Text(
                                      hasMultipleSalons
                                        ? (isNarrowScreen ? 'Apasă pentru schimbare' : 'Apasă pentru a schimba')
                                        : (isNarrowScreen ? 'Apasă pentru opțiuni' : 'Apasă pentru opțiuni salon'),
                                      style: TextStyle(
                                        color: Colors.white.withValues(alpha: 0.8),
                                        fontSize: 11,
                                        fontStyle: FontStyle.italic,
                                      ),
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white.withValues(alpha: 0.9),
          size: 20,
        ),
        SizedBox(height: 6),
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  IconData _getRoleIcon(GroomerRole? role) {
    switch (role) {
      case GroomerRole.chiefGroomer:
        return Icons.star;
      case GroomerRole.groomer:
        return Icons.person;
      case GroomerRole.assistant:
        return Icons.support_agent;
      case GroomerRole.seniorGroomer:
        return Icons.workspace_premium;
      case null:
        return Icons.device_unknown;
      // ignore: deprecated_member_use_from_same_package
      case GroomerRole.regularGroomer:
        return Icons.person; // Legacy support
    }
  }

  /// Handle salon card tap - always shows salon switcher with integrated management actions
  void _handleSalonCardTap(BuildContext context, UserSalonAssociation? currentSalon, bool hasMultipleSalons, RoleProvider roleProvider) {
    // Always show salon switcher - it now includes management actions
    _showSalonSwitcher(context);
  }

  /// Show salon management options for users with single salon
  void _showSalonManagementOptions(BuildContext context, UserSalonAssociation? currentSalon, RoleProvider roleProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    'Opțiuni Salon',
                    style:  TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    currentSalon?.salon.name ?? 'Salon necunoscut',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),

            // Options
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // Create new salon option
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child:  Icon(
                        Icons.add_business,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    title: Text('Creează Salon Nou'),
                    subtitle: Text('Deschide un nou salon de toaletaj'),
                    onTap: () {
                      Navigator.pop(context);
                      _navigateToCreateSalon(context);
                    },
                  ),

                  // View pending invitations
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.mail_outline,
                        color: Colors.orange,
                      ),
                    ),
                    title: Text('Invitații în Așteptare'),
                    subtitle: Text('Vezi invitațiile de la alte saloane'),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const UserInvitationsScreen(),
                        ),
                      );
                    },
                  ),

                  if (roleProvider.permissions?.groomerRole ==
                      GroomerRole.chiefGroomer) ...[
                    // Edit salon option
                    ListTile(
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.edit,
                          color: Colors.blue,
                        ),
                      ),
                      title: Text('Editează Salon'),
                      subtitle:
                          Text('Modifică informațiile salonului curent'),
                      onTap: () {
                        Navigator.pop(context);
                        if (currentSalon != null) {
                          _navigateToEditSalon(context, currentSalon);
                        }
                      },
                    ),

                    // Delete salon option
                    ListTile(
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.delete,
                          color: Colors.red,
                        ),
                      ),
                      title: Text('Șterge Salon'),
                      subtitle:
                          Text('Șterge definitiv acest salon'),
                      onTap: () {
                        Navigator.pop(context);
                        if (currentSalon != null) {
                          _confirmDeleteSalon(context, currentSalon.salon.id);
                        }
                      },
                    ),
                  ],

                  SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildPendingInvitationsCard() {
    return Builder(
      builder: (context) => _buildOptionCard(
        context,
        icon: Icons.mail_outline,
        title: 'Invitații în Așteptare',
        subtitle: 'Vezi invitațiile de la alte saloane',
        onTap: () => Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const UserInvitationsScreen(),
          ),
        ),
      ),
    );
  }

  Future<void> _navigateToCreateSalon(BuildContext context) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const SalonCreationScreen(),
      ),
    );

    if (result == true && context.mounted) {
      // Salon was created successfully, refresh all providers
      final roleProvider = context.read<RoleProvider>();
      final calendarProvider = context.read<CalendarProvider>();

      // Refresh role provider first
      debugPrint('🔄 Refreshing role provider after salon creation...');
      await roleProvider.refresh();

      // Add a longer delay to ensure backend has processed the staff creation
      debugPrint('🔄 Waiting for backend to process staff creation...');
      await Future.delayed(const Duration(seconds: 2));

      // Retry staff loading with multiple attempts
      if (context.mounted) {
        await _retryStaffLoading(calendarProvider);
        await calendarProvider.loadServices();

        debugPrint('✅ Salon created successfully - calendar should now be available');
      }
    }
  }

  Future<void> _navigateToEditSalon(
      BuildContext context, UserSalonAssociation association) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => SalonCreationScreen(salon: association.salon),
      ),
    );

    if (result == true && context.mounted) {
      final roleProvider = context.read<RoleProvider>();
      final calendarProvider = context.read<CalendarProvider>();
      await roleProvider.refresh();
      await Future.delayed(const Duration(seconds: 2));
      if (context.mounted) {
        await _retryStaffLoading(calendarProvider);
        await calendarProvider.loadServices();
      }
    }
  }

  Future<void> _confirmDeleteSalon(
      BuildContext context, String salonId) async {
    final firstConfirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmare'),
        content: Text(
            'Ești sigur că vrei să ștergi acest salon? Această acțiune nu poate fi anulată.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              
            ),
            child: Text('Continuă'),
          ),
        ],
      ),
    );

    if (firstConfirm != true) return;

    final textController = TextEditingController();
    final secondConfirm = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          'Confirmare Finală',
          style: TextStyle(color: Colors.red),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Tastează DELETE pentru a confirma.'),
            SizedBox(height: 12),
            TextField(controller: textController),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () {
              if (textController.text.trim().toUpperCase() == 'DELETE') {
                Navigator.of(context).pop(true);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              
            ),
            child: Text('Șterge'),
          ),
        ],
      ),
    );

    if (secondConfirm != true) return;

    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const AlertDialog(
          content: SizedBox(
            height: 80,
            child: Center(child: CircularProgressIndicator()),
          ),
        ),
      );
    }

    final response = await SalonService.deleteSalon(salonId);

    if (context.mounted) {
      Navigator.of(context).pop();
      if (response.success) {
        // Add a delay to ensure backend has processed the deletion
        debugPrint('🔄 ProfileScreen: Waiting before refreshing role provider...');
        await Future.delayed(const Duration(seconds: 1));

        final roleProvider = context.read<RoleProvider>();
        debugPrint('🔄 ProfileScreen: Refreshing role provider...');
        await roleProvider.refresh();
        debugPrint('✅ ProfileScreen: Role provider refresh completed');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Salonul a fost șters'),
            backgroundColor: Colors.green,
          ),
        );
        // The MainLayout will automatically handle showing onboarding if user has no salons left
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare: ${response.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEditNameDialog(BuildContext context, String currentName) {
    final TextEditingController nameController = TextEditingController(text: currentName);
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Editează Numele',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: nameController,
            decoration: InputDecoration(
              labelText: 'Nume',
              hintText: 'Introdu numele tău',
              border: OutlineInputBorder(),
            ),
            textCapitalization: TextCapitalization.words,
            autofocus: true,
            validator: (value) => AuthService.validateName(value ?? ''),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Anulează',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => _updateUserName(context, nameController.text, formKey),
            style: ElevatedButton.styleFrom(
             
              
            ),
            child: Text('Salvează'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateUserName(BuildContext context, String newName, GlobalKey<FormState> formKey) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      final response = await AuthService.updateUserName(newName.trim());

      if (!mounted) return; // Check if widget is still mounted

      // Close dialog first
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();

      if (response.success) {
        // Use SchedulerBinding to defer SnackBar until after frame
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Numele a fost actualizat cu succes!'),
               
                duration: Duration(seconds: 2),
              ),
            );
          }
        });

        // Trigger a rebuild to show the updated name
        if (mounted) {
          setState(() {});
        }
      } else {
        // Use SchedulerBinding for error SnackBar too
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Eroare la actualizarea numelui: ${response.error}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      // Close dialog
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();

      // Use SchedulerBinding for exception SnackBar
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Eroare la actualizarea numelui: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  void _showEditPhoneDialog(BuildContext context, String? currentPhone) {
    final TextEditingController phoneController = TextEditingController(
      text: currentPhone != null && currentPhone != 'Nu este setat' ? currentPhone : '',
    );
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title:  Text(
          'Editează Telefonul',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: phoneController,
                keyboardType: TextInputType.phone,
                decoration:  InputDecoration(
                  labelText: 'Număr de telefon',
                  hintText: '+40 XXX XXX XXX',
                  prefixIcon: Icon(Icons.phone, color: Theme.of(context).colorScheme.primary),
                  border: OutlineInputBorder(),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
                  ),
                  labelStyle: TextStyle(color: Theme.of(context).colorScheme.primary),
                  helperText: 'Format: +40 XXX XXX XXX',
                ),
                autofocus: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Numărul de telefon este obligatoriu';
                  }
                  if (!PhoneNumberUtils.isValidRomanianMobile(value)) {
                    return 'Numărul de telefon nu este valid (ex: +40728626399)';
                  }
                  return null;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[\d\s\+\-\(\)]')),
                  PhoneNumberFormatter(),
                ],
              ),
              SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                     Icon(Icons.info_outline, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Schimbarea numărului de telefon va necesita confirmare prin SMS.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Anulează',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => _showPhoneConfirmationDialog(context, phoneController.text, formKey),
            style: ElevatedButton.styleFrom(
             
              
            ),
            child: Text('Continuă'),
          ),
        ],
      ),
    );
  }

  void _showPhoneConfirmationDialog(BuildContext context, String newPhone, GlobalKey<FormState> formKey) {
    if (!formKey.currentState!.validate()) {
      return;
    }

    Navigator.of(context).pop(); // Close the phone edit dialog

    final TextEditingController codeController = TextEditingController();
    final GlobalKey<FormState> confirmFormKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title:  Text(
          'Confirmă Telefonul',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Form(
          key: confirmFormKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                     Icon(
                      Icons.sms_outlined,
                      color: Theme.of(context).colorScheme.primary,
                      size: 32,
                    ),
                     SizedBox(height: 8),
                    Text(
                      'Am trimis un cod de confirmare la:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                     SizedBox(height: 4),
                    Text(
                      newPhone,
                      style:  TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),
              TextFormField(
                controller: codeController,
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 8,
                ),
                decoration:  InputDecoration(
                  labelText: 'Cod de confirmare',
                  hintText: '123456',
                  border: OutlineInputBorder(),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
                  ),
                  labelStyle: TextStyle(color: Theme.of(context).colorScheme.primary),
                  counterText: '',
                ),
                maxLength: 6,
                autofocus: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Codul este obligatoriu';
                  }
                  if (value.length != 6) {
                    return 'Codul trebuie să aibă 6 cifre';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Nu ai primit codul? ',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => _resendPhoneConfirmationCode(newPhone),
                    child:  Text(
                      'Retrimite',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Anulează',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => _confirmPhoneUpdate(context, newPhone, codeController.text, confirmFormKey),
            style: ElevatedButton.styleFrom(
             
              
            ),
            child: Text('Confirmă'),
          ),
        ],
      ),
    );
  }

  Future<void> _resendPhoneConfirmationCode(String phoneNumber) async {
    try {
      final response = await AuthService.resendPhoneConfirmationCode(phoneNumber);

      if (!mounted) return; // Check if widget is still mounted

      if (response.success) {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Codul a fost retrimis cu succes!'),
               
                duration: Duration(seconds: 2),
              ),
            );
          }
        });
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Eroare la retrimirea codului: ${response.error}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Eroare la retrimirea codului: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  Future<void> _confirmPhoneUpdate(BuildContext context, String newPhone, String confirmationCode, GlobalKey<FormState> formKey) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      final response = await AuthService.updateUserPhone(newPhone, confirmationCode);

      if (!mounted) return; // Check if widget is still mounted

      // Close dialog first
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();

      if (response.success) {
        // Use SchedulerBinding to defer SnackBar until after frame
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Numărul de telefon a fost actualizat cu succes!'),
               
                duration: Duration(seconds: 2),
              ),
            );
          }
        });

        // Trigger a rebuild to show the updated phone
        if (mounted) {
          setState(() {});
        }
      } else {
        // Use SchedulerBinding for error SnackBar too
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Eroare la actualizarea telefonului: ${response.error}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      // Close dialog
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();

      // Use SchedulerBinding for exception SnackBar
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Eroare la actualizarea telefonului: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style:  TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildManagementOptions(BuildContext context) {
    return Column(
      children: [
        // Enhanced Services card with warning indicator
        Consumer<CalendarProvider>(
          builder: (context, calendarProvider, child) {
            final hasServices = calendarProvider.services.isNotEmpty;
            final servicesCount = calendarProvider.services.length;

            return _buildEnhancedOptionCard(
              context,
              icon: Icons.room_service,
              title: 'Servicii',
              subtitle: hasServices
                ? 'Gestionează serviciile oferite ($servicesCount servicii)'
                : 'Gestionează serviciile oferite',
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ServicesManagementScreen()),
              ),
              showWarning: !hasServices,
              warningText: 'Nu ai servicii configurate!',
            );
          },
        ),
        SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.group,
          title: 'Echipa',
          subtitle: 'Gestionează membrii echipei',
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const TeamManagementScreen()),
          ),
        ),
      ],
    );
  }

  Widget _buildBusinessOptions(BuildContext context) {
    return Column(
      children: [
        _buildOptionCard(
          context,
          icon: Icons.schedule,
          title: 'Program de lucru',
          subtitle: 'Configurează orele de funcționare salon',
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const WorkingHoursScreen()),
          ),
        ),
        SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.sms,
          title: 'Reminders SMS',
          subtitle: 'Notificări SMS pentru clienți',
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SmsRemindersScreen()),
          ),
        ),
        SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.notifications,
          title: 'Notificări',
          subtitle: 'Setări notificări salon',
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const NotificationSettingsScreen()),
          ),
        ),
        if (_reviewsEnabled) ...[
          SizedBox(height: 8),
          _buildOptionCard(
            context,
            icon: Icons.star_rate,
            title: 'Recenzii',
            subtitle: 'Gestionează recenziile clienților',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ReviewsScreen()),
            ),
          ),
        ],
        // Temporarily hide reports section
        // SizedBox(height: 12),
        // _buildOptionCard(
        //   context,
        //   icon: Icons.analytics,
        //   title: 'Rapoarte',
        //   subtitle: 'Statistici și analize business',
        //   onTap: () => Navigator.push(
        //     context,
        //     MaterialPageRoute(builder: (context) => const ReportsScreen()),
        //   ),
        // ),
      ],
    );
  }

  Widget _buildSettingsOptions(BuildContext context) {
    return Column(
      children: [
        // User name editing option
        FutureBuilder<String?>(
          future: AuthService.getCurrentUserName(),
          builder: (context, snapshot) {
            final currentName = snapshot.data ?? 'Utilizator';
            return _buildOptionCard(
              context,
              icon: Icons.person_outline,
              title: 'Editează Numele',
              subtitle: 'Nume curent: $currentName',
              onTap: () => _showEditNameDialog(context, currentName),
              dense: true,
            );
          },
        ),
        SizedBox(height: 8),

        // Phone number editing option
        FutureBuilder<String?>(
          future: AuthService.getCurrentUserPhone(),
          builder: (context, snapshot) {
            final currentPhone = snapshot.data ?? 'Nu este setat';
            return _buildOptionCard(
              context,
              icon: Icons.phone_outlined,
              title: 'Editează Telefonul',
              subtitle: 'Telefon curent: $currentPhone',
              onTap: () => _showEditPhoneDialog(context, snapshot.data),
              dense: true,
            );
          },
        ),
        SizedBox(height: 8),

        // Theme toggle card - only show if feature is enabled
        if (true) ...[ // if theme selection is enabled
          _buildThemeToggleCard(context),
          SizedBox(height: 8),
        ],

        // Translations button - only show if feature is enabled
        if (_translationsEnabled)
          _buildTranslationsCard(context),
      ],
    );
  }

  Widget _buildThemeToggleCard(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding:  EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        themeProvider.isDarkMode ? 'Dark Mode' : 'Light Mode',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: themeProvider.getTextColor(context),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Comutare între tema clară și întunecată',
                        style: TextStyle(
                          fontSize: 14,
                          color: themeProvider.getSecondaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: themeProvider.isDarkMode,
                  onChanged: (value) async {
                    await themeProvider.toggleTheme();

                    // Use SchedulerBinding to defer SnackBar until after frame
                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      if (mounted && context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              themeProvider.isDarkMode
                                  ? 'Tema întunecată activată'
                                  : 'Tema clară activată'
                            ),
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTranslationsCard(BuildContext context) {
    return Consumer<LocaleProvider>(
      builder: (context, localeProvider, child) {
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child:  Icon(
                    Icons.translate,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Limba aplicației',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 4),
                      DropdownButton<Locale>(
                        value: localeProvider.locale,
                        onChanged: (locale) {
                          if (locale != null) {
                            localeProvider.setLocale(locale);
                          }
                        },
                        items: const [
                          DropdownMenuItem(
                            value: Locale('ro'),
                            child: Text('Română'),
                          ),
                          DropdownMenuItem(
                            value: Locale('en'),
                            child: Text('English'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOptionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool dense = false,
  }) {
    return _buildEnhancedOptionCard(
      context,
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      dense: dense,
    );
  }

  Widget _buildEnhancedOptionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showWarning = false,
    String? warningText,
    bool dense = false,
  }) {
    return Card(
      elevation: showWarning ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: showWarning
          ? BorderSide(color: Colors.orange, width: 1.5)
          : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: showWarning
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    Colors.orange.withValues(alpha: 0.05),
                    Colors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              )
            : null,
          child: Padding(
            padding: dense ? const EdgeInsets.all(12) : const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: showWarning
                          ? Colors.orange.withValues(alpha: 0.1)
                          : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        icon,
                        color: showWarning ? Colors.orange : Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (showWarning) ...[
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.warning,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      SizedBox(width: 8),
                    ],
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.grey.shade400,
                      size: 16,
                    ),
                  ],
                ),
                if (showWarning && warningText != null) ...[
                  SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.orange,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            warningText,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.orange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildApplicationOptions(BuildContext context) {
    return Column(
      children: [
        // Privacy Policy
        _buildPrivacyPolicySection(context),
        SizedBox(height: 8),

        // Terms and Conditions
        _buildTermsAndConditionsSection(context),
      ],
    );
  }

  Widget _buildTermsAndConditionsSection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.article_outlined,
      title: 'Termeni și Condiții',
      subtitle: 'Vezi termenii și condițiile',
      onTap: () => _openTermsAndConditions(context),
    );
  }

  Widget _buildPrivacyPolicySection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.privacy_tip_outlined,
      title: 'Politica de Confidențialitate',
      subtitle: 'Vezi politica de confidențialitate',
      onTap: () => _openPrivacyPolicy(context),
    );
  }

  Widget _buildLogoutSection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.logout,
      title: 'Deconectare',
      subtitle: 'Ieși din contul tău',
      onTap: () => _logout(context),
    );
  }

  Widget _buildDeleteAccountSection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.delete_forever_outlined,
      title: 'Delete Account',
      subtitle: 'Șterge definitiv contul',
      onTap: () => _deleteAccount(context),
    );
  }

  void _showSalonSwitcher(BuildContext context) async {
    try {
      final response = await SalonService.getUserSalons();

      if (!mounted) return; // Check if widget is still mounted

      if (response.success && response.data != null) {
        // ignore: use_build_context_synchronously
        _showRevolutStyleSalonSwitcher(context, response.data!);
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Eroare la încărcarea saloanelor: ${response.error}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Eroare la încărcarea saloanelor: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    }
  }

  void _showRevolutStyleSalonSwitcher(BuildContext context, List<UserSalonAssociation> salons) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            RevolutStyleSalonSwitcher(salons: salons),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }

  /// Retry staff loading with multiple attempts
  Future<void> _retryStaffLoading(CalendarProvider calendarProvider) async {
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      debugPrint('🔄 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        debugPrint('✅ Staff loaded successfully on attempt $attempt');
        return;
      }

      if (attempt < maxAttempts) {
        debugPrint('⏳ No staff found, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    debugPrint('❌ Failed to load staff after $maxAttempts attempts');
  }
}