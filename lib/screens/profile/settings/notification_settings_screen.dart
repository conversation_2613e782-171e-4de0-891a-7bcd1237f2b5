import 'package:flutter/material.dart';

import '../../../models/notification_settings.dart';
import '../../../services/notification_settings_service.dart';
import '../../../config/theme/app_theme.dart';
import '../../../widgets/permission_guard.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  // Backend integration state
  NotificationSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // Local state for UI
  bool _pushNotificationsEnabled = true;
  String _soundPreference = 'default';
  bool _vibrationEnabled = true;

  // Do Not Disturb settings
  bool _dndEnabled = false;
  String _dndStart = '22:00';
  String _dndEnd = '08:00';
  bool _allowCritical = true;

  // Notification rules
  bool _newAppointments = true;
  bool _appointmentCancellations = true;
  bool _paymentConfirmations = true;
  bool _teamMemberUpdates = true;
  bool _systemMaintenanceAlerts = true;
  NotificationPriority _defaultPriority = NotificationPriority.normal;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  /// Load notification settings from backend
  Future<void> _loadNotificationSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await NotificationSettingsService.getNotificationSettings();

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _pushNotificationsEnabled = response.data!.pushNotificationsEnabled;
          _soundPreference = response.data!.soundPreference;
          _vibrationEnabled = response.data!.vibrationEnabled;

          // Do Not Disturb settings
          _dndEnabled = response.data!.doNotDisturb.enabled;
          _dndStart = response.data!.doNotDisturb.startTime;
          _dndEnd = response.data!.doNotDisturb.endTime;
          _allowCritical = response.data!.doNotDisturb.allowCritical;

          // Notification rules
          _newAppointments = response.data!.notificationRules.newAppointments;
          _appointmentCancellations = response.data!.notificationRules.appointmentCancellations;
          _paymentConfirmations = response.data!.notificationRules.paymentConfirmations;
          _teamMemberUpdates = response.data!.notificationRules.teamMemberUpdates;
          _systemMaintenanceAlerts = response.data!.notificationRules.systemMaintenanceAlerts;
          _defaultPriority = response.data!.notificationRules.defaultPriority;

          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load notification settings';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading notification settings: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Setări Notificări',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
       
        elevation: 0,

      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
          ? Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.primary))
          : _error != null
            ? _buildErrorView()
            : Column(
                children: [
                  // Scrollable content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // General Settings Section
                          _buildSectionHeader('Setări Generale'),
                          SizedBox(height: 16),
                          _buildGeneralSettingsCard(),
                          SizedBox(height: 24),

                          // Sound & Vibration Section
                          _buildSectionHeader('Sunet și Vibrații'),
                          SizedBox(height: 16),
                          _buildSoundVibrationCard(),
                          SizedBox(height: 24),

                          // Do Not Disturb Section
                          _buildSectionHeader('Nu Deranja'),
                          SizedBox(height: 16),
                          _buildDoNotDisturbCard(),
                          SizedBox(height: 24),

                          // Notification Rules Section
                          _buildSectionHeader('Reguli Notificări'),
                          SizedBox(height: 16),
                          _buildNotificationRulesCard(),
                          SizedBox(height: 80), // Space for fixed button
                        ],
                      ),
                    ),
                  ),
                  // Fixed save button at bottom
                  _buildFixedSaveButton(),
                ],
              ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildGeneralSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setări Generale Notificări',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'Notificări Push',
              subtitle: 'Activează toate notificările push',
              value: _pushNotificationsEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('pushNotificationsEnabled', value),
              icon: Icons.notifications,
            ),
            SizedBox(height: 16),
            Text(
              'Prioritate implicită:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            DropdownButtonFormField<NotificationPriority>(
              value: _defaultPriority,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: NotificationPriority.values.map((priority) {
                return DropdownMenuItem(
                  value: priority,
                  child: Text(priority.displayName),
                );
              }).toList(),
              onChanged: _isSaving ? null : (value) {
                if (value != null) {
                  _updateSetting('defaultPriority', value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Theme.of(context).colorScheme.primary, size: 20),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildSoundVibrationCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setări Sunet și Vibrații',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'Vibrații',
              subtitle: 'Vibrează pentru notificări',
              value: _vibrationEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('vibrationEnabled', value),
              icon: Icons.vibration,
            ),
            SizedBox(height: 16),
            Text(
              'Sunet notificare:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _soundPreference,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: 'default', child: Text('Sunet implicit')),
                DropdownMenuItem(value: 'silent', child: Text('Silențios')),
                DropdownMenuItem(value: 'custom1', child: Text('Clopoțel')),
                DropdownMenuItem(value: 'custom2', child: Text('Clopot')),
                DropdownMenuItem(value: 'custom3', child: Text('Notificare')),
              ],
              onChanged: _isSaving ? null : (value) {
                if (value != null) {
                  _updateSetting('soundPreference', value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDoNotDisturbCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setări Nu Deranja',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'Nu Deranja',
              subtitle: 'Activează modul Nu Deranja',
              value: _dndEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('dndEnabled', value),
              icon: Icons.do_not_disturb,
            ),
            if (_dndEnabled) ...[
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Ora început:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        InkWell(
                          onTap: _isSaving ? null : () => _selectTime(true),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).colorScheme.outline),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(_dndStart),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Ora sfârșit:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        InkWell(
                          onTap: _isSaving ? null : () => _selectTime(false),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).colorScheme.outline),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(_dndEnd),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              _buildSwitchTile(
                title: 'Permite notificări critice',
                subtitle: 'Permite notificări importante în timpul Nu Deranja',
                value: _allowCritical,
                onChanged: _isSaving ? (value) {} : (value) => _updateSetting('allowCritical', value),
                icon: Icons.priority_high,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationRulesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reguli Notificări',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'Programări noi',
              subtitle: 'Notificare la programări noi',
              value: _newAppointments,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('newAppointments', value),
              icon: Icons.event_available,
            ),
            _buildSwitchTile(
              title: 'Anulări programări',
              subtitle: 'Notificare la anularea programărilor',
              value: _appointmentCancellations,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('appointmentCancellations', value),
              icon: Icons.event_busy,
            ),
            _buildSwitchTile(
              title: 'Confirmări plăți',
              subtitle: 'Notificare la confirmarea plăților',
              value: _paymentConfirmations,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('paymentConfirmations', value),
              icon: Icons.payment,
            ),
            _buildSwitchTile(
              title: 'Actualizări echipă',
              subtitle: 'Notificare la schimbări în echipă',
              value: _teamMemberUpdates,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('teamMemberUpdates', value),
              icon: Icons.group,
            ),
            _buildSwitchTile(
              title: 'Alerte mentenanță sistem',
              subtitle: 'Notificare pentru mentenanța sistemului',
              value: _systemMaintenanceAlerts,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('systemMaintenanceAlerts', value),
              icon: Icons.build,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _saveSettings,
            icon:  Icon(Icons.save),
            label: Text(
              'Salvează Setările',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
             
              
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
      ),
    );
  }


  /// Update a specific notification setting
  Future<void> _updateSetting(String settingKey, dynamic value) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Update local state first for immediate UI feedback
      setState(() {
        switch (settingKey) {
          case 'pushNotificationsEnabled':
            _pushNotificationsEnabled = value as bool;
            break;
          case 'vibrationEnabled':
            _vibrationEnabled = value as bool;
            break;
          case 'soundPreference':
            _soundPreference = value as String;
            break;
          case 'dndEnabled':
            _dndEnabled = value as bool;
            break;
          case 'allowCritical':
            _allowCritical = value as bool;
            break;
          case 'newAppointments':
            _newAppointments = value as bool;
            break;
          case 'appointmentCancellations':
            _appointmentCancellations = value as bool;
            break;
          case 'paymentConfirmations':
            _paymentConfirmations = value as bool;
            break;
          case 'teamMemberUpdates':
            _teamMemberUpdates = value as bool;
            break;
          case 'systemMaintenanceAlerts':
            _systemMaintenanceAlerts = value as bool;
            break;
          case 'defaultPriority':
            _defaultPriority = value as NotificationPriority;
            break;
        }
      });

      // Create update request with current values
      final request = UpdateNotificationSettingsRequest(
        pushNotificationsEnabled: _pushNotificationsEnabled,
        soundPreference: _soundPreference,
        vibrationEnabled: _vibrationEnabled,
        doNotDisturb: DoNotDisturbSettings(
          enabled: _dndEnabled,
          startTime: _dndStart,
          endTime: _dndEnd,
          allowCritical: _allowCritical,
        ),
        notificationRules: NotificationRules(
          newAppointments: _newAppointments,
          appointmentCancellations: _appointmentCancellations,
          paymentConfirmations: _paymentConfirmations,
          teamMemberUpdates: _teamMemberUpdates,
          systemMaintenanceAlerts: _systemMaintenanceAlerts,
          defaultPriority: _defaultPriority,
        ),
      );

      // Validate request
      final validationError = NotificationSettingsService.validateNotificationSettingsRequest(request);
      if (validationError != null) {
        _showErrorSnackBar(validationError);
        // Revert local state
        await _loadNotificationSettings();
        return;
      }

      // Send to backend
      final response = await NotificationSettingsService.updateNotificationSettings(request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar('Setările de notificare au fost actualizate cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la actualizarea setărilor de notificare');
        // Revert local state
        await _loadNotificationSettings();
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea setărilor: $e');
      // Revert local state
      await _loadNotificationSettings();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Select time for Do Not Disturb
  Future<void> _selectTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: int.parse((isStartTime ? _dndStart : _dndEnd).split(':')[0]),
        minute: int.parse((isStartTime ? _dndStart : _dndEnd).split(':')[1]),
      ),
    );

    if (picked != null) {
      final timeString = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';

      if (isStartTime) {
        setState(() {
          _dndStart = timeString;
        });
        await _updateSetting('dndStart', timeString);
      } else {
        setState(() {
          _dndEnd = timeString;
        });
        await _updateSetting('dndEnd', timeString);
      }
    }
  }

  void _saveSettings() {
    // This method is now handled by individual setting updates
    // But we can keep it for the save button if needed
    _showSuccessSnackBar('Toate setările sunt deja salvate automat!');
  }

  /// Show success message
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
       
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea setărilor de notificare',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadNotificationSettings,
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: 16),
            Text(
              'Acces restricționat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Doar groomer-ii șefi pot configura setările de notificare pentru salon.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Înapoi'),
            ),
          ],
        ),
      ),
    );
  }
}