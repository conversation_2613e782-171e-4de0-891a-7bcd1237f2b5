import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../models/salon.dart';
import '../../services/salon_service.dart';
import '../../config/theme/app_theme.dart';
import '../../widgets/address_selection/location_selection_button.dart';
import '../../widgets/common/standard_form_field.dart';

/// Screen for creating a new salon
class CreateSalonScreen extends StatefulWidget {
  const CreateSalonScreen({super.key});

  @override
  State<CreateSalonScreen> createState() => _CreateSalonScreenState();
}

class _CreateSalonScreenState extends State<CreateSalonScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();

  bool _isLoading = false;
  List<String> _validationErrors = [];

  // Location selection
  LatLng? _selectedLocation;
  String? _selectedAddress;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Creează salon',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    _buildHeader(),
                    SizedBox(height: 32),

                    // Validation errors
                    if (_validationErrors.isNotEmpty) ...[
                      _buildErrorCard(),
                      SizedBox(height: 16),
                    ],

                    // Form fields
                    _buildFormFields(),
                  ],
                ),
              ),
            ),
          ),

          // Fixed bottom section with create button
          _buildBottomSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                Icons.add_business,
                color: Theme.of(context).colorScheme.primary,
                size: 32,
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Noul tău salon',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Completează informațiile de mai jos pentru a-ți crea salonul',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.taupe,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Erori de validare:',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ..._validationErrors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: 28, bottom: 4),
              child: Text(
                '• $error',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Salon name
        _buildTextField(
          controller: _nameController,
          label: 'Numele salonului *',
          hint: 'ex. Salon Animalia',
          icon: Icons.business,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Numele salonului este obligatoriu';
            }
            if (value.trim().length < 3) {
              return 'Numele trebuie să aibă cel puțin 3 caractere';
            }
            return null;
          },
        ),
        SizedBox(height: 16),

        // Description
        _buildTextField(
          controller: _descriptionController,
          label: 'Descriere (opțional)',
          hint: 'Scurtă descriere a salonului...',
          icon: Icons.description,
          maxLines: 3,
        ),
        SizedBox(height: 16),

        // Location selection
        LocationSelectionButton(
          selectedAddress: _selectedAddress,
          selectedLocation: _selectedLocation,
          label: 'Locația salonului',
          hint: 'Selectați adresa salonului pe hartă',
          isRequired: true,
          showReminderDisclaimer: true,
          onLocationSelected: (location, address) {
            setState(() {
              _selectedLocation = location;
              _selectedAddress = address;
            });
          },
          validator: (value) {
            if (_selectedLocation == null || _selectedAddress == null || _selectedAddress!.trim().isEmpty) {
              return 'Locația salonului este obligatorie';
            }
            return null;
          },
        ),
        SizedBox(height: 16),

        // Phone
        StandardFormField(
          controller: _phoneController,
          labelText: 'Telefon (opțional)',
          hintText: '+40 XXX XXX XXX',
          prefixIcon: Icons.phone,
          keyboardType: TextInputType.phone,
          type: StandardFormFieldType.phone,
          isRequired: false,
        ),
        SizedBox(height: 16),

        // Email
        _buildTextField(
          controller: _emailController,
          label: 'Email (opțional)',
          hint: '<EMAIL>',
          icon: Icons.email,
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return StandardFormField(
      controller: controller,
      labelText: label,
      hintText: hint,
      prefixIcon: icon,
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
      fillColor: Colors.white,
    );
  }

  Widget _buildBottomSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _createSalon,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Creează salon',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Future<void> _createSalon() async {
    // Clear previous errors
    setState(() => _validationErrors = []);

    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate location selection
    if (_selectedLocation == null || _selectedAddress == null || _selectedAddress!.trim().isEmpty) {
      setState(() => _validationErrors = ['Locația salonului este obligatorie']);
      return;
    }

    // Create request
    final request = CreateSalonRequest(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      address: _selectedAddress!,
      phone: _phoneController.text.trim().isEmpty
          ? null
          : _phoneController.text.trim(),
      email: _emailController.text.trim().isEmpty
          ? null
          : _emailController.text.trim(),
    );

    // Additional validation
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      setState(() => _validationErrors = validationErrors);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await SalonService.createSalon(request);

      if (response.success) {
        if (mounted) {
          // Navigate back immediately and let the parent handle the success message
          Navigator.of(context).pop(true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Eroare la crearea salonului'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
