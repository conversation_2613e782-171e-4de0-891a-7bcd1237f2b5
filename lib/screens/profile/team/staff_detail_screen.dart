import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../services/staff_service.dart';
import '../../../services/staff_working_hours_service.dart';
import '../../../config/theme/app_theme.dart';
import '../../../widgets/dialogs/edit_staff_dialog.dart';
import '../../../widgets/schedule/schedule_management_widget.dart';
import '../../../models/staff_working_hours_settings.dart';
import '../../../models/working_hours_settings.dart';
import '../../../models/user_role.dart';
import '../../../providers/calendar_provider.dart';
import '../../../services/ui_notification_service.dart';

class StaffDetailScreen extends StatefulWidget {
  final StaffResponse staff;

  const StaffDetailScreen({
    super.key,
    required this.staff,
  });

  @override
  State<StaffDetailScreen> createState() => _StaffDetailScreenState();
}

class _StaffDetailScreenState extends State<StaffDetailScreen> with TickerProviderStateMixin {
  late StaffResponse _currentStaff;
  late TabController _tabController;
  bool _isLoading = false;

  // Schedule tab state
  StaffWorkingHoursSettings? _currentSettings;
  bool _isScheduleLoading = true;
  bool _isSaving = false;
  String? _scheduleError;

  @override
  void initState() {
    super.initState();
    _currentStaff = widget.staff;
    _tabController = TabController(length: 2, vsync: this);

    // Load schedule data when tab is accessed
    _tabController.addListener(() {
      if (_tabController.index == 1 && _currentSettings == null) {
        _loadStaffWorkingHours();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              floating: false,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  _currentStaff.groomerRole.displayName,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: 40), // Account for status bar
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                            border: Border.all(color: Theme.of(context).colorScheme.onPrimary, width: 3),
                          ),
                          child: Center(
                            child: Text(
                              _currentStaff.name.split(' ').map((n) => n.isNotEmpty ? n[0] : '').join(''),
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            _currentStaff.groomerRole.displayName,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SliverPersistentHeader(
              delegate: _SliverTabBarDelegate(
                TabBar(
                  controller: _tabController,
                  labelColor: Theme.of(context).colorScheme.primary,
                  unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
                  indicatorColor: Theme.of(context).colorScheme.primary,
                  indicatorWeight: 3,
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 16,
                  ),
                  tabs: const [
                    Tab(
                      icon: Icon(Icons.info_outline),
                      text: 'Info',
                    ),
                    Tab(
                      icon: Icon(Icons.schedule),
                      text: 'Program',
                    ),
                  ],
                ),
              ),
              pinned: true,
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildInfoTab(),
            _buildScheduleTab(),
          ],
        ),
      ),
    );
  }

  /// Build the Info tab content
  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection(),
          SizedBox(height: 24),
          _buildPermissionsSection(),
          SizedBox(height: 24),
          _buildRoleManagementSection(),
          SizedBox(height: 24),
          if (_currentStaff.specialties.isNotEmpty) ...[
            _buildSpecialtiesSection(),
            SizedBox(height: 24),
          ],
          if (_currentStaff.notes != null && _currentStaff.notes!.isNotEmpty) ...[
            _buildNotesSection(),
            SizedBox(height: 24),
          ],
          _buildStatsSection(),
        ],
      ),
    );
  }

  /// Build the Schedule tab content
  Widget _buildScheduleTab() {
    if (_isScheduleLoading) {
      return Center(
        child: CircularProgressIndicator(color: Theme.of(context).colorScheme.primary),
      );
    }

    if (_scheduleError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea programului',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              _scheduleError!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadStaffWorkingHours,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      );
    }

    return ScheduleManagementWidget(
      currentSettings: _convertToWorkingHoursSettings(),
      isLoading: false,
      isSaving: _isSaving,
      error: null,
      onRefresh: _loadStaffWorkingHours,
      onTemplateSelected: _applyScheduleTemplate,
      onDayScheduleUpdate: _updateDaySchedule,
      onCustomClosureAdd: _addCustomClosure,
      onCustomClosureRemove: _removeCustomClosure,
      title: 'Program ${_currentStaff.name}',
      showCurrentStatus: true,
      showTemplates: true,
      showHolidays: true,
      showCustomClosures: true,
      showSaveButton: false, // Disable save button for staff detail screen
      hasUnsavedChanges: false,
      onSave: null,
      businessHours: null, // Could be added later if needed
    );
  }

  /// Build role management section with edit and status controls
  Widget _buildRoleManagementSection() {
    final isChiefGroomer = _currentStaff.groomerRole == GroomerRole.chiefGroomer;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Gestionare membru echipă',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),

            // Show restriction message for chief groomer
            if (isChiefGroomer) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.amber.shade700,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Groomer Șef nu poate fi editat. Pentru modificări, contactați administratorul.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.amber.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
            ],

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (_isLoading || isChiefGroomer) ? null : _showEditDialog,
                    icon:  Icon(Icons.edit),
                    label: Text('Editează membru'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isChiefGroomer ? Colors.grey : Theme.of(context).colorScheme.primary,
                      
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (_isLoading || isChiefGroomer) ? null : _toggleStatus,
                    icon: Icon(_currentStaff.isActive ? Icons.pause : Icons.play_arrow),
                    label: Text(_currentStaff.isActive ? 'Dezactivează' : 'Activează'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isChiefGroomer
                          ? Colors.grey
                          : (_currentStaff.isActive ? Colors.orange : Colors.green),
                      
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Delete button (separate row for emphasis)
            if (!isChiefGroomer) ...[
              SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _showDeleteConfirmation,
                  icon:  Icon(Icons.delete_outline),
                  label: Text('Șterge membru din echipă'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: BorderSide(color: Colors.red),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informații de contact',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildInfoRow(Icons.person, 'Nume', _currentStaff.name),
            SizedBox(height: 12),
            if (_currentStaff.nickname != null && _currentStaff.nickname!.isNotEmpty) ...[
              _buildInfoRow(Icons.badge, 'Poreclă', _currentStaff.nickname!),
              SizedBox(height: 12),
            ],
            if (_currentStaff.phone != null) ...[
              _buildInfoRow(Icons.phone, 'Telefon', _currentStaff.formattedPhone ?? _currentStaff.phone!),
              SizedBox(height: 12),
            ],
            if (_currentStaff.email != null) ...[
              _buildInfoRow(Icons.email, 'Email', _currentStaff.email!),
              SizedBox(height: 12),
            ],
            _buildInfoRow(
              _currentStaff.isActive ? Icons.check_circle : Icons.cancel,
              'Status',
              _currentStaff.isActive ? 'Activ' : 'Inactiv',
              valueColor: _currentStaff.isActive ? Colors.green : Colors.red,
            ),
            SizedBox(height: 12),
            _buildInfoRow(
              Icons.calendar_today,
              'Membru din',
              _formatDate(_currentStaff.joinedAt),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Permisiuni și rol',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            _buildInfoRow(
              Icons.work,
              'Rol în echipă',
              _currentStaff.groomerRole.displayName,
            ),
            SizedBox(height: 8),
            Text(
              _currentStaff.groomerRole.description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
            SizedBox(height: 16),
            _buildInfoRow(
              Icons.security,
              'Acces date clienți',
              _currentStaff.clientDataPermission.displayName,
            ),
            SizedBox(height: 8),
            Text(
              _currentStaff.clientDataPermission.description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialtiesSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Specializări',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _currentStaff.specialties.map((specialty) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)),
                ),
                child: Text(
                  specialty,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Note',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                _currentStaff.notes!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistici',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Experiență',
                    '${_currentStaff.experience} ani',
                    Icons.timeline,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Rating',
                    _currentStaff.rating > 0 ? '${_currentStaff.rating.toStringAsFixed(1)}/5' : 'N/A',
                    Icons.star,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Programări',
                    '${_currentStaff.totalAppointments}',
                    Icons.event,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, {Color? valueColor}) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: valueColor ?? Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Theme.of(context).colorScheme.primary),
        SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'ianuarie', 'februarie', 'martie', 'aprilie', 'mai', 'iunie',
      'iulie', 'august', 'septembrie', 'octombrie', 'noiembrie', 'decembrie'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _showEditDialog() {
    showDialog(
      context: context,
      builder: (context) => EditStaffDialog(
        staff: _currentStaff,
        onStaffUpdated: (updatedStaff) {
          // Update the current staff data and refresh UI
          setState(() {
            _currentStaff = updatedStaff;
          });
          _refreshStaffData();
        },
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.red.shade600,
              size: 28,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Șterge membru',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ești sigur că vrei să ștergi pe ${_currentStaff.name} din echipă?',
              style: TextStyle(
                fontSize: 16,
                height: 1.4,
              ),
            ),
            SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.red.shade200,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Această acțiune nu poate fi anulată. Membrul va fi eliminat definitiv din echipă.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade700,
                        height: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Anulează',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteStaffMember();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Șterge',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await StaffService.toggleStaffStatusInCurrentSalon(_currentStaff.id);

      if (response.success && response.data != null) {
        setState(() {
          _currentStaff = response.data!;
        });

        final newStatus = _currentStaff.isActive ? 'activat' : 'dezactivat';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_currentStaff.name} a fost $newStatus cu succes'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Nu s-a putut schimba statusul'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Eroare: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteStaffMember() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await StaffService.removeStaffFromCurrentSalon(
        _currentStaff.id,
      );

      if (response.success) {
        // Show success notification
        UINotificationService.showSuccess(
          context: context,
          title: 'Membru șters',
          message: '${_currentStaff.name} a fost eliminat din echipă cu succes',
        );

        // Navigate back to team management screen
        Navigator.of(context).pop();
      } else {
        UINotificationService.showError(
          context: context,
          title: 'Eroare ștergere',
          message: response.error ?? 'Nu s-a putut șterge membrul din echipă',
          actionLabel: 'Încearcă din nou',
          onActionPressed: () => _deleteStaffMember(),
        );
      }
    } catch (e) {
      UINotificationService.showError(
        context: context,
        title: 'Eroare ștergere',
        message: 'A apărut o eroare neașteptată: $e',
        actionLabel: 'Încearcă din nou',
        onActionPressed: () => _deleteStaffMember(),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshStaffData() async {
    // In a real implementation, you might want to fetch updated staff data
    // For now, we'll just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Datele au fost actualizate cu succes'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  /// Load staff working hours settings from backend
  Future<void> _loadStaffWorkingHours() async {
    setState(() {
      _isScheduleLoading = true;
      _scheduleError = null;
    });

    try {
      final response = await StaffWorkingHoursService.getStaffWorkingHours(_currentStaff.id);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _isScheduleLoading = false;
        });
      } else {
        setState(() {
          _scheduleError = response.error ?? 'Failed to load staff working hours';
          _isScheduleLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _scheduleError = 'Error loading staff working hours: $e';
        _isScheduleLoading = false;
      });
    }
  }

  /// Apply schedule template
  Future<void> _applyScheduleTemplate(Map<String, DaySchedule> template) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: template,
        holidays: _currentSettings?.holidays ?? [],
        customClosures: _currentSettings?.customClosures ?? [],
      );

      final response = await StaffWorkingHoursService.updateStaffWorkingHours(_currentStaff.id, request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });

        // Trigger calendar update for staff schedule changes
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: _currentStaff.id,
            salonScheduleChanged: false,
            customClosuresChanged: false,
          );
          debugPrint('🔄 Staff schedule template applied - calendar updated for staff: ${_currentStaff.id}');
        }

        UINotificationService.showSuccess(
          context: context,
          title: 'Șablon aplicat!',
          message: 'Șablonul de program a fost aplicat cu succes',
        );
      } else {
        UINotificationService.showError(
          context: context,
          title: 'Eroare șablon',
          message: response.error ?? 'Nu s-a putut aplica șablonul de program',
          actionLabel: 'Încearcă din nou',
        );
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la aplicarea șablonului: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Update day schedule
  Future<void> _updateDaySchedule(String dayOfWeek, DaySchedule newSchedule) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await StaffWorkingHoursService.updateStaffDaySchedule(
        staffId: _currentStaff.id,
        dayOfWeek: dayOfWeek,
        schedule: newSchedule,
        currentSettings: _currentSettings,
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });

        // Trigger calendar update for staff day schedule changes
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: _currentStaff.id,
            salonScheduleChanged: false,
            customClosuresChanged: false,
          );
          debugPrint('🔄 Staff day schedule updated - calendar updated for staff: ${_currentStaff.id}, day: $dayOfWeek');
        }

        _showSuccessSnackBar('Programul a fost actualizat cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la actualizarea programului');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea programului: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Add custom closure
  Future<void> _addCustomClosure(CustomClosure closure) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await StaffWorkingHoursService.addStaffCustomClosure(
        _currentStaff.id,
        closure,
        currentSettings: _currentSettings,
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar('Închiderea personalizată a fost adăugată cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la adăugarea închiderii');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la adăugarea închiderii: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Remove custom closure
  Future<void> _removeCustomClosure(DateTime date) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await StaffWorkingHoursService.removeStaffCustomClosure(
        _currentStaff.id,
        date,
        currentSettings: _currentSettings,
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar('Închiderea a fost ștearsă cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la ștergerea închiderii');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la ștergerea închiderii: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Convert StaffWorkingHoursSettings to WorkingHoursSettings for the widget
  WorkingHoursSettings? _convertToWorkingHoursSettings() {
    if (_currentSettings == null) return null;

    return WorkingHoursSettings(
      salonId: _currentSettings!.salonId,
      weeklySchedule: _currentSettings!.weeklySchedule,
      holidays: _currentSettings!.holidays,
      customClosures: _currentSettings!.customClosures,
      updatedAt: _currentSettings!.updatedAt,
    );
  }

  /// Show success message
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

/// Custom delegate for the persistent tab bar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppColors.appBackground,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
