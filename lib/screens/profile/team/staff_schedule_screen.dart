import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/staff_working_hours_settings.dart';
import '../../../models/working_hours_settings.dart';
import '../../../providers/calendar_provider.dart';
import '../../../services/staff_working_hours_service.dart';
import '../../../services/working_hours_service.dart';
import '../../../services/staff_validation_error_service.dart';
import '../../../config/theme/app_theme.dart';
import '../../../widgets/schedule/schedule_management_widget.dart';
import '../../../services/ui_notification_service.dart';

class StaffScheduleScreen extends StatefulWidget {
  final String staffId;
  final String staffName;

  const StaffScheduleScreen({
    Key? key,
    required this.staffId,
    required this.staffName,
  }) : super(key: key);

  @override
  State<StaffScheduleScreen> createState() => _StaffScheduleScreenState();
}

class _StaffScheduleScreenState extends State<StaffScheduleScreen> {
  // Backend integration state
  StaffWorkingHoursSettings? _currentSettings;
  StaffWorkingHoursSettings? _originalSettings; // Track original state for change detection
  WorkingHoursSettings? _businessHours; // Business hours for context display
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadStaffWorkingHours();
    _loadBusinessHours();

    // Test error parsing in debug mode
    StaffValidationErrorService.testErrorParsing();
  }

  /// Load business hours for context display
  Future<void> _loadBusinessHours() async {
    try {
      final response = await WorkingHoursService.getWorkingHours();
      if (response.success && response.data != null) {
        setState(() {
          _businessHours = response.data!;
        });
      }
    } catch (e) {
      // Silently fail - business hours are optional for context
      debugPrint('Failed to load business hours for context: $e');
    }
  }

  /// Load staff working hours settings from backend
  Future<void> _loadStaffWorkingHours() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await StaffWorkingHoursService.getStaffWorkingHours(widget.staffId);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _originalSettings = response.data!; // Store original for change detection
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load staff working hours';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading staff working hours: $e';
        _isLoading = false;
      });
    }
  }

  /// Check if there are unsaved changes
  bool _hasUnsavedChanges() {
    if (_currentSettings == null || _originalSettings == null) return false;

    // Compare weekly schedules
    if (_currentSettings!.weeklySchedule.length != _originalSettings!.weeklySchedule.length) {
      return true;
    }

    for (final entry in _currentSettings!.weeklySchedule.entries) {
      final originalSchedule = _originalSettings!.weeklySchedule[entry.key];
      if (originalSchedule == null || !_schedulesEqual(entry.value, originalSchedule)) {
        return true;
      }
    }

    // Compare custom closures
    if (_currentSettings!.customClosures.length != _originalSettings!.customClosures.length) {
      return true;
    }

    return false;
  }

  /// Compare two day schedules for equality
  bool _schedulesEqual(DaySchedule a, DaySchedule b) {
    return a.isWorkingDay == b.isWorkingDay &&
           a.startTime == b.startTime &&
           a.endTime == b.endTime &&
           a.breakStart == b.breakStart &&
           a.breakEnd == b.breakEnd;
  }

  /// Save current changes
  Future<void> _saveChanges() async {
    if (_currentSettings == null) return;

    setState(() {
      _isSaving = true;
      _error = null; // Clear any previous errors when starting new operation
    });

    try {
      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: _currentSettings!.weeklySchedule,
        holidays: _currentSettings!.holidays,
        customClosures: _currentSettings!.customClosures,
      );
      debugPrint('Sending request to update staff working hours: ${request.toJson()}');

      final response = await StaffWorkingHoursService.updateStaffWorkingHours(widget.staffId, request);

      if (response.success && response.data != null) {
        debugPrint('✅ Staff schedule saved successfully');
        debugPrint('   Staff ID: ${response.data!.staffId}');
        debugPrint('   Weekly schedule: ${response.data!.weeklySchedule}');
        debugPrint('   Holidays: ${response.data!.holidays}');
        debugPrint('   Custom closures: ${response.data!.customClosures}');

        setState(() {
          _currentSettings = response.data!;
          _originalSettings = response.data!; // Update original to reflect saved state
          _error = null; // Clear any previous errors
        });

        // Trigger calendar update for staff schedule changes
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: widget.staffId,
            salonScheduleChanged: false,
            customClosuresChanged: false,
          );
          debugPrint('🔄 Staff schedule saved - calendar updated for staff: ${widget.staffId}');
        }

        UINotificationService.showSuccess(
          context: context,
          title: 'Program salvat!',
          message: 'Programul a fost salvat cu succes și este acum activ',
        );
      } else {
        // Handle validation errors with beautiful dialog or set error state
        final errorMessage = response.error ?? 'Eroare la salvarea programului';
        debugPrint('🚨 Staff schedule save error: ${response.error}');
        debugPrint('🚨 Response success: ${response.success}');
        debugPrint('🚨 Response status code: ${response.statusCode}');
        debugPrint('🚨 Error message to handle: $errorMessage');
        _handleValidationError(errorMessage);
      }
    } catch (e) {
      final errorMessage = 'Eroare la salvarea programului: $e';
      _handleValidationError(errorMessage);
      debugPrint('🚨 Staff schedule save exception: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Apply schedule template
  Future<void> _applyScheduleTemplate(Map<String, DaySchedule> template) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: template,
        holidays: _currentSettings?.holidays ?? [],
        customClosures: _currentSettings?.customClosures ?? [],
      );

      final response = await StaffWorkingHoursService.updateStaffWorkingHours(widget.staffId, request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _originalSettings = response.data!; // Update original to reflect saved state
          _error = null; // Clear any previous errors
        });

        // Trigger calendar update for staff schedule changes
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: widget.staffId,
            salonScheduleChanged: false,
            customClosuresChanged: false,
          );
          debugPrint('🔄 Staff schedule template applied - calendar updated for staff: ${widget.staffId}');
        }

        UINotificationService.showSuccess(
          context: context,
          title: 'Șablon aplicat!',
          message: 'Șablonul de program a fost aplicat cu succes',
        );
      } else {
        // Handle validation errors with beautiful dialog or set error state
        final errorMessage = response.error ?? 'Eroare la aplicarea șablonului';
        _handleValidationError(errorMessage);
        debugPrint('🚨 Staff schedule template error: ${response.error}');
      }
    } catch (e) {
      final errorMessage = 'Eroare la aplicarea șablonului: $e';
      _handleValidationError(errorMessage);
      debugPrint('🚨 Staff schedule template exception: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Update day schedule
  Future<void> _updateDaySchedule(String dayOfWeek, DaySchedule newSchedule) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await StaffWorkingHoursService.updateStaffDaySchedule(
        staffId: widget.staffId,
        dayOfWeek: dayOfWeek,
        schedule: newSchedule,
        currentSettings: _currentSettings, // Pass current settings to avoid fetching stale data
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _originalSettings = response.data!; // Update original to reflect saved state
          _error = null; // Clear any previous errors
        });

        // Trigger calendar update for staff day schedule changes
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: widget.staffId,
            salonScheduleChanged: false,
            customClosuresChanged: false,
          );
          debugPrint('🔄 Staff day schedule updated - calendar updated for staff: ${widget.staffId}, day: $dayOfWeek');
        }

        UINotificationService.showSuccess(
          context: context,
          title: 'Program actualizat!',
          message: 'Programul zilei a fost actualizat cu succes',
        );
      } else {
        // Handle validation errors with beautiful dialog or set error state
        final errorMessage = response.error ?? 'Eroare la actualizarea programului';
        _handleValidationError(errorMessage);
        debugPrint('🚨 Staff day schedule update error: ${response.error}');
      }
    } catch (e) {
      final errorMessage = 'Eroare la actualizarea programului: $e';
      _handleValidationError(errorMessage);
      debugPrint('🚨 Staff day schedule update exception: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Add custom closure
  Future<void> _addCustomClosure(CustomClosure closure) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await StaffWorkingHoursService.addStaffCustomClosure(
        widget.staffId,
        closure,
        currentSettings: _currentSettings, // Pass current settings to avoid fetching stale data
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _originalSettings = response.data!; // Update original to reflect saved state
        });

        // Trigger calendar update for staff custom closure changes
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: widget.staffId,
            salonScheduleChanged: false,
            customClosuresChanged: true,
          );
          debugPrint('🔄 Staff custom closure added - calendar updated for staff: ${widget.staffId}');
        }

        _showSuccessSnackBar('Închiderea personalizată a fost adăugată cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la adăugarea închiderii');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la adăugarea închiderii: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Remove custom closure
  Future<void> _removeCustomClosure(DateTime date) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await StaffWorkingHoursService.removeStaffCustomClosure(
        widget.staffId,
        date,
        currentSettings: _currentSettings, // Pass current settings to avoid fetching stale data
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _originalSettings = response.data!; // Update original to reflect saved state
        });

        // Trigger calendar update for staff custom closure removal
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.handleScheduleUpdate(
            staffId: widget.staffId,
            salonScheduleChanged: false,
            customClosuresChanged: true,
          );
          debugPrint('🔄 Staff custom closure removed - calendar updated for staff: ${widget.staffId}');
        }

        _showSuccessSnackBar('Închiderea a fost ștearsă cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la ștergerea închiderii');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la ștergerea închiderii: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Convert StaffWorkingHoursSettings to WorkingHoursSettings for the widget
  WorkingHoursSettings? _convertToWorkingHoursSettings() {
    if (_currentSettings == null) return null;

    return WorkingHoursSettings(
      salonId: _currentSettings!.salonId,
      weeklySchedule: _currentSettings!.weeklySchedule,
      holidays: _currentSettings!.holidays,
      customClosures: _currentSettings!.customClosures,
      updatedAt: _currentSettings!.updatedAt,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appBackground,
      appBar: AppBar(
        title: Text(
          'Program ${widget.staffName}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.forestGreen,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // Debug button to test error display
          if (const bool.fromEnvironment('dart.vm.product') == false)
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: _testErrorDisplay,
              tooltip: 'Test Error',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStaffWorkingHours,
            tooltip: 'Reîncarcă',
          ),
        ],
      ),
      body: ScheduleManagementWidget(
        currentSettings: _convertToWorkingHoursSettings(),
        isLoading: _isLoading,
        isSaving: _isSaving,
        error: _error,
        onRefresh: _loadStaffWorkingHours,
        onTemplateSelected: _applyScheduleTemplate,
        onDayScheduleUpdate: _updateDaySchedule,
        onCustomClosureAdd: _addCustomClosure,
        onCustomClosureRemove: _removeCustomClosure,
        title: 'Program ${widget.staffName}',
        showCurrentStatus: true,
        showTemplates: true,
        showHolidays: true,
        showCustomClosures: true,
        showSaveButton: true,
        hasUnsavedChanges: _hasUnsavedChanges(),
        onSave: _saveChanges,
        businessHours: _businessHours,
      ),
      floatingActionButton: _convertToWorkingHoursSettings() != null && !_isSaving
          ? FloatingActionButton.extended(
              heroTag: "staff_schedule_save_fab",
              onPressed: () => _showSuccessSnackBar('Programul este salvat automat!'),
              backgroundColor: AppColors.forestGreen,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.check),
              label: const Text('Salvat'),
            )
          : null,
    );
  }

  /// Show success message
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.forestGreen,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Test error display (debug only)
  void _testErrorDisplay() {
    const testError = 'Staff schedule violates business hours constraints: Staff end time 21:00 on MONDAY is after business end time 17:00';
    _handleValidationError(testError);
    debugPrint('🧪 Test error triggered: $testError');
  }

  /// Handle validation errors by showing beautiful notification
  void _handleValidationError(String error) {
    debugPrint('🔍 Handling validation error: $error');

    // Use the new beautiful notification system
    UINotificationService.showScheduleError(
      context: context,
      errorMessage: error,
      businessHours: _businessHours,
      onRetry: () {
        debugPrint('🔍 Notification retry pressed');
        // Clear any existing error state
        setState(() {
          _error = null;
        });
      },
    );
  }
}
