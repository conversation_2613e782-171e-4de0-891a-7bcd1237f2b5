import 'package:animaliaproject/screens/profile/team/staff_detail_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../services/staff_service.dart';
import '../../../services/invitation_service.dart';
import '../../../models/salon_invitation.dart';
import '../../../config/theme/app_theme.dart';
import '../../../widgets/forms/add_staff_dialog.dart';

import '../../../providers/calendar_provider.dart';
import '../../pending_invitation_detail_screen.dart';

class TeamManagementScreen extends StatefulWidget {
  const TeamManagementScreen({Key? key}) : super(key: key);

  @override
  State<TeamManagementScreen> createState() => _TeamManagementScreenState();
}

class _TeamManagementScreenState extends State<TeamManagementScreen> {
  final GlobalKey<RefreshIndicatorState> _refreshKey = GlobalKey<RefreshIndicatorState>();
  CalendarProvider? _calendarProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store reference to calendar provider safely
    _calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
  }

  @override
  void dispose() {
    // Refresh calendar staff data when leaving team management screen
    // This ensures calendar shows updated nicknames
    _refreshCalendarStaffDataSafely();
    super.dispose();
  }

  Future<StaffListResponse?> _loadStaff() async {
    debugPrint('🔄 TeamManagementScreen: Loading staff data');

    try {
      final staffResponse = await StaffService.getCurrentSalonStaff(activeOnly: false);

      if (staffResponse.success && staffResponse.data != null) {
        final activeStaff = staffResponse.data!.activeStaff;

        // Also load pending invitations via InvitationService
        final inviteResponse = await InvitationService.getSentInvitations();
        List<PendingStaffInvitation> pendingInvitations = [];

        if (inviteResponse.success && inviteResponse.data != null) {
          pendingInvitations = inviteResponse.data!
              .map(_convertSalonInvitation)
              .toList();
        } else {
          debugPrint('   - Invitation error: ${inviteResponse.error}');
        }

        debugPrint('- Active Staff: ${activeStaff.length}' + '   - Pending Invitations: ${pendingInvitations.length}') ;

        return StaffListResponse(
          activeStaff: activeStaff,
          pendingStaff: pendingInvitations,
          totalActiveCount: activeStaff.length,
          totalPendingCount: pendingInvitations.length,
          activeCount: activeStaff.where((s) => s.isActive).length,
          inactiveCount: activeStaff.where((s) => !s.isActive).length,
        );
      } else {
        debugPrint('   - Error: ${staffResponse.error}');
      }
      return null;
    } catch (e) {
      debugPrint('❌ TeamManagementScreen: Error loading staff: $e');
      return null;
    }
  }

  Future<void> _refreshStaff() async {
    setState(() {
      // This will trigger a rebuild and reload the data
    });
  }

  /// Refresh both team and calendar staff data
  void _refreshAllStaffData() {
    _refreshStaff();
    _refreshCalendarStaffData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appBackground,
      appBar: AppBar(
        title: const Text(
          'Gestionare Echipă',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.forestGreen,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: FutureBuilder<StaffListResponse?>(
        future: _loadStaff(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.forestGreen),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Eroare la încărcarea echipei',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${snapshot.error}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _refreshStaff(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.forestGreen,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Încearcă din nou'),
                  ),
                ],
              ),
            );
          }

          final staffList = snapshot.data;

          if (staffList == null ||
              (staffList.activeStaff.isEmpty && staffList.pendingStaff.isEmpty)) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.group_off,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Niciun membru în echipă',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Adaugă primul membru al echipei',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showAddStaffDialog(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.forestGreen,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Icon(Icons.add),
                  ),
                ],
              ),
            );
          }

          final activeStaff = staffList.activeStaff;
          final pendingInvitations = staffList.pendingStaff;

          return Column(
            children: [
              // Header with add button
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${activeStaff.length} membri în echipă',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),

                    ElevatedButton(
                      onPressed: () => _showAddStaffDialog(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.forestGreen,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Icon(Icons.add),
                    ),
                  ],
                ),
              ),
              // Team members list
              Expanded(
                child: RefreshIndicator(
                  key: _refreshKey,
                  onRefresh: _refreshStaff,
                  color: AppColors.forestGreen,
                  child: ListView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    children: [
                      for (final staff in activeStaff) _buildStaffCard(staff),
                      if (pendingInvitations.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        Text(
                          'Invitații în așteptare',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        for (final invitation in pendingInvitations)
                          _buildPendingInvitationCard(invitation),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStaffCard(StaffResponse staff) {
    return GestureDetector(
      onTap: () => _navigateToStaffDetail(staff),
      child: Card(
        key: ValueKey(staff.id),
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Staff member avatar
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: staff.isActive ? AppColors.forestGreen : Colors.grey,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        staff.name.split(' ').map((n) => n.isNotEmpty ? n[0] : '').join(''),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Builder(
                                builder: (context) {
                                  // Add comprehensive logging for nickname display debugging
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        staff.displayName,
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: staff.isActive ? AppColors.forestGreen : Colors.grey,
                                        ),
                                      ),
                                      if (staff.nickname != null && staff.nickname!.isNotEmpty && staff.nickname != staff.name) ...[
                                        const SizedBox(height: 2),
                                        Text(
                                          '(${staff.name})',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey.shade600,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ],
                                    ],
                                  );
                                },
                              ),
                            ),
                            // Role badge
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: staff.groomerRole.hasManagementAccess
                                    ? AppColors.forestGreen.withValues(alpha: 0.1)
                                    : Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: staff.groomerRole.hasManagementAccess
                                      ? AppColors.forestGreen.withValues(alpha: 0.3)
                                      : Colors.blue.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                staff.groomerRole.displayName,
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: staff.groomerRole.hasManagementAccess
                                      ? AppColors.forestGreen
                                      : Colors.blue,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        if (staff.phone != null && staff.phone!.isNotEmpty) ...[
                          Row(
                            children: [
                              Icon(Icons.phone, size: 16, color: Colors.grey.shade600),
                              const SizedBox(width: 4),
                              Text(
                                staff.formattedPhone ?? staff.phone!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                        // Status indicator
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: staff.isActive ? Colors.green : Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              staff.isActive ? 'Activ' : 'Inactiv',
                              style: TextStyle(
                                fontSize: 12,
                                color: staff.isActive ? Colors.green : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            // Visual indicator for tap action
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.forestGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.forestGreen.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Text(
                                    'Detalii',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.forestGreen,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 12,
                                    color: AppColors.forestGreen,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (staff.specialties.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'Specializări:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: staff.specialties.map((specialty) =>
                    _buildSpecialtyChip(specialty)
                  ).toList(),
                ),
              ],
              if (staff.notes != null && staff.notes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          staff.notes!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade700,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpecialtyChip(String specialty) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.forestGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.forestGreen.withValues(alpha: 0.3)),
      ),
      child: Text(
        specialty,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: AppColors.forestGreen,
        ),
      ),
    );
  }

  void _navigateToStaffDetail(StaffResponse staff) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StaffDetailScreen(staff: staff),
      ),
    ).then((_) {
      // Refresh the list when returning from detail screen
      _refreshStaff();
    });
  }

  void _navigateToPendingInvitationDetail(PendingStaffInvitation invitation) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PendingInvitationDetailScreen(invitation: invitation),
      ),
    ).then((_) {
      // Refresh the list when returning from detail screen
      _refreshStaff();
    });
  }

  Widget _buildPendingInvitationCard(PendingStaffInvitation invitation) {
    return GestureDetector(
      onTap: () => _navigateToPendingInvitationDetail(invitation),
      child: Card(
        key: ValueKey(invitation.invitationId),
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Pending invitation avatar
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300, width: 2),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.schedule,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Builder(
                              builder: (context) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      invitation.displayName,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange,
                                      ),
                                    ),
                                    if (invitation.nickname != null && invitation.nickname!.isNotEmpty) ...[
                                      const SizedBox(height: 2),
                                      Text(
                                        '(${invitation.formattedPhoneNumber})',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade600,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    ],
                                  ],
                                );
                              },
                            ),
                          ),
                          // Status badge
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.orange.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Text(
                              invitation.status,
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      // Role badge
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: invitation.groomerRole.hasManagementAccess
                              ? AppColors.forestGreen.withValues(alpha: 0.1)
                              : Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: invitation.groomerRole.hasManagementAccess
                                ? AppColors.forestGreen.withValues(alpha: 0.3)
                                : Colors.blue.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          invitation.groomerRole.displayName,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: invitation.groomerRole.hasManagementAccess
                                ? AppColors.forestGreen
                                : Colors.blue,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Expiry info
                      Row(
                        children: [
                          Icon(
                            invitation.isValid ? Icons.access_time : Icons.error_outline,
                            size: 14,
                            color: invitation.isValid ? Colors.grey.shade600 : Colors.red,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            invitation.isValid
                                ? 'Expiră ${_formatDate(invitation.expiresAt)}'
                                : 'Expirat',
                            style: TextStyle(
                              fontSize: 12,
                              color: invitation.isValid ? Colors.grey.shade600 : Colors.red,
                              fontWeight: invitation.isValid ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'resend') {
                      _resendInvitation(invitation);
                    } else if (value == 'cancel') {
                      _showCancelInvitationConfirmation(invitation);
                    }
                  },
                  itemBuilder: (context) => [
                    if (invitation.isValid) ...[
                      const PopupMenuItem(
                        value: 'resend',
                        child: Row(
                          children: [
                            Icon(Icons.send, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('Retrimite invitația'),
                          ],
                        ),
                      ),
                    ],
                    const PopupMenuItem(
                      value: 'cancel',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Anulează invitația'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Invitation details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.person, size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      Text(
                        'Invitat de: ${invitation.invitedBy}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      Text(
                        'Invitat la: ${_formatDate(invitation.invitedAt)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  void _showAddStaffDialog() {
    showDialog(
      context: context,
      builder: (context) => AddStaffDialog(
        onSuccess: _refreshAllStaffData,
      ),
    );
  }






  Future<void> _resendInvitation(PendingStaffInvitation invitation) async {
    try {
      final response = await StaffService.resendInvitationInCurrentSalon(invitation.invitationId);

      if (response.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invitația pentru ${invitation.phoneNumber} a fost retrimisă cu succes'),
            backgroundColor: AppColors.forestGreen,
          ),
        );
        _refreshStaff();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Nu s-a putut retrimite invitația'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Eroare: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showCancelInvitationConfirmation(PendingStaffInvitation invitation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Anulează Invitația'),
        content: Text('Ești sigur că vrei să anulezi invitația pentru ${invitation.phoneNumber}?\n\nAceastă acțiune nu poate fi anulată.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Nu'),
          ),
          ElevatedButton(
            onPressed: () => _cancelInvitation(invitation),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Da, anulează'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelInvitation(PendingStaffInvitation invitation) async {
    Navigator.pop(context); // Close dialog

    try {
      final response = await StaffService.cancelInvitationInCurrentSalon(invitation.invitationId);

      if (response.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invitația pentru ${invitation.phoneNumber} a fost anulată cu succes'),
            backgroundColor: AppColors.forestGreen,
          ),
        );
        _refreshStaff();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Nu s-a putut anula invitația'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Eroare: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'ianuarie', 'februarie', 'martie', 'aprilie', 'mai', 'iunie',
      'iulie', 'august', 'septembrie', 'octombrie', 'noiembrie', 'decembrie'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }



  PendingStaffInvitation _convertSalonInvitation(SalonInvitation invitation) {
    return PendingStaffInvitation(
      invitationId: invitation.id,
      phoneNumber: invitation.invitedUserPhone,
      nickname: null,
      groomerRole: invitation.proposedRole,
      clientDataPermission: invitation.proposedClientDataPermission,
      status: invitation.status.value,
      message: invitation.message,
      invitedBy: invitation.invitedByName,
      invitedAt: invitation.createdAt,
      expiresAt: invitation.expiresAt,
      isExpired: invitation.isExpired,
    );
  }

  /// Refresh calendar staff data when staff information is updated
  void _refreshCalendarStaffData() {
    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      debugPrint('🔄 TeamManagementScreen: Refreshing calendar staff data');
      calendarProvider.refreshStaffData();
    } catch (e) {
      debugPrint('❌ TeamManagementScreen: Error refreshing calendar staff data: $e');
    }
  }

  /// Safely refresh calendar staff data (for use in dispose)
  void _refreshCalendarStaffDataSafely() {
    try {
      if (_calendarProvider != null) {
        debugPrint('🔄 TeamManagementScreen: Safely refreshing calendar staff data');
        _calendarProvider!.refreshStaffData();
      } else {
        debugPrint('⚠️ TeamManagementScreen: Calendar provider not available for refresh');
      }
    } catch (e) {
      debugPrint('❌ TeamManagementScreen: Error safely refreshing calendar staff data: $e');
    }
  }
}