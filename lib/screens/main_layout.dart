import 'package:animaliaproject/screens/profile/settings/profile_screen.dart';
import 'package:animaliaproject/screens/reports/sales_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/theme/app_theme.dart';
import '../providers/role_provider.dart';
import '../providers/theme_provider.dart';
import '../services/feature_toggle_service.dart';
import 'appointments/calendar_screen.dart';
import 'clients/clients_screen.dart';
import 'notifications_screen.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _currentIndex = 0;
  bool _sellScreenEnabled = false;
  bool? _previousHasSalonAssociation; // Track previous salon association state

  @override
  void initState() {
    super.initState();
    _loadFeatureToggles();
  }

  Future<void> _loadFeatureToggles() async {
    final enabled = await FeatureToggleService.isSellScreenEnabled();
    if (mounted) {
      setState(() {
        _sellScreenEnabled = enabled;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        final availableScreens = _getAvailableScreens(roleProvider);
        final availableNavItems = _getAvailableNavItems(roleProvider);

        // Handle salon association changes
        final currentHasSalonAssociation = roleProvider.hasSalonAssociation;

        // Check if user just gained salon association (completed onboarding)
        if (_previousHasSalonAssociation == false && currentHasSalonAssociation == true) {
          debugPrint('🔄 MainLayout: User just completed onboarding, switching to Calendar tab');
          _currentIndex = 0; // Calendar is always first when available
        }

        // Update the previous state for next comparison
        _previousHasSalonAssociation = currentHasSalonAssociation;

        if (!currentHasSalonAssociation) {
          debugPrint('🔍 MainLayout: User has no salon association - forcing to Profile tab');
          // Find the profile tab index (it's always the last tab)
          final profileIndex = availableScreens.length - 1;
          _currentIndex = profileIndex;
          debugPrint('🔍 MainLayout: Set current index to Profile tab: $profileIndex');
        } else {
          debugPrint('✅ MainLayout: User has salon association - normal navigation');
          // Ensure current index is valid for available screens
          if (_currentIndex >= availableScreens.length) {
            _currentIndex = 0;
          }
        }

        return Scaffold(
          body: IndexedStack(
            index: _currentIndex,
            children: availableScreens,
          ),
          // Hide bottom navigation completely during onboarding
          bottomNavigationBar: roleProvider.hasSalonAssociation
            ? BottomNavigationBar(
                currentIndex: _currentIndex,
                onTap: (index) {
                  setState(() => _currentIndex = index);
                },
                type: BottomNavigationBarType.fixed,
                // Let Material 3 handle the colors automatically
                // backgroundColor, selectedItemColor, unselectedItemColor will be set by theme
                items: availableNavItems,
              )
            : null, // Completely hide bottom navigation during onboarding
        );
      },
    );
  }

  List<Widget> _getAvailableScreens(RoleProvider roleProvider) {
    final List<Widget> screens = [];

    // Calendar is only available if user has salon association
    if (roleProvider.hasSalonAssociation) {
      screens.add(const CalendarScreen());
    }

    // Clients screen - requires client data access AND salon association
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      screens.add(const ClientsScreen());
    }

    // Sales screen - requires management access, salon association and toggle
    if (_sellScreenEnabled &&
        roleProvider.hasSalonAssociation &&
        roleProvider.canViewSalesData()) {
      screens.add(const SalesScreen());
    }

    // Notifications is always available
    screens.add(const NotificationsScreen());

    // Profile is always available
    screens.add(const ProfileScreen());
    return screens;
  }

  List<BottomNavigationBarItem> _getAvailableNavItems(RoleProvider roleProvider) {
    final List<BottomNavigationBarItem> items = [];

    // Calendar is only available if user has salon association
    if (roleProvider.hasSalonAssociation) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.calendar_today),
        label: 'Calendar',
      ));
    }

    // Clients screen - requires client data access AND salon association
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.people),
        label: 'Clienti',
      ));
    }

    // Sales screen - requires management access, salon association and toggle
    if (_sellScreenEnabled &&
        roleProvider.hasSalonAssociation &&
        roleProvider.canViewSalesData()) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.shopping_cart),
        label: 'Vanzari',
      ));
    }

    // Notifications is always available
    items.add(const BottomNavigationBarItem(
      icon: Icon(Icons.notifications),
      label: 'Notificari',
    ));

    // Profile is always available
    items.add(const BottomNavigationBarItem(
      icon: Icon(Icons.person),
      label: 'Profile',
    ));

    return items;
  }


}
