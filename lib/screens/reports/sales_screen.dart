import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../providers/theme_provider.dart';

class SalesScreen extends StatelessWidget {
  const SalesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Vanzari'),
            backgroundColor: themeProvider.isDarkMode
                ? AppColors.darkTertiary
                : AppColors.forestGreen,
            foregroundColor: themeProvider.isDarkMode
                ? AppColors.darkTextPrimary
                : AppColors.white,
          ),
          body: Container(
            color: themeProvider.getBackgroundColor(context),
            child: Center(
              child: Text(
                'Vanzari Screen',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: themeProvider.isDarkMode
                      ? AppColors.forestGreenLight
                      : AppColors.forestGreen,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
