import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/services/error_message_service.dart';
import 'package:animaliaproject/models/api_response.dart';
import 'package:animaliaproject/services/appointment/calendar_service.dart';

void main() {
  group('Appointment Error Handling Tests', () {
    test('should return user-friendly Romanian error messages for scheduling conflicts', () {
      // Test scheduling conflict error
      final conflictMessage = ErrorMessageService.getErrorMessage('SCHEDULING_CONFLICT');
      expect(conflictMessage, 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval');
    });

    test('should return user-friendly Romanian error messages for invalid time slots', () {
      // Test invalid time slot error
      final invalidTimeMessage = ErrorMessageService.getErrorMessage('INVALID_TIME_SLOT');
      expect(invalidTimeMessage, 'Intervalul de timp selectat nu este valid');
    });

    test('should return user-friendly Romanian error messages for staff unavailable', () {
      // Test staff unavailable error
      final staffUnavailableMessage = ErrorMessageService.getErrorMessage('STAFF_UNAVAILABLE');
      expect(staffUnavailableMessage, 'Personalul selectat nu este disponibil în acest interval');
    });

    test('should provide suggested actions for actionable errors', () {
      // Test scheduling conflict suggestion
      final conflictSuggestion = ErrorMessageService.getSuggestedAction('SCHEDULING_CONFLICT');
      expect(conflictSuggestion, 'Alegeți un alt interval de timp sau verificați calendarul pentru conflicte');

      // Test invalid time slot suggestion
      final timeSuggestion = ErrorMessageService.getSuggestedAction('INVALID_TIME_SLOT');
      expect(timeSuggestion, 'Selectați un interval de timp valid în programul de lucru');

      // Test staff unavailable suggestion
      final staffSuggestion = ErrorMessageService.getSuggestedAction('STAFF_UNAVAILABLE');
      expect(staffSuggestion, 'Alegeți alt membru al personalului sau un alt interval de timp');
    });

    test('should identify user-actionable errors correctly', () {
      // Test actionable errors
      expect(ErrorMessageService.isUserActionableError('SCHEDULING_CONFLICT'), true);
      expect(ErrorMessageService.isUserActionableError('INVALID_TIME_SLOT'), true);
      expect(ErrorMessageService.isUserActionableError('STAFF_UNAVAILABLE'), true);
      expect(ErrorMessageService.isUserActionableError('VALIDATION_ERROR'), true);

      // Test non-actionable errors
      expect(ErrorMessageService.isUserActionableError('SERVER_ERROR'), false);
      expect(ErrorMessageService.isUserActionableError('NETWORK_ERROR'), false);
      expect(ErrorMessageService.isUserActionableError('UNKNOWN_ERROR'), false);
    });

    test('should identify retryable errors correctly', () {
      // Test retryable errors
      expect(ErrorMessageService.isRetryableError('NETWORK_ERROR'), true);
      expect(ErrorMessageService.isRetryableError('SERVER_ERROR'), true);
      expect(ErrorMessageService.isRetryableError('SERVICE_UNAVAILABLE'), true);
      expect(ErrorMessageService.isRetryableError('TIMEOUT'), true);

      // Test non-retryable errors
      expect(ErrorMessageService.isRetryableError('SCHEDULING_CONFLICT'), false);
      expect(ErrorMessageService.isRetryableError('VALIDATION_ERROR'), false);
      expect(ErrorMessageService.isRetryableError('UNAUTHORIZED'), false);
    });

    test('should parse structured API error responses correctly', () {
      // Test ApiResponse with structured error
      final response = ApiResponse<String>.error(
        'Cannot schedule appointment: Another appointment or block time is already scheduled during this time slot',
        errorCode: 'SCHEDULING_CONFLICT',
        errorDetails: {
          'message': 'Cannot schedule appointment: Another appointment or block time is already scheduled during this time slot',
          'code': 'SCHEDULING_CONFLICT',
          'conflictType': 'appointment',
          'conflictTime': '2024-01-15T10:00:00Z'
        },
      );

      expect(response.success, false);
      expect(response.errorCode, 'SCHEDULING_CONFLICT');
      expect(response.isSchedulingConflict, true);
      expect(response.isInvalidTimeSlot, false);
      expect(response.isStaffUnavailable, false);
    });

    test('should create AppointmentCreationResult with proper error handling', () {
      // Test failure result with error code
      final failureResult = AppointmentCreationResult.failure(
        'Scheduling conflict detected',
        errorCode: 'SCHEDULING_CONFLICT',
        errorDetails: {'conflictType': 'appointment'},
      );
      expect(failureResult.success, false);
      expect(failureResult.errorCode, 'SCHEDULING_CONFLICT');
      expect(failureResult.isSchedulingConflict, true);
      expect(failureResult.userFriendlyError, 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval');
    });

    test('should handle unknown error codes gracefully', () {
      // Test unknown error code
      final unknownMessage = ErrorMessageService.getErrorMessage('UNKNOWN_ERROR_CODE');
      expect(unknownMessage, 'A apărut o eroare neașteptată');

      // Test with fallback message
      final fallbackMessage = ErrorMessageService.getErrorMessage(
        'UNKNOWN_ERROR_CODE',
        fallbackMessage: 'Custom fallback message',
      );
      expect(fallbackMessage, 'Custom fallback message');
    });

    test('should get API error messages with proper fallback handling', () {
      // Test with error code
      final codeMessage = ErrorMessageService.getApiErrorMessage(
        errorCode: 'SCHEDULING_CONFLICT',
      );
      expect(codeMessage, 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval');

      // Test with error message but no code
      final messageOnly = ErrorMessageService.getApiErrorMessage(
        errorMessage: 'Custom error message',
      );
      expect(messageOnly, 'Custom error message');

      // Test with fallback
      final fallbackOnly = ErrorMessageService.getApiErrorMessage(
        fallbackMessage: 'Fallback error message',
      );
      expect(fallbackOnly, 'Fallback error message');

      // Test with nothing (should use default)
      final defaultMessage = ErrorMessageService.getApiErrorMessage();
      expect(defaultMessage, 'A apărut o eroare neașteptată');
    });

    test('should create scheduling conflict messages with context', () {
      // Test basic scheduling conflict
      final basicMessage = ErrorMessageService.getSchedulingConflictMessage();
      expect(basicMessage, 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval');

      // Test with specific message
      final specificMessage = ErrorMessageService.getSchedulingConflictMessage(
        specificMessage: 'Conflict with existing appointment at 10:00',
      );
      expect(specificMessage, 'Conflict with existing appointment at 10:00');

      // Test with conflict time
      final timeMessage = ErrorMessageService.getSchedulingConflictMessage(
        conflictTime: DateTime(2024, 1, 15, 10, 30),
      );
      expect(timeMessage, 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval la ora 10:30');

      // Test with conflict type
      final typeMessage = ErrorMessageService.getSchedulingConflictMessage(
        conflictType: 'appointment',
      );
      expect(typeMessage, 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval (programare existentă)');
    });
  });
}
