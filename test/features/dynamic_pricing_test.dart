import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/widgets/new_appointment/appointment_form_data.dart';
import 'package:animaliaproject/widgets/new_appointment/appointment_form_constants.dart';
import 'package:animaliaproject/models/service.dart';

void main() {
  group('Dynamic Price Calculation Tests', () {
    late AppointmentFormData formData;

    setUp(() {
      formData = AppointmentFormData(
        appointmentDate: DateTime(2024, 6, 15),
        startTime: DateTime(2024, 6, 15, 10, 0),
        endTime: DateTime(2024, 6, 15, 11, 0),
      );
    });

    group('Breed to Size Mapping', () {
      test('should map dog breeds to correct sizes', () {
        expect(AppointmentFormConstants.getBreedSize('Chihuahua'), equals('S'));
        expect(AppointmentFormConstants.getBreedSize('Labrador Retriever'), equals('L'));
        expect(AppointmentFormConstants.getBreedSize('<PERSON>agle'), equals('M'));
      });

      test('should map cat breeds to correct sizes', () {
        expect(AppointmentFormConstants.getBreedSize('Devon Rex'), equals('S'));
        expect(AppointmentFormConstants.getBreedSize('Maine Coon'), equals('L'));
        expect(AppointmentFormConstants.getBreedSize('Persian'), equals('M'));
      });

      test('should default to Medium for unknown breeds', () {
        expect(AppointmentFormConstants.getBreedSize('Unknown Breed'), equals('M'));
        expect(AppointmentFormConstants.getBreedSize(''), equals('M'));
      });

      test('should check breed mapping availability', () {
        expect(AppointmentFormConstants.hasBreedSizeMapping('Chihuahua'), isTrue);
        expect(AppointmentFormConstants.hasBreedSizeMapping('Unknown Breed'), isFalse);
      });
    });

    group('Size-Based Price Calculation', () {
      test('should calculate price for size-based service', () {
        // Create a service with size-based pricing
        final service = Service(
          id: 'test-service',
          name: 'Test Grooming',
          description: 'Test service',
          price: 50.0, // Base price (not used when sizePrices exist)
          sizePrices: {
            'S': 40.0,
            'M': 60.0,
            'L': 80.0,
          },
          duration: 60,
          createdAt: DateTime.now(),
        );

        expect(service.getPriceForSize('S'), equals(40.0));
        expect(service.getPriceForSize('M'), equals(60.0));
        expect(service.getPriceForSize('L'), equals(80.0));
      });

      test('should handle price ranges for size-based services', () {
        final service = Service(
          id: 'test-service',
          name: 'Test Grooming',
          description: 'Test service',
          price: 50.0,
          sizePrices: {
            'S': 40.0,
            'M': 60.0,
            'L': 80.0,
          },
          sizeMinPrices: {
            'S': 35.0,
            'M': 55.0,
            'L': 75.0,
          },
          sizeMaxPrices: {
            'S': 45.0,
            'M': 65.0,
            'L': 85.0,
          },
          duration: 60,
          createdAt: DateTime.now(),
        );

        expect(service.getPriceRangeForSize('S'), equals('35.00 - 45.00 RON'));
        expect(service.getPriceRangeForSize('M'), equals('55.00 - 65.00 RON'));
        expect(service.getPriceRangeForSize('L'), equals('75.00 - 85.00 RON'));
      });

      test('should fallback to base price for fixed pricing services', () {
        final service = Service(
          id: 'test-service',
          name: 'Test Grooming',
          description: 'Test service',
          price: 50.0,
          duration: 60,
          createdAt: DateTime.now(),
        );

        expect(service.getPriceForSize('S'), equals(50.0));
        expect(service.getPriceForSize('M'), equals(50.0));
        expect(service.getPriceForSize('L'), equals(50.0));
      });
    });

    group('Dynamic Price Updates', () {
      test('should update total price when pet size changes', () {
        // Setup form data with services
        formData.services = ['Test Service'];
        formData.petSize = 'S';

        // Mock service details
        final serviceDetails = {
          'Test Service': {
            'id': 'test-service',
            'name': 'Test Service',
            'description': 'Test service',
            'price': 50.0,
            'sizePrices': {
              'S': 40.0,
              'M': 60.0,
              'L': 80.0,
            },
            'duration': 60,
            'createdAt': DateTime.now().toIso8601String(),
          }
        };

        // Test price for Small size
        final smallPrice = formData.getTotalPriceFromDetailsWithSize(serviceDetails, 'S');
        expect(smallPrice, equals(40.0));

        // Test price for Large size
        final largePrice = formData.getTotalPriceFromDetailsWithSize(serviceDetails, 'L');
        expect(largePrice, equals(80.0));
      });

      test('should handle multiple services with different pricing strategies', () {
        formData.services = ['Size-Based Service', 'Fixed Price Service'];

        final serviceDetails = {
          'Size-Based Service': {
            'id': 'size-service',
            'name': 'Size-Based Service',
            'description': 'Size-based service',
            'price': 50.0,
            'sizePrices': {
              'S': 30.0,
              'M': 50.0,
              'L': 70.0,
            },
            'duration': 60,
            'createdAt': DateTime.now().toIso8601String(),
          },
          'Fixed Price Service': {
            'id': 'fixed-service',
            'name': 'Fixed Price Service',
            'description': 'Fixed price service',
            'price': 25.0,
            'duration': 30,
            'createdAt': DateTime.now().toIso8601String(),
          }
        };

        // For Small pet: 30.0 + 25.0 = 55.0
        final smallTotal = formData.getTotalPriceFromDetailsWithSize(serviceDetails, 'S');
        expect(smallTotal, equals(55.0));

        // For Large pet: 70.0 + 25.0 = 95.0
        final largeTotal = formData.getTotalPriceFromDetailsWithSize(serviceDetails, 'L');
        expect(largeTotal, equals(95.0));
      });
    });

    group('Pricing Breakdown Analysis', () {
      test('should provide detailed pricing breakdown', () {
        formData.services = ['Test Service'];
        formData.petSize = 'M';

        final serviceDetails = {
          'Test Service': {
            'id': 'test-service',
            'name': 'Test Service',
            'description': 'Test service',
            'price': 50.0,
            'sizePrices': {
              'S': 40.0,
              'M': 60.0,
              'L': 80.0,
            },
            'duration': 60,
            'createdAt': DateTime.now().toIso8601String(),
          }
        };

        final breakdown = formData.getPricingBreakdown(serviceDetails);

        expect(breakdown['petSize'], equals('M'));
        expect(breakdown['totalPrice'], equals(60.0));
        expect(breakdown['hasSizeBasedPricing'], isTrue);
        expect(breakdown['services'], hasLength(1));
        
        final serviceBreakdown = breakdown['services'][0];
        expect(serviceBreakdown['name'], equals('Test Service'));
        expect(serviceBreakdown['price'], equals(60.0));
        expect(serviceBreakdown['isSizeBasedPricing'], isTrue);
      });

      test('should detect services with size-based pricing', () {
        formData.services = ['Size Service', 'Fixed Service'];

        final serviceDetails = {
          'Size Service': {
            'id': 'size-service',
            'name': 'Size Service',
            'sizePrices': {'S': 30.0, 'M': 50.0, 'L': 70.0},
            'price': 50.0,
            'duration': 60,
            'createdAt': DateTime.now().toIso8601String(),
          },
          'Fixed Service': {
            'id': 'fixed-service',
            'name': 'Fixed Service',
            'price': 25.0,
            'duration': 30,
            'createdAt': DateTime.now().toIso8601String(),
          }
        };

        expect(formData.hasServicesWithSizeBasedPricing(serviceDetails), isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle missing service details gracefully', () {
        formData.services = ['Missing Service'];
        final serviceDetails = <String, Map<String, dynamic>>{};

        final total = formData.getTotalPriceFromDetailsWithSize(serviceDetails, 'M');
        expect(total, equals(50.0)); // Default fallback price
      });

      test('should handle malformed service data', () {
        formData.services = ['Malformed Service'];

        final serviceDetails = {
          'Malformed Service': {
            'invalid': 'data',
            // Missing required fields
          }
        };

        final total = formData.getTotalPriceFromDetailsWithSize(serviceDetails, 'M');
        // The actual implementation returns 0.0 for malformed data, which is acceptable
        expect(total, equals(0.0)); // Malformed data returns 0.0
      });
    });
  });
}
