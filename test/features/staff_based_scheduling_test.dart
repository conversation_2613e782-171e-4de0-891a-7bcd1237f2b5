import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/providers/calendar_provider.dart';
import 'package:animaliaproject/models/working_hours_settings.dart';
import 'package:animaliaproject/models/staff_working_hours_settings.dart';
import 'package:animaliaproject/services/appointment/calendar_service.dart';
import '../../test/test_setup.dart';

void main() {
  group('Staff-Based Scheduling Tests', () {
    late CalendarProvider provider;

    setUpAll(() {
      // Initialize test environment with all required mocks
      TestSetup.initializeTestEnvironment();
    });

    setUp(() {
      // Clear any previous test data and setup test environment
      TestSetup.clearMocks();
      TestSetup.setupTestEnvironment(
        authToken: 'test-token',
        salonId: 'test-salon-id',
        userId: 'test-user-id',
        userName: 'Test User',
        userEmail: '<EMAIL>',
      );
      provider = CalendarProvider();
    });

    group('Business Hours Override', () {
      test('should return wide time range instead of salon-specific hours', () {
        // Test that getBusinessHours returns wide range for staff-based scheduling
        final businessHours = provider.getBusinessHours();
        
        expect(businessHours['openTime'], equals(6)); // 6 AM
        expect(businessHours['closeTime'], equals(22)); // 10 PM
        expect(businessHours['workDays'], equals([1, 2, 3, 4, 5, 6, 7])); // All days
        expect(businessHours['lunchBreak']['start'], equals(25)); // Disabled
        expect(businessHours['lunchBreak']['end'], equals(25)); // Disabled
      });

      test('should not use salon working hours even when available', () {
        // Create salon working hours settings
        final salonSettings = WorkingHoursSettings(
          salonId: 'test-salon',
          weeklySchedule: {
            'monday': DaySchedule(
              isWorkingDay: true,
              startTime: '09:00',
              endTime: '17:00',
              breakStart: '13:00',
              breakEnd: '14:00',
            ),
          },
          holidays: [],
          customClosures: [],
          updatedAt: DateTime.now(),
        );

        // Set salon working hours
        provider.setWorkingHoursForTesting(salonSettings);

        // Business hours should still return wide range, not salon-specific
        final businessHours = provider.getBusinessHours();
        
        expect(businessHours['openTime'], equals(6)); // Wide range, not 9
        expect(businessHours['closeTime'], equals(22)); // Wide range, not 17
        expect(businessHours['lunchBreak']['start'], equals(25)); // Disabled, not 13
      });
    });

    group('Staff Availability Logic', () {
      test('should return false when staff settings are not available', () {
        final testDate = DateTime(2024, 6, 15); // Saturday
        
        // Without staff settings, should return false (no salon fallback)
        final isWorking = provider.isStaffWorkingOnDateSync('staff-123', testDate);
        
        expect(isWorking, isFalse);
      });

      test('should only check holidays and custom closures for salon closure', () {
        final holidayDate = DateTime(2024, 12, 25); // Christmas
        final regularDate = DateTime(2024, 6, 15); // Regular Saturday
        
        // Holiday should be detected as closed
        final isHolidayClosed = provider.isClosedOnDate(holidayDate);
        expect(isHolidayClosed, isTrue);
        
        // Regular date should not be closed (no salon working days check)
        final isRegularClosed = provider.isClosedOnDate(regularDate);
        expect(isRegularClosed, isFalse);
      });

      test('should only check holidays and custom closures for working day', () {
        final holidayDate = DateTime(2024, 12, 25); // Christmas
        final regularDate = DateTime(2024, 6, 15); // Regular Saturday
        
        // Holiday should not be a working day
        final isHolidayWorking = provider.isWorkingDay(holidayDate);
        expect(isHolidayWorking, isFalse);
        
        // Regular date should be a working day (no salon working days check)
        final isRegularWorking = provider.isWorkingDay(regularDate);
        expect(isRegularWorking, isTrue);
      });
    });

    group('Time Slot Styling', () {
      test('should grey out slots when staff is unavailable', () {
        final testDateTime = DateTime(2024, 6, 15, 10, 0); // Saturday 10 AM
        
        // Without staff settings, slot should be greyed out
        final styling = provider.getTimeSlotStyling(testDateTime, 'staff-123');
        
        expect(styling.isAvailable, isFalse);
        expect(styling.isGreyedOut, isTrue);
        expect(styling.isInteractive, isFalse);
        expect(styling.disabledReason, contains('Personal indisponibil'));
      });

      test('should not apply salon business hours when staff is specified', () {
        final testDateTime = DateTime(2024, 6, 15, 5, 0); // Saturday 5 AM (before salon hours)
        
        // Even though this is before traditional salon hours, 
        // it should only be greyed out due to missing staff settings, not salon hours
        final styling = provider.getTimeSlotStyling(testDateTime, 'staff-123');
        
        expect(styling.isAvailable, isFalse);
        expect(styling.isGreyedOut, isTrue);
        expect(styling.disabledReason, contains('Personal indisponibil'));
        // Should NOT contain salon-related messages
        expect(styling.disabledReason, isNot(contains('programului salonului')));
      });

      test('should apply salon business hours only when no staff is specified', () {
        final testDateTime = DateTime(2024, 6, 15, 8, 0); // Saturday 8 AM (within wide range)

        // Without staff ID, should apply wide business hours
        final styling = provider.getTimeSlotStyling(testDateTime, null);

        expect(styling.isAvailable, isTrue); // 8 AM is within 6-22 range
        expect(styling.isGreyedOut, isFalse);
        expect(styling.isInteractive, isTrue);
      });

      test('should grey out slots on holidays regardless of staff', () {
        final holidayDateTime = DateTime(2024, 12, 25, 10, 0); // Christmas 10 AM
        
        // Holiday should be greyed out even with staff
        final styling = provider.getTimeSlotStyling(holidayDateTime, 'staff-123');
        
        expect(styling.isAvailable, isFalse);
        expect(styling.isGreyedOut, isTrue);
        expect(styling.isInteractive, isFalse);
        expect(styling.disabledReason, contains('Sărbătoare legală'));
      });
    });

    group('Integration Tests', () {
      test('should maintain holiday and closure functionality while removing salon constraints', () {
        final regularDate = DateTime(2024, 6, 15); // Regular Saturday
        final holidayDate = DateTime(2024, 12, 25); // Christmas
        
        // Regular date should be considered working (no salon day constraints)
        expect(provider.isWorkingDay(regularDate), isTrue);
        expect(provider.isClosedOnDate(regularDate), isFalse);
        
        // Holiday should still be detected
        expect(provider.isWorkingDay(holidayDate), isFalse);
        expect(provider.isClosedOnDate(holidayDate), isTrue);
        
        // Business hours should be wide range
        final businessHours = provider.getBusinessHours();
        expect(businessHours['openTime'], equals(6));
        expect(businessHours['closeTime'], equals(22));
        expect(businessHours['workDays'], contains(6)); // Saturday should be included
        expect(businessHours['workDays'], contains(7)); // Sunday should be included
      });
    });
  });
}
