import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/utils/formatters/phone_number_utils.dart';

void main() {
  group('Authentication Flow Phone Normalization Tests', () {
    group('Consistency Tests', () {
      test('should ensure same account access for different phone formats', () {
        // This test verifies that the normalization logic ensures
        // that different input formats for the same phone number
        // result in the same normalized value

        const phone1 = '**********';
        const phone2 = '*********';
        const phone3 = '+***********';

        final normalized1 = PhoneNumberUtils.formatToRomanianStandard(phone1);
        final normalized2 = PhoneNumberUtils.formatToRomanianStandard(phone2);
        final normalized3 = PhoneNumberUtils.formatToRomanianStandard(phone3);

        expect(normalized1, equals(normalized2));
        expect(normalized2, equals(normalized3));
        expect(normalized1, equals('+***********'));
      });

      test('should normalize all common Romanian phone formats consistently', () {
        // Test various input formats that users might enter
        const testCases = [
          '**********',
          '*********',
          '+***********',
          '+40 731 446 895',
          '0731 446 895',
          '731 446 895',
          '+40-731-446-895',
          '(+40) 731 446 895',
        ];
        const expectedNormalized = '+***********';

        for (final testCase in testCases) {
          final normalized = PhoneNumberUtils.formatToRomanianStandard(testCase);
          expect(normalized, equals(expectedNormalized),
            reason: 'Input "$testCase" should normalize to "$expectedNormalized"');
        }
      });

      test('should handle different Romanian mobile prefixes correctly', () {
        const validPrefixes = ['72', '73', '74', '75', '76', '77', '78', '79'];

        for (final prefix in validPrefixes) {
          final phoneWithZero = '0${prefix}1446895';
          final phoneWithoutZero = '${prefix}1446895';
          final phoneWithCountryCode = '+40${prefix}1446895';

          final normalized1 = PhoneNumberUtils.formatToRomanianStandard(phoneWithZero);
          final normalized2 = PhoneNumberUtils.formatToRomanianStandard(phoneWithoutZero);
          final normalized3 = PhoneNumberUtils.formatToRomanianStandard(phoneWithCountryCode);

          final expected = '+40${prefix}1446895';

          expect(normalized1, equals(expected),
            reason: 'Phone with zero prefix should normalize correctly');
          expect(normalized2, equals(expected),
            reason: 'Phone without zero prefix should normalize correctly');
          expect(normalized3, equals(expected),
            reason: 'Phone with country code should remain correct');

          // All should be equal
          expect(normalized1, equals(normalized2));
          expect(normalized2, equals(normalized3));
        }
      });

      test('should demonstrate fix for login consistency issue', () {
        // This test specifically addresses the reported bug where
        // ********** and ********* are treated differently

        const problematicPhone1 = '**********';  // With leading zero
        const problematicPhone2 = '*********';   // Without leading zero

        final normalized1 = PhoneNumberUtils.formatToRomanianStandard(problematicPhone1);
        final normalized2 = PhoneNumberUtils.formatToRomanianStandard(problematicPhone2);

        // Both should normalize to the same value
        expect(normalized1, equals(normalized2),
          reason: 'Both phone formats should result in same normalized value for login');

        // Both should be the correct Romanian format
        expect(normalized1, equals('+***********'));
        expect(normalized2, equals('+***********'));

        print('✅ Phone normalization fix verified:');
        print('   Input: "$problematicPhone1" → Normalized: "$normalized1"');
        print('   Input: "$problematicPhone2" → Normalized: "$normalized2"');
        print('   Result: Both normalize to same value for consistent login');
      });
    });
  });
}
