import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/config/theme/app_theme.dart';
import 'package:animaliaproject/providers/theme_provider.dart';
import 'dart:math' as math;

void main() {
  group('Dark Mode Accessibility Tests', () {
    group('WCAG AA Compliance Tests', () {
      test('should have sufficient contrast ratio for primary text in dark mode', () {
        // WCAG AA requires minimum 4.5:1 contrast ratio for normal text
        // WCAG AAA requires 7:1 contrast ratio for enhanced accessibility
        
        const darkBackground = AppColors.darkPrimary; // #000000
        const primaryText = AppColors.darkTextPrimary; // #FFFFFF
        
        final contrastRatio = _calculateContrastRatio(darkBackground, primaryText);
        
        expect(contrastRatio, greaterThanOrEqualTo(4.5),
          reason: 'Primary text should meet WCAG AA contrast requirements (4.5:1)');
        
        expect(contrastRatio, greaterThanOrEqualTo(7.0),
          reason: 'Primary text should meet WCAG AAA contrast requirements (7:1)');
      });

      test('should have sufficient contrast ratio for secondary text in dark mode', () {
        const darkBackground = AppColors.darkSecondary; // #121212
        const secondaryText = AppColors.darkTextSecondary; // #E5E5E7
        
        final contrastRatio = _calculateContrastRatio(darkBackground, secondaryText);
        
        expect(contrastRatio, greaterThanOrEqualTo(4.5),
          reason: 'Secondary text should meet WCAG AA contrast requirements');
      });

      test('should have sufficient contrast ratio for tertiary text in dark mode', () {
        const darkBackground = AppColors.darkTertiary; // #1C1C1E
        const tertiaryText = AppColors.darkTextTertiary; // #8E8E93
        
        final contrastRatio = _calculateContrastRatio(darkBackground, tertiaryText);
        
        expect(contrastRatio, greaterThanOrEqualTo(3.0),
          reason: 'Tertiary text should meet minimum contrast requirements for placeholder text');
      });

      test('should have sufficient contrast for forest green accent on dark backgrounds', () {
        const darkBackground = AppColors.darkPrimary;
        const forestGreenLight = AppColors.forestGreenLight; // For dark backgrounds
        
        final contrastRatio = _calculateContrastRatio(darkBackground, forestGreenLight);
        
        expect(contrastRatio, greaterThanOrEqualTo(3.0),
          reason: 'Forest green accent should be visible on dark backgrounds');
      });

      test('should have sufficient contrast for card backgrounds', () {
        const darkCard = AppColors.darkCard; // #1E1E1E
        const primaryText = AppColors.darkTextPrimary; // #FFFFFF
        
        final contrastRatio = _calculateContrastRatio(darkCard, primaryText);
        
        expect(contrastRatio, greaterThanOrEqualTo(4.5),
          reason: 'Text on card backgrounds should meet WCAG AA requirements');
      });
    });

    group('Theme Provider Dark Mode Tests', () {
      test('should provide correct dark theme colors', () {
        final themeProvider = ThemeProvider();

        final darkTheme = themeProvider.darkTheme;
        expect(darkTheme.scaffoldBackgroundColor, equals(const Color(0xFF121212)));
        expect(darkTheme.cardTheme.color, equals(const Color(0xFF1E1E1E)));
      });
    });

    group('Visual Contrast Issues Detection', () {
      test('should identify potential contrast issues in current theme', () {
        // Test combinations that might have poor contrast
        final problematicCombinations = [
          {
            'background': AppColors.darkTertiary,
            'text': AppColors.darkTextTertiary,
            'description': 'Tertiary text on tertiary background'
          },
          {
            'background': AppColors.darkSecondary,
            'text': AppColors.darkTextTertiary,
            'description': 'Tertiary text on secondary background'
          },
        ];
        
        for (final combination in problematicCombinations) {
          final background = combination['background'] as Color;
          final text = combination['text'] as Color;
          final description = combination['description'] as String;
          
          final contrastRatio = _calculateContrastRatio(background, text);
          
          if (contrastRatio < 3.0) {
            print('⚠️ Low contrast detected: $description (${contrastRatio.toStringAsFixed(2)}:1)');
          }
          
          // For now, we'll just log issues rather than fail tests
          // This helps identify areas that need improvement
        }
      });
    });
  });
}

/// Calculate contrast ratio between two colors according to WCAG guidelines
/// Returns a ratio where higher values indicate better contrast
/// WCAG AA requires 4.5:1 for normal text, 3:1 for large text
/// WCAG AAA requires 7:1 for normal text, 4.5:1 for large text
double _calculateContrastRatio(Color color1, Color color2) {
  final luminance1 = _calculateLuminance(color1);
  final luminance2 = _calculateLuminance(color2);
  
  final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
  final darker = luminance1 > luminance2 ? luminance2 : luminance1;
  
  return (lighter + 0.05) / (darker + 0.05);
}

/// Calculate relative luminance of a color according to WCAG guidelines
double _calculateLuminance(Color color) {
  final r = _linearizeColorComponent(color.red / 255.0);
  final g = _linearizeColorComponent(color.green / 255.0);
  final b = _linearizeColorComponent(color.blue / 255.0);
  
  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

/// Linearize color component for luminance calculation
double _linearizeColorComponent(double component) {
  if (component <= 0.03928) {
    return component / 12.92;
  } else {
    return ((component + 0.055) / 1.055).pow(2.4);
  }
}

extension on double {
  double pow(double exponent) {
    return math.pow(this, exponent).toDouble();
  }
}
