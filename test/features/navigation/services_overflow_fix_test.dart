import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/models/service.dart';
import 'package:animaliaproject/config/theme/app_theme.dart';

void main() {
  group('Services Overflow Fix Tests', () {
    group('Wrap Layout Tests', () {
      testWidgets('should handle size-based pricing with Wrap layout without overflow', (WidgetTester tester) async {
        // Create a service with size-based pricing that previously caused overflow
        final sizePricingService = Service(
          id: '1',
          name: '<PERSON>vic<PERSON> cu prețuri pe mărimi',
          duration: 90,
          price: 100.0,
          sizePrices: {
            'S': 80.0,
            'M': 120.0,
            'L': 160.0,
          },
          sizeMinPrices: {
            'S': 75.0,
            'M': 115.0,
            'L': 155.0,
          },
          sizeMaxPrices: {
            'S': 85.0,
            'M': 125.0,
            'L': 165.0,
          },
          requirements: ['Câine curat', '<PERSON>ără boli de piele'],
          description: '<PERSON><PERSON><PERSON> cu prețuri variabile pe mărimi',
          createdAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service name
                        Text(
                          sizePricingService.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        // Fixed layout using Wrap instead of Row
                        Wrap(
                          spacing: 12,
                          runSpacing: 8,
                          children: [
                            _buildInfoChip(
                              icon: Icons.access_time,
                              label: sizePricingService.formattedDuration,
                              color: Colors.blue,
                            ),
                            _buildInfoChip(
                              icon: Icons.attach_money,
                              label: sizePricingService.formattedPrice,
                              color: AppColors.forestGreen,
                            ),
                            if (sizePricingService.requirements.isNotEmpty)
                              _buildInfoChip(
                                icon: Icons.rule,
                                label: '${sizePricingService.requirements.length} cerințe',
                                color: Colors.orange,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        // Should not have any overflow exceptions
        expect(tester.takeException(), isNull,
          reason: 'Wrap layout should prevent overflow');

        // Verify all chips are present
        expect(find.byIcon(Icons.access_time), findsOneWidget);
        expect(find.byIcon(Icons.attach_money), findsOneWidget);
        expect(find.byIcon(Icons.rule), findsOneWidget);

        // Verify text is displayed (even if truncated)
        expect(find.textContaining('1h 30min'), findsOneWidget);
        expect(find.textContaining('RON'), findsOneWidget);
        expect(find.text('2 cerințe'), findsOneWidget);
      });

      testWidgets('should handle very long service names without overflow', (WidgetTester tester) async {
        final longNameService = Service(
          id: '2',
          name: 'Serviciu de toaletaj complet cu spălare, tundere, manichiură, pedichiură, parfumare și îngrijire specială pentru câini de talie foarte mare cu blană lungă și dificilă',
          duration: 180,
          price: 250.0,
          sizePrices: {
            'S': 200.0,
            'M': 250.0,
            'L': 300.0,
          },
          sizeMinPrices: {
            'S': 180.0,
            'M': 230.0,
            'L': 280.0,
          },
          sizeMaxPrices: {
            'S': 220.0,
            'M': 270.0,
            'L': 320.0,
          },
          requirements: ['Câine foarte curat', 'Fără boli de piele', 'Vaccinat la zi', 'Deparazitat'],
          description: 'Serviciu foarte complex și detaliat',
          createdAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Long service name with proper text wrapping
                        Text(
                          longNameService.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 12),
                        // Info chips with Wrap layout
                        Wrap(
                          spacing: 12,
                          runSpacing: 8,
                          children: [
                            _buildInfoChip(
                              icon: Icons.access_time,
                              label: longNameService.formattedDuration,
                              color: Colors.blue,
                            ),
                            _buildInfoChip(
                              icon: Icons.attach_money,
                              label: longNameService.formattedPrice,
                              color: AppColors.forestGreen,
                            ),
                            _buildInfoChip(
                              icon: Icons.rule,
                              label: '${longNameService.requirements.length} cerințe',
                              color: Colors.orange,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        // Should not have overflow
        expect(tester.takeException(), isNull);

        // Verify all elements are present
        expect(find.byIcon(Icons.access_time), findsOneWidget);
        expect(find.byIcon(Icons.attach_money), findsOneWidget);
        expect(find.byIcon(Icons.rule), findsOneWidget);
      });

      test('should demonstrate the fix for overflow issue', () {
        final problematicService = Service(
          id: '3',
          name: 'Test Service',
          duration: 120,
          price: 150.0,
          sizePrices: {
            'S': 120.0,
            'M': 150.0,
            'L': 180.0,
          },
          sizeMinPrices: {
            'S': 100.0,
            'M': 130.0,
            'L': 160.0,
          },
          sizeMaxPrices: {
            'S': 140.0,
            'M': 170.0,
            'L': 200.0,
          },
          description: 'Test service with complex pricing',
          createdAt: DateTime.now(),
        );

        final formattedPrice = problematicService.formattedPrice;
        
        print('✅ Overflow Fix Analysis:');
        print('  • Problem: Row layout with long price text caused 377px overflow');
        print('  • Solution: Replace Row with Wrap layout');
        print('  • Price text: $formattedPrice (${formattedPrice.length} chars)');
        print('  • Wrap allows text to flow to next line instead of overflowing');
        print('  • Added Flexible widget with ellipsis for very long text');
        
        expect(formattedPrice.length, greaterThan(50),
          reason: 'Complex pricing still creates long strings');
        
        // The key insight: Wrap layout handles overflow gracefully
        // by moving items to the next line when they don't fit
      });
    });
  });
}

// Helper method to build info chips (same as in the actual screen)
Widget _buildInfoChip({
  required IconData icon,
  required String label,
  required Color color,
}) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: color.withOpacity(0.1),
      borderRadius: BorderRadius.circular(20),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    ),
  );
}
