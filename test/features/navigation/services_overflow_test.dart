import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/models/service.dart';
import 'package:animaliaproject/screens/profile/settings/services_management_screen.dart';

void main() {
  group('Services Screen Overflow Tests', () {
    group('Service Card Layout Tests', () {
      testWidgets('should handle long service names without overflow', (WidgetTester tester) async {
        // Create a service with a very long name
        final longNameService = Service(
          id: '1',
          name: '<PERSON><PERSON><PERSON> de toaletaj complet cu spălare, tundere, manichiură, pedichiură și parfumare pentru câini de talie mare',
          duration: 120,
          price: 150.0,
          description: 'Serviciu complet',
          createdAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    // Simulate the service card layout
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    longNameService.name,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                PopupMenuButton<String>(
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: Text('Edit'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Should not have overflow
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle size-based pricing without overflow', (WidgetTester tester) async {
        // Create a service with size-based pricing that would cause overflow
        final sizePricingService = Service(
          id: '2',
          name: 'Serviciu cu prețuri pe mărimi',
          duration: 60,
          price: 50.0, // Base price
          sizePrices: {
            'S': 50.0,
            'M': 75.0,
            'L': 100.0,
          },
          sizeMinPrices: {
            'S': 45.0,
            'M': 70.0,
            'L': 95.0,
          },
          sizeMaxPrices: {
            'S': 55.0,
            'M': 80.0,
            'L': 105.0,
          },
          description: 'Serviciu cu prețuri variabile',
          createdAt: DateTime.now(),
        );

        // Test the formatted price string length
        final formattedPrice = sizePricingService.formattedPrice;
        print('Formatted price: $formattedPrice');
        print('Price string length: ${formattedPrice.length}');

        // This would be something like: "S: 45.00 - 55.00 RON | M: 70.00 - 80.00 RON | L: 95.00 - 105.00 RON"
        expect(formattedPrice.length, greaterThan(50), 
          reason: 'Size-based pricing should create long strings');

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    // Simulate the problematic Row layout
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // This Row is likely causing the overflow
                            Row(
                              children: [
                                // Duration chip
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(Icons.access_time, size: 16, color: Colors.blue),
                                      const SizedBox(width: 4),
                                      Text(
                                        sizePricingService.formattedDuration,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.blue,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 12),
                                // Price chip - this is the problematic one
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(Icons.attach_money, size: 16, color: Colors.green),
                                      const SizedBox(width: 4),
                                      Text(
                                        formattedPrice, // This is the long text causing overflow
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // This should cause an overflow exception
        final exception = tester.takeException();
        if (exception != null) {
          print('Overflow detected: $exception');
          expect(exception.toString(), contains('overflow'),
            reason: 'Should detect Row overflow with long price text');
        }
      });

      test('should demonstrate the overflow problem with size-based pricing', () {
        // Create a service that would definitely cause overflow
        final problematicService = Service(
          id: '3',
          name: 'Serviciu problematic',
          duration: 90,
          price: 100.0,
          sizePrices: {
            'S': 80.0,
            'M': 120.0,
            'L': 160.0,
          },
          sizeMinPrices: {
            'S': 75.0,
            'M': 115.0,
            'L': 155.0,
          },
          sizeMaxPrices: {
            'S': 85.0,
            'M': 125.0,
            'L': 165.0,
          },
          description: 'Service with problematic pricing display',
          createdAt: DateTime.now(),
        );

        final formattedPrice = problematicService.formattedPrice;
        final formattedDuration = problematicService.formattedDuration;

        print('🔍 Overflow Analysis:');
        print('  • Service name: ${problematicService.name}');
        print('  • Formatted duration: $formattedDuration (${formattedDuration.length} chars)');
        print('  • Formatted price: $formattedPrice (${formattedPrice.length} chars)');
        print('  • Total text length: ${formattedDuration.length + formattedPrice.length} chars');
        print('  • Estimated width needed: ~${(formattedDuration.length + formattedPrice.length) * 8}px');

        // The issue is that the Row tries to fit:
        // 1. Duration chip: ~80px
        // 2. Spacing: 12px  
        // 3. Price chip with very long text: ~400px+
        // 4. Requirements chip (if any): ~100px
        // Total: ~600px+ which exceeds typical screen width of ~400px

        expect(formattedPrice.length, greaterThan(60),
          reason: 'Size-based pricing creates very long strings that cause overflow');
      });
    });
  });
}
