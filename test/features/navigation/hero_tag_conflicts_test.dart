import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/widgets/address_selection/full_screen_map_picker.dart';
import 'package:animaliaproject/screens/clients/clients_list_screen.dart';
import 'package:animaliaproject/screens/appointments/simple_calendar_screen.dart';
import 'package:animaliaproject/screens/notifications_screen.dart';
import 'package:animaliaproject/providers/client_provider.dart';
import 'package:animaliaproject/providers/auth_provider.dart';
import 'package:provider/provider.dart';

void main() {
  group('Hero Tag Conflicts Tests', () {
    group('FloatingActionButton Hero Tags', () {
      testWidgets('should have unique hero tags for all FloatingActionButtons', (WidgetTester tester) async {
        // Test that screens with FloatingActionButtons have unique hero tags
        
        // Test 1: Clients List Screen
        await tester.pumpWidget(
          MaterialApp(
            home: ChangeNotifierProvider(
              create: (_) => ClientProvider(),
              child: const ClientsListScreen(),
            ),
          ),
        );

        // Find FloatingActionButton
        final clientsFab = find.byType(FloatingActionButton);
        expect(clientsFab, findsOneWidget);

        // Test 2: Simple Calendar Screen
        await tester.pumpWidget(
          MaterialApp(
            home: const SimpleCalendarScreen(),
          ),
        );

        final calendarFab = find.byType(FloatingActionButton);
        expect(calendarFab, findsOneWidget);

        // Test 3: Notifications Screen
        await tester.pumpWidget(
          MaterialApp(
            home: ChangeNotifierProvider(
              create: (_) => AuthProvider(),
              child: const NotificationsScreen(),
            ),
          ),
        );

        final notificationsFab = find.byType(FloatingActionButton);
        expect(notificationsFab, findsOneWidget);
      });

      testWidgets('should not have Hero tag conflicts when multiple FABs exist', (WidgetTester tester) async {
        // Create a test scenario where multiple FloatingActionButtons might exist
        // This simulates the case where FullScreenMapPicker is opened from a screen
        // that already has a FloatingActionButton
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const Center(child: Text('Test Screen')),
              floatingActionButton: FloatingActionButton(
                heroTag: "main_fab", // This should have a unique tag
                onPressed: () {},
                child: const Icon(Icons.add),
              ),
            ),
          ),
        );

        // Verify the main FAB exists
        expect(find.byType(FloatingActionButton), findsOneWidget);

        // Now simulate opening FullScreenMapPicker (which has its own FAB)
        await tester.pumpWidget(
          MaterialApp(
            home: const FullScreenMapPicker(),
          ),
        );

        // The map picker should have its own FAB
        expect(find.byType(FloatingActionButton), findsOneWidget);
      });

      testWidgets('should handle nested FloatingActionButtons without conflicts', (WidgetTester tester) async {
        // Test a more complex scenario with nested navigation
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => Center(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FullScreenMapPicker(),
                        ),
                      );
                    },
                    child: const Text('Open Map'),
                  ),
                ),
              ),
              floatingActionButton: FloatingActionButton(
                heroTag: "parent_fab",
                onPressed: () {},
                child: const Icon(Icons.home),
              ),
            ),
          ),
        );

        // Verify parent screen FAB
        expect(find.byType(FloatingActionButton), findsOneWidget);
        expect(find.byIcon(Icons.home), findsOneWidget);

        // Tap to open map picker
        await tester.tap(find.text('Open Map'));
        await tester.pumpAndSettle();

        // Should now see the map picker's FAB
        expect(find.byType(FloatingActionButton), findsOneWidget);
        expect(find.byIcon(Icons.my_location), findsOneWidget);
      });
    });

    group('Hero Tag Best Practices', () {
      test('should document Hero tag naming conventions', () {
        // This test documents the expected naming convention for Hero tags
        const expectedConventions = {
          'main_screen_fab': 'For primary screen FloatingActionButton',
          'map_location_fab': 'For map location FloatingActionButton',
          'clients_add_fab': 'For clients list add FloatingActionButton',
          'calendar_add_fab': 'For calendar add FloatingActionButton',
          'notifications_test_fab': 'For notifications test FloatingActionButton',
        };

        // Verify we have documented conventions
        expect(expectedConventions.isNotEmpty, isTrue);
        
        print('📋 Hero Tag Naming Conventions:');
        expectedConventions.forEach((tag, description) {
          print('  • $tag: $description');
        });
      });
    });
  });
}
