import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/providers/calendar_provider.dart';
import 'package:animaliaproject/utils/romanian_holidays.dart';
import 'package:animaliaproject/models/working_hours_settings.dart';
import 'package:animaliaproject/models/staff_working_hours_settings.dart';
import '../test_setup.dart';

void main() {
  group('Calendar Holiday and Closure Tests', () {
    late CalendarProvider calendarProvider;

    setUpAll(() {
      // Initialize test environment with all required mocks
      TestSetup.initializeTestEnvironment();
    });

    setUp(() {
      // Clear any previous test data and setup test environment
      TestSetup.clearMocks();
      TestSetup.setupTestEnvironment(
        authToken: 'test-token',
        salonId: 'test-salon',
        userId: 'test-user',
        userName: 'Test User',
        userEmail: '<EMAIL>',
        includeWorkingHours: true,
      );
      calendarProvider = CalendarProvider();
    });

    group('Romanian Holiday Detection', () {
      test('should detect New Year as Romanian holiday', () {
        final newYear = DateTime(2024, 1, 1);
        expect(calendarProvider.isRomanianHoliday(newYear), isTrue);
      });

      test('should detect Christmas as Romanian holiday', () {
        final christmas = DateTime(2024, 12, 25);
        expect(calendarProvider.isRomanianHoliday(christmas), isTrue);
      });

      test('should not detect regular day as holiday', () {
        final regularDay = DateTime(2024, 3, 15);
        expect(calendarProvider.isRomanianHoliday(regularDay), isFalse);
      });

      test('should get holiday information for specific date', () {
        final newYear = DateTime(2024, 1, 1);
        final holiday = RomanianHolidays.getHolidayForDate(newYear);
        expect(holiday, isNotNull);
        expect(holiday!.name, contains('Anul Nou'));
      });
    });

    group('Custom Closure Detection', () {
      test('should detect custom closure when working hours settings exist', () {
        // Create mock working hours with custom closure
        final customClosure = CustomClosure(
          reason: 'Renovări',
          date: DateTime(2024, 6, 15),
        );
        
        final workingHours = WorkingHoursSettings(
          salonId: 'test-salon',
          weeklySchedule: {
            'monday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'tuesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'wednesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'thursday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'friday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'saturday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'sunday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: false),
          },
          holidays: [],
          customClosures: [customClosure],
          updatedAt: DateTime.now(),
        );

        // Set the working hours in the provider
        calendarProvider.setWorkingHoursForTesting(workingHours);

        final closureDate = DateTime(2024, 6, 15);
        expect(calendarProvider.hasCustomClosure(closureDate), isTrue);
      });

      test('should not detect custom closure for regular day', () {
        final regularDay = DateTime(2024, 6, 16);
        expect(calendarProvider.hasCustomClosure(regularDay), isFalse);
      });
    });

    group('Comprehensive Closure Information', () {
      test('should provide complete closure info for Romanian holiday', () {
        final newYear = DateTime(2024, 1, 1);
        final closureInfo = calendarProvider.getDateClosureInfo(newYear);

        expect(closureInfo.isRomanianHoliday, isTrue);
        expect(closureInfo.isSalonClosed, isTrue);
        expect(closureInfo.closureReason, contains('Sărbătoare legală'));
        expect(closureInfo.holidayName, isNotNull);
      });

      test('should provide complete closure info for custom closure', () {
        // Setup custom closure
        final customClosure = CustomClosure(
          reason: 'Concediu personal',
          date: DateTime(2024, 7, 20),
        );
        
        final workingHours = WorkingHoursSettings(
          salonId: 'test-salon',
          weeklySchedule: {
            'monday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'tuesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'wednesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'thursday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'friday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'saturday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
            'sunday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: false),
          },
          holidays: [],
          customClosures: [customClosure],
          updatedAt: DateTime.now(),
        );

        calendarProvider.setWorkingHoursForTesting(workingHours);

        final closureDate = DateTime(2024, 7, 20);
        final closureInfo = calendarProvider.getDateClosureInfo(closureDate);

        expect(closureInfo.hasCustomClosure, isTrue);
        expect(closureInfo.isSalonClosed, isTrue);
        expect(closureInfo.closureReason, equals('Concediu personal'));
      });

      test('should provide normal info for regular working day', () {
        final regularDay = DateTime(2024, 6, 18); // Tuesday
        final closureInfo = calendarProvider.getDateClosureInfo(regularDay);

        expect(closureInfo.isRomanianHoliday, isFalse);
        expect(closureInfo.hasCustomClosure, isFalse);
        expect(closureInfo.closureReason, isNull);
        expect(closureInfo.holidayName, isNull);
      });
    });

    group('Time Slot Styling', () {
      test('should grey out time slots on Romanian holidays', () {
        final newYearMorning = DateTime(2024, 1, 1, 10, 0);
        final styling = calendarProvider.getTimeSlotStyling(newYearMorning, 'staff123');

        expect(styling.isAvailable, isFalse);
        expect(styling.isGreyedOut, isTrue);
        expect(styling.isInteractive, isFalse);
        expect(styling.disabledReason, contains('Sărbătoare legală'));
      });

      test('should grey out time slots during lunch break', () {
        // Setup working hours with lunch break
        final workingHours = WorkingHoursSettings(
          salonId: 'test-salon',
          weeklySchedule: {
            'tuesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
          },
          holidays: [],
          customClosures: [],
          updatedAt: DateTime.now(),
        );
        calendarProvider.setWorkingHoursForTesting(workingHours);

        // Also set up staff working hours for staff123
        final staffWorkingHours = StaffWorkingHoursSettings(
          staffId: 'staff123',
          salonId: 'test-salon',
          weeklySchedule: {
            'tuesday': const DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          },
          holidays: [],
          customClosures: [],
          updatedAt: DateTime.now(),
        );
        calendarProvider.setStaffCacheForTesting('staff123', staffWorkingHours);

        final lunchTime = DateTime(2024, 6, 18, 12, 30); // Tuesday lunch
        final styling = calendarProvider.getTimeSlotStyling(lunchTime, 'staff123');

        // Debug: Print the actual styling information
        print('Lunch break test - isAvailable: ${styling.isAvailable}, isGreyedOut: ${styling.isGreyedOut}, disabledReason: "${styling.disabledReason}"');

        expect(styling.isAvailable, isFalse);
        expect(styling.isGreyedOut, isTrue);
        expect(styling.isInteractive, isFalse);
        // For staff-based scheduling, the system returns generic unavailability message during lunch break
        expect(styling.disabledReason, equals('Personal indisponibil la această oră'));
      });

      test('should allow interaction during business hours on working days', () {
        // Setup working hours
        final workingHours = WorkingHoursSettings(
          salonId: 'test-salon',
          weeklySchedule: {
            'tuesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true, breakStart: '12:00', breakEnd: '13:00'),
          },
          holidays: [],
          customClosures: [],
          updatedAt: DateTime.now(),
        );
        calendarProvider.setWorkingHoursForTesting(workingHours);

        // Also set up staff working hours for staff123
        final staffWorkingHours = StaffWorkingHoursSettings(
          staffId: 'staff123',
          salonId: 'test-salon',
          weeklySchedule: {
            'tuesday': const DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          },
          holidays: [],
          customClosures: [],
          updatedAt: DateTime.now(),
        );
        calendarProvider.setStaffCacheForTesting('staff123', staffWorkingHours);

        final businessHour = DateTime(2024, 6, 18, 14, 0); // Tuesday afternoon
        final styling = calendarProvider.getTimeSlotStyling(businessHour, 'staff123');

        expect(styling.isAvailable, isTrue);
        expect(styling.isGreyedOut, isFalse);
        expect(styling.isInteractive, isTrue);
        expect(styling.disabledReason, isNull);
      });
    });

    group('Debug and Logging Tests', () {
      test('should log custom closure detection', () {
        final customClosure = CustomClosure(
          reason: 'Test closure',
          date: DateTime(2024, 8, 15),
        );

        final workingHours = WorkingHoursSettings(
          salonId: 'test-salon',
          weeklySchedule: {
            'thursday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true),
          },
          holidays: [],
          customClosures: [customClosure],
          updatedAt: DateTime.now(),
        );
        calendarProvider.setWorkingHoursForTesting(workingHours);

        // This should trigger debug logging
        final hasCustom = calendarProvider.hasCustomClosure(DateTime(2024, 8, 15));
        expect(hasCustom, isTrue);
      });

      test('should log Romanian holiday detection', () {
        // This should trigger debug logging
        final isHoliday = calendarProvider.isRomanianHoliday(DateTime(2024, 12, 25));
        expect(isHoliday, isTrue);
      });

      test('should log time slot styling decisions', () {
        final newYearMorning = DateTime(2024, 1, 1, 10, 0);

        // This should trigger comprehensive debug logging
        final styling = calendarProvider.getTimeSlotStyling(newYearMorning, 'staff123');

        expect(styling.isGreyedOut, isTrue);
        expect(styling.disabledReason, contains('Sărbătoare legală'));
      });
    });
  });
}
