import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/providers/calendar_provider.dart';
import 'package:animaliaproject/models/staff_working_hours_settings.dart';
import 'package:animaliaproject/models/working_hours_settings.dart';
import '../../test/test_setup.dart';

void main() {
  group('Staff Schedule Update Bug Fix Tests', () {
    late CalendarProvider provider;

    setUpAll(() {
      // Initialize test environment with all required mocks
      TestSetup.initializeTestEnvironment();
    });

    setUp(() {
      // Clear any previous test data and setup test environment
      TestSetup.clearMocks();
      TestSetup.setupTestEnvironment(
        authToken: 'test-token',
        salonId: 'test-salon-id',
        userId: 'test-user-id',
        userName: 'Test User',
        userEmail: '<EMAIL>',
      );
      provider = CalendarProvider();
    });

    test('should clear staff cache when handleScheduleUpdate is called with staffId', () {
      // Simulate staff cache being populated
      final staffId = 'staff-123';

      // Create proper mock working hours settings
      final mockSettings = StaffWorkingHoursSettings(
        staffId: staffId,
        salonId: 'test-salon-id',
        weeklySchedule: {
          'monday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'tuesday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'wednesday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'thursday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'friday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'saturday': DaySchedule(isWorkingDay: false),
          'sunday': DaySchedule(isWorkingDay: false),
        },
        holidays: [],
        customClosures: [],
        updatedAt: DateTime.now(),
      );

      // Add mock data to cache (simulating cached staff settings)
      provider.setStaffCacheForTesting(staffId, mockSettings);

      // Verify cache has data
      expect(provider.hasStaffCacheForTesting(staffId), isTrue);

      // Call handleScheduleUpdate (this should clear the cache)
      provider.handleScheduleUpdate(
        staffId: staffId,
        salonScheduleChanged: false,
        customClosuresChanged: false,
      );

      // Verify cache was cleared
      expect(provider.hasStaffCacheForTesting(staffId), isFalse);
    });

    test('should trigger notifyListeners when staff schedule is updated', () async {
      bool listenerCalled = false;

      // Add listener to detect when notifyListeners is called
      provider.addListener(() {
        listenerCalled = true;
      });

      // Call handleScheduleUpdate
      provider.handleScheduleUpdate(
        staffId: 'staff-123',
        salonScheduleChanged: false,
        customClosuresChanged: false,
      );

      // Wait a bit for async operations to complete
      await Future.delayed(Duration(milliseconds: 100));

      // Verify listener was called (indicating UI will refresh)
      // Note: In test environment, the API calls will fail but the method should still
      // trigger notifyListeners to update the UI
      expect(listenerCalled, isTrue);
    });

    test('should handle staff schedule update without affecting salon schedule', () async {
      bool listenerCalled = false;

      provider.addListener(() {
        listenerCalled = true;
      });

      // Call handleScheduleUpdate for staff only
      provider.handleScheduleUpdate(
        staffId: 'staff-123',
        salonScheduleChanged: false,
        customClosuresChanged: false,
      );

      // Wait for async operations
      await Future.delayed(Duration(milliseconds: 100));

      // Should trigger UI update
      expect(listenerCalled, isTrue);

      // Reset listener flag
      listenerCalled = false;

      // Call handleScheduleUpdate for salon
      provider.handleScheduleUpdate(
        salonScheduleChanged: true,
        customClosuresChanged: false,
      );

      // Wait for async operations
      await Future.delayed(Duration(milliseconds: 100));

      // Should also trigger UI update
      expect(listenerCalled, isTrue);
    });

    test('should clear specific staff cache without affecting other staff', () {
      final staffId1 = 'staff-123';
      final staffId2 = 'staff-456';

      // Create proper mock working hours settings for both staff
      final mockSettings1 = StaffWorkingHoursSettings(
        staffId: staffId1,
        salonId: 'test-salon-id',
        weeklySchedule: {
          'monday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'tuesday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'wednesday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'thursday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'friday': DaySchedule(isWorkingDay: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00'),
          'saturday': DaySchedule(isWorkingDay: false),
          'sunday': DaySchedule(isWorkingDay: false),
        },
        holidays: [],
        customClosures: [],
        updatedAt: DateTime.now(),
      );

      final mockSettings2 = StaffWorkingHoursSettings(
        staffId: staffId2,
        salonId: 'test-salon-id',
        weeklySchedule: {
          'monday': DaySchedule(isWorkingDay: true, startTime: '08:00', endTime: '16:00', breakStart: '12:00', breakEnd: '13:00'),
          'tuesday': DaySchedule(isWorkingDay: true, startTime: '08:00', endTime: '16:00', breakStart: '12:00', breakEnd: '13:00'),
          'wednesday': DaySchedule(isWorkingDay: true, startTime: '08:00', endTime: '16:00', breakStart: '12:00', breakEnd: '13:00'),
          'thursday': DaySchedule(isWorkingDay: true, startTime: '08:00', endTime: '16:00', breakStart: '12:00', breakEnd: '13:00'),
          'friday': DaySchedule(isWorkingDay: true, startTime: '08:00', endTime: '16:00', breakStart: '12:00', breakEnd: '13:00'),
          'saturday': DaySchedule(isWorkingDay: true, startTime: '08:00', endTime: '16:00', breakStart: '12:00', breakEnd: '13:00'),
          'sunday': DaySchedule(isWorkingDay: false),
        },
        holidays: [],
        customClosures: [],
        updatedAt: DateTime.now(),
      );

      // Add mock data for both staff members
      provider.setStaffCacheForTesting(staffId1, mockSettings1);
      provider.setStaffCacheForTesting(staffId2, mockSettings2);

      // Verify both have cache
      expect(provider.hasStaffCacheForTesting(staffId1), isTrue);
      expect(provider.hasStaffCacheForTesting(staffId2), isTrue);

      // Update only staff1
      provider.handleScheduleUpdate(
        staffId: staffId1,
        salonScheduleChanged: false,
        customClosuresChanged: false,
      );

      // Verify only staff1 cache was cleared
      expect(provider.hasStaffCacheForTesting(staffId1), isFalse);
      expect(provider.hasStaffCacheForTesting(staffId2), isTrue);
    });

    group('Integration with Calendar UI', () {
      test('should provide immediate feedback for staff schedule changes', () async {
        // This test verifies that the calendar provider properly handles
        // staff schedule updates in a way that would immediately refresh the UI

        bool uiRefreshTriggered = false;

        provider.addListener(() {
          uiRefreshTriggered = true;
        });

        // Simulate staff schedule update (like what happens in staff_detail_screen.dart)
        provider.handleScheduleUpdate(
          staffId: 'staff-123',
          salonScheduleChanged: false,
          customClosuresChanged: false,
        );

        // Wait for async operations to complete
        await Future.delayed(Duration(milliseconds: 200));

        // Verify UI refresh was triggered
        expect(uiRefreshTriggered, isTrue);
      });

      test('should handle multiple rapid staff updates correctly', () async {
        int listenerCallCount = 0;

        provider.addListener(() {
          listenerCallCount++;
        });

        // Simulate multiple rapid updates (like user quickly changing multiple settings)
        provider.handleScheduleUpdate(staffId: 'staff-123', salonScheduleChanged: false, customClosuresChanged: false);
        provider.handleScheduleUpdate(staffId: 'staff-123', salonScheduleChanged: false, customClosuresChanged: false);
        provider.handleScheduleUpdate(staffId: 'staff-456', salonScheduleChanged: false, customClosuresChanged: false);

        // Wait for all async operations to complete
        await Future.delayed(Duration(milliseconds: 300));

        // Should have triggered listener for each update
        expect(listenerCallCount, equals(3));
      });
    });

    group('Comparison with Salon Schedule Updates', () {
      test('should behave consistently between staff and salon schedule updates', () {
        int staffUpdateListenerCalls = 0;
        int salonUpdateListenerCalls = 0;
        
        // Test staff update
        provider.addListener(() {
          staffUpdateListenerCalls++;
        });
        
        provider.handleScheduleUpdate(
          staffId: 'staff-123',
          salonScheduleChanged: false,
          customClosuresChanged: false,
        );
        
        // Remove listener and add new one for salon test
        provider.removeListener(() {});
        
        provider.addListener(() {
          salonUpdateListenerCalls++;
        });
        
        provider.handleScheduleUpdate(
          salonScheduleChanged: true,
          customClosuresChanged: false,
        );
        
        // Both should trigger exactly one listener call
        expect(staffUpdateListenerCalls, equals(1));
        expect(salonUpdateListenerCalls, equals(1));
      });
    });
  });
}
