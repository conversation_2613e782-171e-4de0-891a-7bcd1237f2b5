import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/services/staff_service.dart';
import 'package:animaliaproject/models/user_role.dart';
import 'package:animaliaproject/models/appointment.dart';

void main() {
  group('Calendar Nickname Integration Tests', () {
    late StaffResponse staffWithNickname;
    late StaffResponse staffWithoutNickname;
    late Appointment testAppointment;

    setUp(() {
      // Create test staff members
      staffWithNickname = StaffResponse(
        id: 'staff-1',
        name: '<PERSON>',
        nickname: 'msak<PERSON>',
        phone: '+40123456789',
        email: '<EMAIL>',
        groomerRole: GroomerRole.groomer,
        clientDataPermission: ClientDataPermission.limitedAccess,
        isActive: true,
        joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      staffWithoutNickname = StaffResponse(
        id: 'staff-2',
        name: '<PERSON>',
        phone: '+40123456788',
        email: '<EMAIL>',
        groomerRole: GroomerRole.groomer,
        clientDataPermission: ClientDataPermission.limitedAccess,
        isActive: true,
        joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      // Create test appointment
      testAppointment = Appointment(
        id: 'appointment-1',
        clientId: 'client-1',
        clientName: 'Ana Client',
        clientPhone: '+40123456787',
        petId: 'pet-1',
        petName: 'Rex',
        petSpecies: 'dog',
        service: 'Tuns complet + baie',
        startTime: DateTime(2024, 6, 15, 10, 0),
        endTime: DateTime(2024, 6, 15, 11, 30),
        status: 'confirmed',
        isPaid: false,
        assignedGroomer: 'Maria Popescu',
        groomerId: 'staff-1',
      );
    });

    group('Staff Display Name Logic', () {
      test('should use nickname when available', () {
        expect(staffWithNickname.displayName, equals('msakK'));
        expect(staffWithNickname.nickname?.isNotEmpty == true, isTrue);
      });

      test('should use full name when nickname not available', () {
        expect(staffWithoutNickname.displayName, equals('Ion Georgescu'));
        expect(staffWithoutNickname.nickname?.isNotEmpty == true, isFalse);
      });

      test('should show full name in parentheses when nickname differs', () {
        final shouldShowFullName = staffWithNickname.nickname != null && 
                                  staffWithNickname.nickname!.isNotEmpty && 
                                  staffWithNickname.nickname != staffWithNickname.name;
        expect(shouldShowFullName, isTrue);
      });
    });

    group('Appointment Staff Matching Logic', () {
      test('should match appointment by groomerId (preferred method)', () {
        // Test the logic used in calendar views for matching appointments to staff
        final matchesById = testAppointment.groomerId == staffWithNickname.id;
        expect(matchesById, isTrue);
      });

      test('should fallback to name matching when groomerId not available', () {
        // Create appointment without groomerId
        final appointmentWithoutId = testAppointment.copyWith(groomerId: null);
        
        // Test name matching logic
        final matchesByName = appointmentWithoutId.assignedGroomer == staffWithNickname.name ||
                             appointmentWithoutId.assignedGroomer == staffWithNickname.displayName;
        expect(matchesByName, isTrue);
      });

      test('should match by display name when assignedGroomer uses nickname', () {
        // Create appointment that uses nickname in assignedGroomer field
        final appointmentWithNickname = testAppointment.copyWith(
          groomerId: null,
          assignedGroomer: 'msakK',
        );
        
        // Test display name matching
        final matchesByDisplayName = appointmentWithNickname.assignedGroomer == staffWithNickname.displayName;
        expect(matchesByDisplayName, isTrue);
      });
    });

    group('Calendar View Display Logic', () {
      test('should display staff header with nickname', () {
        // Test the logic used in day/week view headers
        final headerText = staffWithNickname.displayName;
        expect(headerText, equals('msakK'));
        
        // Test first name extraction for compact views
        final firstNameOrNickname = staffWithNickname.displayName.split(' ')[0];
        expect(firstNameOrNickname, equals('msakK'));
      });

      test('should display staff header with full name when no nickname', () {
        final headerText = staffWithoutNickname.displayName;
        expect(headerText, equals('Ion Georgescu'));
        
        // Test first name extraction
        final firstName = staffWithoutNickname.displayName.split(' ')[0];
        expect(firstName, equals('Ion'));
      });
    });

    group('Staff Selection and Filtering Logic', () {
      test('should display nickname in staff selection dropdown', () {
        // Test coworker selection widget display
        final displayText = staffWithNickname.displayName;
        expect(displayText, equals('msakK'));
        
        // Test if full name should be shown in subtitle
        final shouldShowFullName = staffWithNickname.nickname != null && 
                                  staffWithNickname.nickname!.isNotEmpty && 
                                  staffWithNickname.nickname != staffWithNickname.name;
        expect(shouldShowFullName, isTrue);
      });

      test('should display nickname in calendar settings filter', () {
        // Test calendar settings dialog display
        final primaryText = staffWithNickname.displayName;
        expect(primaryText, equals('msakK'));
        
        // Test secondary text (full name in parentheses)
        final shouldShowSecondaryText = staffWithNickname.nickname != null && 
                                       staffWithNickname.nickname!.isNotEmpty && 
                                       staffWithNickname.nickname != staffWithNickname.name;
        expect(shouldShowSecondaryText, isTrue);
      });

      test('should display nickname in staff legend', () {
        // Test staff color legend display
        final legendText = staffWithNickname.displayName;
        expect(legendText, equals('msakK'));
      });
    });

    group('Appointment Details Display Logic', () {
      test('should find staff by groomerId in appointment details', () {
        final staffList = [staffWithNickname, staffWithoutNickname];
        
        // Test finding staff by groomerId (primary method)
        StaffResponse? foundStaff;
        if (testAppointment.groomerId != null && testAppointment.groomerId!.isNotEmpty) {
          try {
            foundStaff = staffList.firstWhere(
              (s) => s.id == testAppointment.groomerId,
            );
          } catch (e) {
            // Staff not found by ID
          }
        }
        
        expect(foundStaff, isNotNull);
        expect(foundStaff!.id, equals('staff-1'));
        expect(foundStaff.displayName, equals('msakK'));
      });

      test('should fallback to name matching in appointment details', () {
        final staffList = [staffWithNickname, staffWithoutNickname];
        final appointmentWithoutId = testAppointment.copyWith(groomerId: null);
        
        // Test fallback to name matching
        StaffResponse? foundStaff;
        try {
          foundStaff = staffList.firstWhere(
            (s) => s.name == appointmentWithoutId.assignedGroomer || 
                   s.displayName == appointmentWithoutId.assignedGroomer,
          );
        } catch (e) {
          // Staff not found
        }
        
        expect(foundStaff, isNotNull);
        expect(foundStaff!.displayName, equals('msakK'));
      });

      test('should display nickname in appointment details with full name fallback', () {
        // Test appointment details dialog display logic
        final primaryName = staffWithNickname.displayName;
        expect(primaryName, equals('msakK'));
        
        // Test if full name should be shown as additional detail
        final shouldShowFullName = staffWithNickname.nickname != null && 
                                  staffWithNickname.nickname!.isNotEmpty && 
                                  staffWithNickname.nickname != staffWithNickname.name;
        expect(shouldShowFullName, isTrue);
        
        if (shouldShowFullName) {
          final fullName = staffWithNickname.name;
          expect(fullName, equals('Maria Popescu'));
        }
      });
    });

    group('Staff List Response Integration', () {
      test('should parse staff list with mixed nickname data correctly', () {
        final staffListData = {
          'activeStaff': [
            {
              'userId': 'staff-1',
              'userName': 'Maria Popescu',
              'nickname': 'msakK',
              'userPhone': '+40123456789',
              'userEmail': '<EMAIL>',
              'role': 'GROOMER',
              'clientDataPermission': 'LIMITED_ACCESS',
              'isActive': true,
              'hiredAt': '2024-01-01T00:00:00Z',
            },
            {
              'userId': 'staff-2',
              'userName': 'Ion Georgescu',
              'userPhone': '+40123456788',
              'userEmail': '<EMAIL>',
              'role': 'GROOMER',
              'clientDataPermission': 'LIMITED_ACCESS',
              'isActive': true,
              'hiredAt': '2024-01-01T00:00:00Z',
            }
          ],
          'pendingStaff': [],
          'totalActiveCount': 2,
          'totalPendingCount': 0,
          'activeCount': 2,
          'inactiveCount': 0,
        };

        final staffList = StaffListResponse.fromJson(staffListData);
        
        // Verify first staff member (with nickname)
        expect(staffList.activeStaff[0].name, equals('Maria Popescu'));
        expect(staffList.activeStaff[0].nickname, equals('msakK'));
        expect(staffList.activeStaff[0].displayName, equals('msakK'));
        
        // Verify second staff member (without nickname)
        expect(staffList.activeStaff[1].name, equals('Ion Georgescu'));
        expect(staffList.activeStaff[1].nickname, isNull);
        expect(staffList.activeStaff[1].displayName, equals('Ion Georgescu'));
      });
    });

    group('Calendar Provider Integration', () {
      test('should filter appointments by groomerId correctly', () {
        final staffList = [staffWithNickname, staffWithoutNickname];
        final appointments = [testAppointment];
        
        // Test filtering logic used in calendar provider
        final filteredAppointments = appointments.where((appointment) {
          // First try to match by groomerId (most reliable)
          if (appointment.groomerId != null && appointment.groomerId!.isNotEmpty) {
            return staffList.any((staff) => staff.id == appointment.groomerId);
          }
          
          // Fallback to name matching
          final appointmentStaffName = appointment.assignedGroomer ?? '';
          return staffList.any((staff) => 
            staff.name == appointmentStaffName || 
            staff.displayName == appointmentStaffName);
        }).toList();
        
        expect(filteredAppointments.length, equals(1));
        expect(filteredAppointments.first.id, equals('appointment-1'));
      });
    });
  });
}
