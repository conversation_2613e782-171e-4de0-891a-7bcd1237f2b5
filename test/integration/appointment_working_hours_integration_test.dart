import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/models/working_hours_settings.dart';

void main() {
  group('Appointment Working Hours Integration Tests', () {
    late WorkingHoursSettings settings;

    setUp(() {
      settings = WorkingHoursSettings(
        salonId: 'salon1',
        weeklySchedule: {
          'monday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true),
          'tuesday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true),
          'wednesday': const DaySchedule(startTime: null, endTime: null, isWorkingDay: false),
          'thursday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true),
          'friday': const DaySchedule(startTime: '09:00', endTime: '17:00', isWorkingDay: true),
          'saturday': const DaySchedule(startTime: null, endTime: null, isWorkingDay: false),
          'sunday': const DaySchedule(startTime: null, endTime: null, isWorkingDay: false),
        },
        holidays: const [],
        customClosures: const [],
        updatedAt: DateTime(2024, 1, 1),
      );
    });

    test('booking before opening is invalid', () {
      final start = DateTime(2024, 1, 8, 8, 30);
      final end = DateTime(2024, 1, 8, 9, 30);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isFalse);
    });

    test('booking after closing is invalid', () {
      final start = DateTime(2024, 1, 8, 17, 0);
      final end = DateTime(2024, 1, 8, 18, 0);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isFalse);
    });

    test('booking overlapping non-working hours is invalid', () {
      final start = DateTime(2024, 1, 8, 16, 30);
      final end = DateTime(2024, 1, 8, 17, 30);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isFalse);
    });
  });
}
