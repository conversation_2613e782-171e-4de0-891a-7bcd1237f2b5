import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/services/staff_service.dart';
import 'package:animaliaproject/models/user_role.dart';
import 'package:animaliaproject/models/appointment.dart';

void main() {
  group('Team Management Integration Tests', () {
    group('Nickname Support', () {
      test('should create StaffResponse with nickname', () {
        // Arrange
        final staffData = {
          'userId': 'staff-1',
          'userName': '<PERSON>',
          'nickname': '<PERSON>',
          'userPhone': '+40123456789',
          'userEmail': '<EMAIL>',
          'role': 'GROOMER',
          'clientDataPermission': 'LIMITED_ACCESS',
          'isActive': true,
          'hiredAt': '2024-01-01T00:00:00Z',
        };

        // Act
        final staff = StaffResponse.fromJson(staffData);

        // Assert
        expect(staff.name, equals('<PERSON>'));
        expect(staff.nickname, equals('<PERSON>'));
        expect(staff.displayName, equals('<PERSON>')); // Should use nickname
      });

      test('should use full name when nickname is not provided', () {
        // Arrange
        final staffData = {
          'userId': 'staff-2',
          'userName': '<PERSON>',
          'userPhone': '+40123456789',
          'userEmail': '<EMAIL>',
          'role': 'GROOMER',
          'clientDataPermission': 'LIMITED_ACCESS',
          'isActive': true,
          'hiredAt': '2024-01-01T00:00:00Z',
        };

        // Act
        final staff = StaffResponse.fromJson(staffData);

        // Assert
        expect(staff.name, equals('Jane Smith'));
        expect(staff.nickname, isNull);
        expect(staff.displayName, equals('Jane Smith')); // Should use full name
      });

      test('should use full name when nickname is empty', () {
        // Arrange
        final staffData = {
          'userId': 'staff-3',
          'userName': 'Bob Wilson',
          'nickname': '',
          'userPhone': '+40123456789',
          'userEmail': '<EMAIL>',
          'role': 'GROOMER',
          'clientDataPermission': 'LIMITED_ACCESS',
          'isActive': true,
          'hiredAt': '2024-01-01T00:00:00Z',
        };

        // Act
        final staff = StaffResponse.fromJson(staffData);

        // Assert
        expect(staff.name, equals('Bob Wilson'));
        expect(staff.nickname, equals(''));
        expect(staff.displayName, equals('Bob Wilson')); // Should use full name when nickname is empty
      });

      test('should include nickname in JSON serialization', () {
        // Arrange
        final staff = StaffResponse(
          id: 'staff-1',
          name: 'John Doe',
          nickname: 'Johnny',
          phone: '+40123456789',
          email: '<EMAIL>',
          groomerRole: GroomerRole.groomer,
          clientDataPermission: ClientDataPermission.limitedAccess,
          isActive: true,
          joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
        );

        // Act
        final json = staff.toJson();

        // Assert
        expect(json['name'], equals('John Doe'));
        expect(json['nickname'], equals('Johnny'));
      });
    });

    group('Pending Invitation Nickname Support', () {
      test('should create PendingStaffInvitation with nickname', () {
        // Arrange
        final invitationData = {
          'invitationId': 'inv-1',
          'phoneNumber': '+40123456789',
          'nickname': 'Ana',
          'groomerRole': 'GROOMER',
          'clientDataPermission': 'LIMITED_ACCESS',
          'status': 'PENDING',
          'invitedBy': 'admin-1',
          'invitedAt': '2024-01-01T00:00:00Z',
          'expiresAt': '2024-01-08T00:00:00Z',
          'isExpired': false,
        };

        // Act
        final invitation = PendingStaffInvitation.fromJson(invitationData);

        // Assert
        expect(invitation.phoneNumber, equals('+40123456789'));
        expect(invitation.nickname, equals('Ana'));
        expect(invitation.displayName, equals('Ana')); // Should use nickname
      });

      test('should use formatted phone when nickname is not provided', () {
        // Arrange
        final invitationData = {
          'invitationId': 'inv-2',
          'phoneNumber': '+40123456789',
          'groomerRole': 'GROOMER',
          'clientDataPermission': 'LIMITED_ACCESS',
          'status': 'PENDING',
          'invitedBy': 'admin-1',
          'invitedAt': '2024-01-01T00:00:00Z',
          'expiresAt': '2024-01-08T00:00:00Z',
          'isExpired': false,
        };

        // Act
        final invitation = PendingStaffInvitation.fromJson(invitationData);

        // Assert
        expect(invitation.phoneNumber, equals('+40123456789'));
        expect(invitation.nickname, isNull);
        expect(invitation.displayName, isNotEmpty); // Should use formatted phone
      });
    });

    group('AddStaffRequest with Nickname', () {
      test('should create AddStaffRequest with nickname', () {
        // Act
        final request = AddStaffRequest.fromInput(
          phoneNumber: '+40123456789',
          nickname: 'Ana',
          groomerRole: GroomerRole.groomer,
          clientDataPermission: ClientDataPermission.limitedAccess,
          notes: 'Test notes',
        );

        // Assert
        expect(request.phoneNumber, equals('+40123456789'));
        expect(request.nickname, equals('Ana'));
        expect(request.groomerRole, equals(GroomerRole.groomer));
        expect(request.notes, equals('Test notes'));
      });

      test('should include nickname in JSON when provided', () {
        // Arrange
        final request = AddStaffRequest.fromInput(
          phoneNumber: '+40123456789',
          nickname: 'Ana',
          groomerRole: GroomerRole.groomer,
          clientDataPermission: ClientDataPermission.limitedAccess,
        );

        // Act
        final json = request.toJson();

        // Assert
        expect(json['phoneNumber'], equals('+40123456789'));
        expect(json['nickname'], equals('Ana'));
        expect(json['groomerRole'], equals('GROOMER'));
      });

      test('should exclude nickname from JSON when not provided', () {
        // Arrange
        final request = AddStaffRequest.fromInput(
          phoneNumber: '+40123456789',
          groomerRole: GroomerRole.groomer,
          clientDataPermission: ClientDataPermission.limitedAccess,
        );

        // Act
        final json = request.toJson();

        // Assert
        expect(json['phoneNumber'], equals('+40123456789'));
        expect(json.containsKey('nickname'), isFalse);
        expect(json['groomerRole'], equals('GROOMER'));
      });
    });

    group('Appointment Display Name Integration', () {
      test('should include groomerId in appointment model', () {
        // Arrange
        final appointmentData = {
          'id': 'appt-1',
          'client': {
            'id': 'client-1',
            'name': 'John Doe',
            'phone': '+40123456789',
          },
          'pet': {
            'id': 'pet-1',
            'name': 'Buddy',
            'breed': 'Golden Retriever',
          },
          'staff': {
            'id': 'staff-1',
            'name': 'Ana Popescu',
          },
          'services': [
            {'name': 'Tuns complet'}
          ],
          'startTime': '2024-01-01T10:00:00Z',
          'endTime': '2024-01-01T11:00:00Z',
          'status': 'SCHEDULED',
        };

        // Act
        final appointment = Appointment.fromJson(appointmentData);

        // Assert
        expect(appointment.assignedGroomer, equals('Ana Popescu'));
        expect(appointment.groomerId, equals('staff-1'));
      });

      test('should handle appointment with legacy structure', () {
        // Arrange
        final appointmentData = {
          'id': 'appt-2',
          'clientId': 'client-1',
          'clientName': 'John Doe',
          'clientPhone': '+40123456789',
          'petId': 'pet-1',
          'petName': 'Buddy',
          'petSpecies': 'Golden Retriever',
          'service': 'Tuns complet',
          'startTime': '2024-01-01T10:00:00Z',
          'endTime': '2024-01-01T11:00:00Z',
          'status': 'SCHEDULED',
          'assignedGroomer': 'Ana Popescu',
          'groomerId': 'staff-1',
        };

        // Act
        final appointment = Appointment.fromJson(appointmentData);

        // Assert
        expect(appointment.assignedGroomer, equals('Ana Popescu'));
        expect(appointment.groomerId, equals('staff-1'));
      });
    });
  });
}
