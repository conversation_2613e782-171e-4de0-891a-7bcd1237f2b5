import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/widgets/new_appointment/pet_selection_widget.dart';
import 'package:animaliaproject/widgets/new_appointment/appointment_form_data.dart';
import 'package:animaliaproject/models/client.dart';
import 'package:animaliaproject/models/pet.dart';

void main() {
  group('Pet Display Integration Tests', () {
    testWidgets('should show correct UI states for pet selection', (WidgetTester tester) async {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        isExistingClient: true,
      );

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PetSelectionWidget(
              formData: formData,
              onPetSelected: (pet) {},
              onPetNameChanged: (name) {},
              onPetBreedChanged: (breed) {},
              onAddNewPet: () {},
            ),
          ),
        ),
      );

      // Assert - Should show "select client first" message
      expect(find.text('Selectează mai întâi un client pentru a vedea animalele acestuia.'), findsOneWidget);
    });

    testWidgets('should show no pets message when client has no pets', (WidgetTester tester) async {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        isExistingClient: true,
        clientId: 'client-123',
        clientPets: [], // No pets
      );

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PetSelectionWidget(
              formData: formData,
              onPetSelected: (pet) {},
              onPetNameChanged: (name) {},
              onPetBreedChanged: (breed) {},
              onAddNewPet: () {},
            ),
          ),
        ),
      );

      // Assert - Should show "no pets found" message
      expect(find.text('Clientul selectat nu are animale înregistrate. Adaugă un animal nou.'), findsOneWidget);
      expect(find.text('Nume animal *'), findsOneWidget); // Should show new pet form
    });

    testWidgets('should show pet dropdown when client has pets', (WidgetTester tester) async {
      // Arrange
      final pets = [
        Pet(
          id: 'pet-1',
          name: 'Buddy',
          species: 'dog',
          breed: 'Golden Retriever',
          gender: 'male',
          birthDate: DateTime.now().subtract(const Duration(days: 730)),
          weight: 30.0,
          color: 'golden',
          ownerId: 'client-123',
        ),
        Pet(
          id: 'pet-2',
          name: 'Luna',
          species: 'cat',
          breed: 'Persian',
          gender: 'female',
          birthDate: DateTime.now().subtract(const Duration(days: 365)),
          weight: 4.5,
          color: 'white',
          ownerId: 'client-123',
        ),
      ];

      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        isExistingClient: true,
        clientId: 'client-123',
        clientPets: pets,
      );

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PetSelectionWidget(
              formData: formData,
              onPetSelected: (pet) {},
              onPetNameChanged: (name) {},
              onPetBreedChanged: (breed) {},
              onAddNewPet: () {},
            ),
          ),
        ),
      );

      // Assert - Should show pet dropdown
      expect(find.text('Selectează animalul'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);
      
      // Should show "Add new pet" button
      expect(find.text('Animal nou'), findsOneWidget);
    });

    testWidgets('should handle pet selection correctly', (WidgetTester tester) async {
      // Arrange
      final pets = [
        Pet(
          id: 'pet-1',
          name: 'Buddy',
          species: 'dog',
          breed: 'Golden Retriever',
          gender: 'male',
          birthDate: DateTime.now().subtract(const Duration(days: 730)),
          weight: 30.0,
          color: 'golden',
          ownerId: 'client-123',
        ),
      ];

      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        isExistingClient: true,
        clientId: 'client-123',
        clientPets: pets,
      );

      Pet? selectedPet;

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PetSelectionWidget(
              formData: formData,
              onPetSelected: (pet) {
                selectedPet = pet;
              },
              onPetNameChanged: (name) {},
              onPetBreedChanged: (breed) {},
              onAddNewPet: () {},
            ),
          ),
        ),
      );

      // Act - Tap dropdown to open it
      await tester.tap(find.byType(DropdownButtonFormField<String>));
      await tester.pumpAndSettle();

      // Select the pet
      await tester.tap(find.text('Buddy (Golden Retriever)').last);
      await tester.pumpAndSettle();

      // Assert
      expect(selectedPet, isNotNull);
      expect(selectedPet!.id, equals('pet-1'));
      expect(selectedPet!.name, equals('Buddy'));
    });

    testWidgets('should show new pet form for new clients', (WidgetTester tester) async {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        isExistingClient: false, // New client
      );

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PetSelectionWidget(
              formData: formData,
              onPetSelected: (pet) {},
              onPetNameChanged: (name) {},
              onPetBreedChanged: (breed) {},
              onAddNewPet: () {},
            ),
          ),
        ),
      );

      // Assert - Should show new pet form
      expect(find.text('Nume animal *'), findsOneWidget);
      expect(find.text('Rasă *'), findsOneWidget); // Fixed: Use correct Romanian accent
      expect(find.byType(TextFormField), findsAtLeastNWidgets(1)); // Fixed: There are multiple TextFormFields
      expect(find.byType(Autocomplete<String>), findsOneWidget); // Fixed: The breed field is an Autocomplete, not DropdownButtonFormField
    });

    testWidgets('should toggle between existing and new pet modes', (WidgetTester tester) async {
      // Arrange
      final pets = [
        Pet(
          id: 'pet-1',
          name: 'Buddy',
          species: 'dog',
          breed: 'Golden Retriever',
          gender: 'male',
          birthDate: DateTime.now().subtract(const Duration(days: 730)),
          weight: 30.0,
          color: 'golden',
          ownerId: 'client-123',
        ),
      ];

      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        isExistingClient: true,
        clientId: 'client-123',
        clientPets: pets,
      );

      bool addNewPetCalled = false;

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return PetSelectionWidget(
                  formData: formData,
                  onPetSelected: (pet) {},
                  onPetNameChanged: (name) {},
                  onPetBreedChanged: (breed) {},
                  onAddNewPet: () {
                    setState(() {
                      formData.resetToNewPet();
                      addNewPetCalled = true;
                    });
                  },
                );
              },
            ),
          ),
        ),
      );

      // Initially should show pet dropdown
      expect(find.text('Selectează animalul'), findsOneWidget);
      expect(find.text('Animal nou'), findsOneWidget);

      // Act - Tap "Add new pet" button
      await tester.tap(find.text('Animal nou'));
      await tester.pumpAndSettle();

      // Assert
      expect(addNewPetCalled, isTrue);
      expect(formData.isNewPet, isTrue);
    });
  });
}
