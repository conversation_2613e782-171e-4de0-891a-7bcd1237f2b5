import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/config/environment.dart';

void main() {
  group('Environment Configuration Tests', () {
    test('should detect development environment by default', () {
      // In test environment, it should default to development
      expect(EnvironmentConfig.currentEnvironment, Environment.development);
      expect(EnvironmentConfig.isDevelopment, true);
      expect(EnvironmentConfig.isProduction, false);
      expect(EnvironmentConfig.isStaging, false);
    });

    test('should have correct development configuration', () {
      EnvironmentConfig.setEnvironment(Environment.development);
      
      expect(EnvironmentConfig.apiBaseUrl, 'http://192.168.1.153:8080');
      expect(EnvironmentConfig.appName, 'Animalia Grooming (Dev)');
      expect(EnvironmentConfig.bundleId, 'ro.animalia-programari.animalia.dev');
      expect(EnvironmentConfig.isDebugMode, true);
      expect(EnvironmentConfig.logLevel, 'DEBUG');
      expect(EnvironmentConfig.apiTimeout.inSeconds, 30);
    });

    test('should have correct staging configuration', () {
      EnvironmentConfig.setEnvironment(Environment.staging);
      
      expect(EnvironmentConfig.apiBaseUrl, 'https://staging-api.animalia-grooming.ro');
      expect(EnvironmentConfig.appName, 'Animalia Grooming (Staging)');
      expect(EnvironmentConfig.bundleId, 'ro.animalia-programari.animalia.staging');
      expect(EnvironmentConfig.isDebugMode, true);
      expect(EnvironmentConfig.logLevel, 'INFO');
      expect(EnvironmentConfig.apiTimeout.inSeconds, 20);
    });

    test('should have correct production configuration', () {
      EnvironmentConfig.setEnvironment(Environment.production);
      
      expect(EnvironmentConfig.apiBaseUrl, 'https://www.api.animalia-programari.ro');
      expect(EnvironmentConfig.appName, 'Animalia Grooming');
      expect(EnvironmentConfig.bundleId, 'ro.animalia-programari.animalia');
      expect(EnvironmentConfig.isDebugMode, false);
      expect(EnvironmentConfig.logLevel, 'ERROR');
      expect(EnvironmentConfig.apiTimeout.inSeconds, 15);
    });

    test('should have consistent Firebase configuration', () {
      // All environments currently use the same Firebase project
      final devConfig = EnvironmentConfig.firebaseConfig;
      
      EnvironmentConfig.setEnvironment(Environment.staging);
      final stagingConfig = EnvironmentConfig.firebaseConfig;
      
      EnvironmentConfig.setEnvironment(Environment.production);
      final prodConfig = EnvironmentConfig.firebaseConfig;
      
      expect(devConfig['projectId'], stagingConfig['projectId']);
      expect(stagingConfig['projectId'], prodConfig['projectId']);
      expect(devConfig['apiKey'], stagingConfig['apiKey']);
      expect(stagingConfig['apiKey'], prodConfig['apiKey']);
    });

    test('should have same Google Maps API key across environments', () {
      EnvironmentConfig.setEnvironment(Environment.development);
      final devKey = EnvironmentConfig.googleMapsApiKey;
      
      EnvironmentConfig.setEnvironment(Environment.staging);
      final stagingKey = EnvironmentConfig.googleMapsApiKey;
      
      EnvironmentConfig.setEnvironment(Environment.production);
      final prodKey = EnvironmentConfig.googleMapsApiKey;
      
      expect(devKey, stagingKey);
      expect(stagingKey, prodKey);
      expect(devKey.isNotEmpty, true);
    });

    test('should correctly identify environment states', () {
      EnvironmentConfig.setEnvironment(Environment.development);
      expect(EnvironmentConfig.isDevelopment, true);
      expect(EnvironmentConfig.isStaging, false);
      expect(EnvironmentConfig.isProduction, false);
      expect(EnvironmentConfig.isDebugMode, true);
      
      EnvironmentConfig.setEnvironment(Environment.staging);
      expect(EnvironmentConfig.isDevelopment, false);
      expect(EnvironmentConfig.isStaging, true);
      expect(EnvironmentConfig.isProduction, false);
      expect(EnvironmentConfig.isDebugMode, true);
      
      EnvironmentConfig.setEnvironment(Environment.production);
      expect(EnvironmentConfig.isDevelopment, false);
      expect(EnvironmentConfig.isStaging, false);
      expect(EnvironmentConfig.isProduction, true);
      expect(EnvironmentConfig.isDebugMode, false);
    });

    test('should have valid API endpoints', () {
      EnvironmentConfig.setEnvironment(Environment.development);
      expect(EnvironmentConfig.apiBaseUrl.startsWith('http'), true);
      
      EnvironmentConfig.setEnvironment(Environment.staging);
      expect(EnvironmentConfig.apiBaseUrl.startsWith('https'), true);
      
      EnvironmentConfig.setEnvironment(Environment.production);
      expect(EnvironmentConfig.apiBaseUrl.startsWith('https'), true);
    });

    test('should have unique bundle IDs per environment', () {
      EnvironmentConfig.setEnvironment(Environment.development);
      final devBundleId = EnvironmentConfig.bundleId;
      
      EnvironmentConfig.setEnvironment(Environment.staging);
      final stagingBundleId = EnvironmentConfig.bundleId;
      
      EnvironmentConfig.setEnvironment(Environment.production);
      final prodBundleId = EnvironmentConfig.bundleId;
      
      expect(devBundleId, isNot(equals(stagingBundleId)));
      expect(stagingBundleId, isNot(equals(prodBundleId)));
      expect(devBundleId, isNot(equals(prodBundleId)));
      
      expect(devBundleId.endsWith('.dev'), true);
      expect(stagingBundleId.endsWith('.staging'), true);
      expect(prodBundleId.endsWith('.animalia'), true);
    });
  });
}
