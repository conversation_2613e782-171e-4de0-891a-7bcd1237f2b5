import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:animaliaproject/screens/auth/login_screen.dart';
import 'package:animaliaproject/widgets/social_login_buttons.dart';
import 'package:animaliaproject/providers/auth_provider.dart';

void main() {
  testWidgets('SocialLoginButtons render and phone navigation works', (tester) async {
    await tester.pumpWidget(
      ChangeNotifierProvider<AuthProvider>(
        create: (_) => AuthProvider(),
        child: MaterialApp(
          routes: {
            '/phone-login': (_) => const Scaffold(body: Text('Phone Login Screen')),
          },
          home: const LoginScreen(),
        ),
      ),
    );

    expect(find.byType(SocialLoginButtons), findsOneWidget);
    expect(find.text('Continue with Phone'), findsOneWidget);

    await tester.tap(find.text('Continue with Phone'));
    await tester.pumpAndSettle();

    expect(find.text('Phone Login Screen'), findsOneWidget);
  });
}
