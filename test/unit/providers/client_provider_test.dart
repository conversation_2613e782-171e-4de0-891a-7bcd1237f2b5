import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/providers/client_provider.dart';
import 'package:animaliaproject/models/client.dart';
import '../../../test/test_setup.dart';

void main() {
  group('ClientProvider', () {
    late ClientProvider clientProvider;

    setUpAll(() {
      // Initialize test environment with all required mocks
      TestSetup.initializeTestEnvironment();
    });

    setUp(() {
      // Clear any previous test data
      TestSetup.clearMocks();
      clientProvider = ClientProvider();
    });

    group('Data Isolation Logic', () {
      test('should clear all data when clearForSalonSwitch is called', () async {
        // Arrange - Simulate having some data
        clientProvider.searchClients('test query');

        // Verify initial state has search query
        expect(clientProvider.searchQuery, equals('test query'));

        // Act - Clear for salon switch
        await clientProvider.clearForSalonSwitch('new-salon-id');

        // Assert - All data should be cleared
        expect(clientProvider.searchQuery, isEmpty);
        expect(clientProvider.clients, isEmpty);
        expect(clientProvider.filteredClients, isEmpty);
        expect(clientProvider.currentSalonId, equals('new-salon-id'));
      });

      test('should clear data when clear method is called', () {
        // Arrange - Simulate having some data
        clientProvider.searchClients('test query');

        // Verify initial state
        expect(clientProvider.searchQuery, equals('test query'));

        // Act
        clientProvider.clear();

        // Assert
        expect(clientProvider.searchQuery, isEmpty);
        expect(clientProvider.clients, isEmpty);
        expect(clientProvider.filteredClients, isEmpty);
        expect(clientProvider.currentSalonId, isNull);
        expect(clientProvider.isInitialized, isFalse);
      });
    });

    group('Client Search', () {
      test('should filter clients by name correctly', () {
        // Arrange - Manually set some test clients
        final testClients = [
          Client(
            id: '1',
            name: 'John Doe',
            phone: '+40123456789',
            email: '<EMAIL>',
            registrationDate: DateTime.now(),
          ),
          Client(
            id: '2',
            name: 'Jane Smith',
            phone: '+40987654321',
            email: '<EMAIL>',
            registrationDate: DateTime.now(),
          ),
        ];

        // Simulate having loaded clients (normally done by API)
        clientProvider.clients.addAll(testClients);
        clientProvider.filteredClients.addAll(testClients);

        // Act & Assert - Search by name
        clientProvider.searchClients('John');
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.filteredClients[0].name, equals('John Doe'));
        expect(clientProvider.searchQuery, equals('John'));

        // Act & Assert - Search by phone
        clientProvider.searchClients('987654321');
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.filteredClients[0].name, equals('Jane Smith'));

        // Act & Assert - Search by email
        clientProvider.searchClients('jane@example');
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.filteredClients[0].name, equals('Jane Smith'));

        // Act & Assert - Clear search
        clientProvider.searchClients('');
        expect(clientProvider.filteredClients.length, equals(2));
        expect(clientProvider.searchQuery, isEmpty);
      });

      test('should handle case-insensitive search', () {
        // Arrange
        final testClients = [
          Client(
            id: '1',
            name: 'John Doe',
            phone: '+40123456789',
            registrationDate: DateTime.now(),
          ),
        ];

        clientProvider.clients.addAll(testClients);
        clientProvider.filteredClients.addAll(testClients);

        // Act & Assert - Case insensitive search
        clientProvider.searchClients('JOHN');
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.filteredClients[0].name, equals('John Doe'));

        clientProvider.searchClients('doe');
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.filteredClients[0].name, equals('John Doe'));
      });

      test('should return empty results for non-matching search', () {
        // Arrange
        final testClients = [
          Client(
            id: '1',
            name: 'John Doe',
            phone: '+40123456789',
            registrationDate: DateTime.now(),
          ),
        ];

        clientProvider.clients.addAll(testClients);
        clientProvider.filteredClients.addAll(testClients);

        // Act
        clientProvider.searchClients('NonExistentName');

        // Assert
        expect(clientProvider.filteredClients, isEmpty);
        expect(clientProvider.searchQuery, equals('NonExistentName'));
      });
    });

    group('Client Lookup', () {
      test('should find client by ID', () {
        // Arrange
        final testClient = Client(
          id: 'test-id',
          name: 'Test Client',
          phone: '+40123456789',
          registrationDate: DateTime.now(),
        );

        clientProvider.clients.add(testClient);

        // Act
        final foundClient = clientProvider.getClientById('test-id');

        // Assert
        expect(foundClient, isNotNull);
        expect(foundClient!.id, equals('test-id'));
        expect(foundClient.name, equals('Test Client'));
      });

      test('should return null for non-existent client ID', () {
        // Act
        final foundClient = clientProvider.getClientById('non-existent-id');

        // Assert
        expect(foundClient, isNull);
      });
    });
  });
}
