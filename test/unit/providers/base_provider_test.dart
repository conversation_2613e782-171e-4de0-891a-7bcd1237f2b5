import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/providers/base_provider.dart';
import 'package:animaliaproject/core/constants/app_strings.dart';

class DummyProvider extends BaseProvider {
  @override
  Future<void> initialize() async {
    setInitialized(true);
  }

  // Expose protected methods for testing
  void publicSetLoading(bool loading) => setLoading(loading);
  void publicSetError(String? error) => setError(error);
  void publicClearError() => clearError();
  void publicSetInitialized(bool initialized) => setInitialized(initialized);

  Future<T?> publicExecuteAsync<T>(Future<T> Function() operation,
      {String? errorMessage, bool showLoading = true, bool clearErrorFirst = true}) {
    return executeAsync(operation,
        errorMessage: errorMessage,
        showLoading: showLoading,
        clearErrorFirst: clearErrorFirst);
  }

  Future<bool> publicExecuteBoolAsync(Future<bool> Function() operation,
      {String? errorMessage, bool showLoading = true, bool clearErrorFirst = true}) {
    return executeBoolAsync(operation,
        errorMessage: errorMessage,
        showLoading: showLoading,
        clearErrorFirst: clearErrorFirst);
  }

  Future<void> publicExecuteVoidAsync(Future<void> Function() operation,
      {String? errorMessage, bool showLoading = true, bool clearErrorFirst = true}) {
    return executeVoidAsync(operation,
        errorMessage: errorMessage,
        showLoading: showLoading,
        clearErrorFirst: clearErrorFirst);
  }
}

void main() {
  group('BaseProvider Async Execution', () {
    late DummyProvider provider;

    setUp(() {
      provider = DummyProvider();
    });

    test('executeAsync success updates loading and clears error', () async {
      provider.publicSetError('old error');
      final future = provider.publicExecuteAsync<int>(() async {
        expect(provider.isLoading, isTrue);
        return 10;
      });

      expect(provider.isLoading, isTrue);
      final result = await future;

      expect(result, equals(10));
      expect(provider.isLoading, isFalse);
      expect(provider.error, isNull);
    });

    test('executeAsync failure sets error and resets loading', () async {
      final result = await provider.publicExecuteAsync<int>(() async {
        throw Exception('failure');
      });

      expect(result, isNull);
      expect(provider.isLoading, isFalse);
      expect(provider.error, contains(AppStrings.errorGeneral));
    });

    test('executeBoolAsync success returns true', () async {
      final result = await provider.publicExecuteBoolAsync(() async {
        return true;
      });

      expect(result, isTrue);
      expect(provider.error, isNull);
      expect(provider.isLoading, isFalse);
    });

    test('executeBoolAsync failure returns false', () async {
      final result = await provider.publicExecuteBoolAsync(() async {
        throw Exception('fail');
      });

      expect(result, isFalse);
      expect(provider.error, contains(AppStrings.errorGeneral));
      expect(provider.isLoading, isFalse);
    });

    test('executeVoidAsync success completes without error', () async {
      var called = false;
      await provider.publicExecuteVoidAsync(() async {
        called = true;
      });

      expect(called, isTrue);
      expect(provider.error, isNull);
      expect(provider.isLoading, isFalse);
    });

    test('executeVoidAsync failure sets error', () async {
      await provider.publicExecuteVoidAsync(() async {
        throw Exception('boom');
      });

      expect(provider.error, contains(AppStrings.errorGeneral));
      expect(provider.isLoading, isFalse);
    });
  });
}
