import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/widgets/new_appointment/appointment_form_data.dart';
import 'package:animaliaproject/models/client.dart';
import 'package:animaliaproject/models/pet.dart';

void main() {
  group('Appointment Pet Loading Bug Fix Tests', () {
    late AppointmentFormData formData;

    setUp(() {
      formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
      );
    });

    group('Client Selection and Pet Data Clearing', () {
      test('should clear pets when client is updated', () {
        // Arrange
        final client = Client(
          id: 'client-123',
          name: '<PERSON>',
          phone: '+40728626399',
          email: '<EMAIL>',
          address: 'Bucharest',
          notes: '',
          isActive: true,
          registrationDate: DateTime.now(),
        );

        // Add some existing pets to simulate previous client
        formData.clientPets = [
          Pet(
            id: 'old-pet-1',
            name: 'Old Pet',
            species: 'dog',
            breed: 'Labrador',
            gender: 'male',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
            weight: 25.0,
            color: 'brown',
            ownerId: 'old-client',
          ),
        ];

        // Set some pet data to simulate previous selection
        formData.petId = 'old-pet-1';
        formData.petName = 'Old Pet';
        formData.petBreed = 'Labrador';

        // Act
        formData.updateClientData(client);

        // Assert
        expect(formData.clientId, equals('client-123'));
        expect(formData.clientName, equals('John Doe'));
        expect(formData.clientPhone, equals('+40728626399'));
        expect(formData.clientPets, isEmpty); // Should be cleared
        expect(formData.petId, isEmpty); // Should be reset
        expect(formData.petName, isEmpty); // Should be reset
        expect(formData.petBreed, isEmpty); // Should be reset
        expect(formData.petSpecies, equals('dog')); // Should be default
        expect(formData.petSize, equals('M')); // Should be default
      });

      test('should handle pet data assignment correctly', () {
        // Arrange
        final pet = Pet(
          id: 'pet-123',
          name: 'Buddy',
          species: 'dog',
          breed: 'Golden Retriever',
          gender: 'male',
          birthDate: DateTime.now().subtract(const Duration(days: 730)),
          weight: 30.0,
          color: 'golden',
          ownerId: 'client-123',
        );

        // Act
        formData.updatePetData(pet);

        // Assert
        expect(formData.petId, equals('pet-123'));
        expect(formData.petName, equals('Buddy'));
        expect(formData.petSpecies, equals('dog'));
        expect(formData.petBreed, equals('Golden Retriever'));
      });

      test('should simulate client switching scenario', () {
        // Arrange - First client
        final client1 = Client(
          id: 'client-1',
          name: 'Client One',
          phone: '+40728111111',
          email: '<EMAIL>',
          address: 'City1',
          notes: '',
          isActive: true,
          registrationDate: DateTime.now(),
        );

        final pet1 = Pet(
          id: 'pet-1-1',
          name: 'Pet One',
          species: 'dog',
          breed: 'Breed One',
          gender: 'male',
          birthDate: DateTime.now().subtract(const Duration(days: 365)),
          weight: 20.0,
          color: 'brown',
          ownerId: 'client-1',
        );

        // Simulate first client selection
        formData.updateClientData(client1);
        formData.clientPets = [pet1];
        formData.updatePetData(pet1);

        // Verify first client state
        expect(formData.clientId, equals('client-1'));
        expect(formData.petId, equals('pet-1-1'));

        // Arrange - Second client
        final client2 = Client(
          id: 'client-2',
          name: 'Client Two',
          phone: '+40728222222',
          email: '<EMAIL>',
          address: 'City2',
          notes: '',
          isActive: true,
          registrationDate: DateTime.now(),
        );

        // Act - Switch to second client
        formData.updateClientData(client2);

        // Assert - Should have cleared previous client's pet data
        expect(formData.clientId, equals('client-2'));
        expect(formData.clientName, equals('Client Two'));
        expect(formData.clientPets, isEmpty); // Should be cleared
        expect(formData.petId, isEmpty); // Should be reset
        expect(formData.petName, isEmpty); // Should be reset
      });
    });

    group('Pet Selection State Management', () {
      test('should correctly identify new pet state', () {
        // Arrange
        formData.petId = 'new-pet-123456789';

        // Act & Assert
        expect(formData.isNewPet, isTrue);
      });

      test('should correctly identify existing pet state', () {
        // Arrange
        formData.petId = 'existing-pet-123';

        // Act & Assert
        expect(formData.isNewPet, isFalse);
      });

      test('should reset to new pet correctly', () {
        // Arrange
        formData.petId = 'existing-pet-123';
        formData.petName = 'Existing Pet';
        formData.petBreed = 'Existing Breed';

        // Act
        formData.resetToNewPet();

        // Assert
        expect(formData.isNewPet, isTrue);
        expect(formData.petName, isEmpty);
        expect(formData.petBreed, isEmpty);
        expect(formData.petSpecies, equals('dog')); // Default
        expect(formData.petSize, equals('M')); // Default
      });
    });

    group('Edge Cases and Data Validation', () {
      test('should handle empty pet list assignment', () {
        // Act
        formData.clientPets = [];

        // Assert
        expect(formData.clientPets, isEmpty);
      });

      test('should handle multiple pets assignment', () {
        // Arrange
        final pets = [
          Pet(
            id: 'pet-1',
            name: 'Pet One',
            species: 'dog',
            breed: 'Breed One',
            gender: 'male',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
            weight: 20.0,
            color: 'brown',
            ownerId: 'client-1',
          ),
          Pet(
            id: 'pet-2',
            name: 'Pet Two',
            species: 'cat',
            breed: 'Breed Two',
            gender: 'female',
            birthDate: DateTime.now().subtract(const Duration(days: 730)),
            weight: 5.0,
            color: 'black',
            ownerId: 'client-1',
          ),
        ];

        // Act
        formData.clientPets = pets;

        // Assert
        expect(formData.clientPets.length, equals(2));
        expect(formData.clientPets[0].name, equals('Pet One'));
        expect(formData.clientPets[1].name, equals('Pet Two'));
      });
    });
  });
}
