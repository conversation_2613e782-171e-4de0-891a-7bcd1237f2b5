name: Flutter Tests

on:
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
      - name: Install lcov
        run: sudo apt-get update && sudo apt-get install -y lcov
      - name: Run coverage script
        run: ./test_coverage.sh
