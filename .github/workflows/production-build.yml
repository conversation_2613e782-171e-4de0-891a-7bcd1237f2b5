name: Production Build

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to build for'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging
        - development

jobs:
  build-ios:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        channel: stable
        cache: true
        
    - name: Detect Environment
      id: env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENV="${{ github.event.inputs.environment }}"
        else
          # Auto-detect from branch
          case "${{ github.ref_name }}" in
            "main"|"master")
              ENV="production"
              ;;
            "staging"|"stage"|"develop")
              ENV="staging"
              ;;
            *)
              ENV="development"
              ;;
          esac
        fi
        echo "environment=$ENV" >> $GITHUB_OUTPUT
        echo "🌍 Building for environment: $ENV"
        
    - name: Set Environment Variables
      run: |
        echo "FLUTTER_ENV=${{ steps.env.outputs.environment }}" >> $GITHUB_ENV
        echo "Environment set to: ${{ steps.env.outputs.environment }}"
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Run tests
      run: flutter test
      
    - name: Analyze code
      run: flutter analyze
      
    - name: Build iOS (Development/Staging)
      if: steps.env.outputs.environment != 'production'
      run: |
        cd ios
        pod install
        cd ..
        flutter build ios --no-codesign --dart-define=FLUTTER_ENV=${{ steps.env.outputs.environment }}
        
    - name: Build iOS (Production)
      if: steps.env.outputs.environment == 'production'
      run: |
        cd ios
        pod install
        cd ..
        flutter build ios --release --no-codesign --dart-define=FLUTTER_ENV=production
        
    - name: Archive build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ios-build-${{ steps.env.outputs.environment }}
        path: build/ios/
        retention-days: 30
        
    - name: Notify Success
      if: success() && steps.env.outputs.environment == 'production'
      run: |
        echo "🚀 Production build completed successfully!"
        echo "Environment: ${{ steps.env.outputs.environment }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        
  build-android:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        channel: stable
        cache: true
        
    - name: Set up Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Detect Environment
      id: env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENV="${{ github.event.inputs.environment }}"
        else
          case "${{ github.ref_name }}" in
            "main"|"master")
              ENV="production"
              ;;
            "staging"|"stage"|"develop")
              ENV="staging"
              ;;
            *)
              ENV="development"
              ;;
          esac
        fi
        echo "environment=$ENV" >> $GITHUB_OUTPUT
        echo "🌍 Building for environment: $ENV"
        
    - name: Set Environment Variables
      run: |
        echo "FLUTTER_ENV=${{ steps.env.outputs.environment }}" >> $GITHUB_ENV
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Build Android APK
      run: |
        flutter build apk --dart-define=FLUTTER_ENV=${{ steps.env.outputs.environment }}
        
    - name: Build Android App Bundle (Production only)
      if: steps.env.outputs.environment == 'production'
      run: |
        flutter build appbundle --dart-define=FLUTTER_ENV=production
        
    - name: Archive APK
      uses: actions/upload-artifact@v4
      with:
        name: android-apk-${{ steps.env.outputs.environment }}
        path: build/app/outputs/flutter-apk/
        retention-days: 30
        
    - name: Archive App Bundle
      if: steps.env.outputs.environment == 'production'
      uses: actions/upload-artifact@v4
      with:
        name: android-bundle-production
        path: build/app/outputs/bundle/
        retention-days: 30
