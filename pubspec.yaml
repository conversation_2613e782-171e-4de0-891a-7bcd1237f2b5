name: animaliaproject
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+2

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:


  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Authentication
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1
  flutter_facebook_auth: ^7.1.2

  # State management
  provider: ^6.1.2

  # UI
  flutter_svg: ^2.0.10+1
  font_awesome_flutter: ^10.8.0
  pin_code_fields: ^8.0.1
  table_calendar: ^3.0.9
  intl: ^0.19.0
  flutter_speed_dial: ^7.0.0
  image_picker: ^1.0.7

  # Utils
  shared_preferences: ^2.2.2
  http: ^1.2.1
  intl_phone_field: ^3.2.0
  flutter_phoenix: ^1.1.1
  crypto: ^3.0.6
  url_launcher: ^6.2.5
  flutter_secure_storage: ^9.0.0

  # Maps and location
  google_places_flutter: ^2.0.9
  geolocator: ^10.1.0
  permission_handler: ^11.3.1
  google_maps_flutter: ^2.5.0

  # Mock Server
  shelf: ^1.4.1
  shelf_router: ^1.1.4
  shelf_cors_headers: ^0.1.5
  firebase_crashlytics: ^4.3.6
  firebase_analytics: ^11.4.6
  firebase_messaging: ^15.2.6
  flutter_local_notifications: ^17.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

  # Testing utilities
  mockito: ^5.4.4
  build_runner: ^2.4.9

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: false  # We only need iOS
  ios: true
  image_path: "assets/icons/app-icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package