#!/bin/bash

# Test Coverage Script for Animalia Project
# This script runs unit tests with coverage reporting

echo "🧪 Animalia Project - Unit Test Coverage Report"
echo "================================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Start timer
start_time=$(date +%s)

echo -e "${BLUE}📋 Test Configuration:${NC}"
echo "  • Target Coverage: 85%"
echo "  • Environment: Development"
echo "  • Test Type: Comprehensive (Unit, Feature, Widget, Integration)"
echo "  • Total Test Files: $(find test -name "*_test.dart" | wc -l | tr -d ' ')"
echo ""

# Clean previous coverage data
echo -e "${YELLOW}🧹 Cleaning previous coverage data...${NC}"
rm -rf coverage/
rm -f coverage/lcov.info

# Install dependencies if needed
echo -e "${YELLOW}📦 Ensuring dependencies are installed...${NC}"
flutter pub get

# Generate mocks if needed
echo -e "${YELLOW}🔧 Generating mocks...${NC}"
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run all tests with coverage
echo -e "${YELLOW}🧪 Running comprehensive test suite with coverage...${NC}"
if flutter test --coverage; then
    echo -e "${GREEN}✅ All Tests: PASSED${NC}"
    all_tests_passed=true
else
    echo -e "${RED}❌ Some Tests: FAILED${NC}"
    all_tests_passed=false
fi

# Run specific test categories for detailed reporting
echo ""
echo -e "${YELLOW}📊 Running test categories for detailed analysis...${NC}"

# Test categories
categories=("unit" "features" "widgets" "providers" "services")
category_results=()

for category in "${categories[@]}"; do
    if [ -d "test/$category" ]; then
        echo -e "${BLUE}  • Testing $category...${NC}"
        if flutter test test/$category/ --reporter=compact > /dev/null 2>&1; then
            echo -e "${GREEN}    ✅ $category tests: PASSED${NC}"
            category_results+=("$category:PASSED")
        else
            echo -e "${RED}    ❌ $category tests: FAILED${NC}"
            category_results+=("$category:FAILED")
        fi
    else
        echo -e "${YELLOW}    ⚠️  $category directory not found${NC}"
        category_results+=("$category:NOT_FOUND")
    fi
done

# Check if coverage directory exists
if [ -d "coverage" ] && [ -f "coverage/lcov.info" ]; then
    echo -e "${YELLOW}📊 Generating coverage report...${NC}"
    
    # Install lcov if not available (macOS)
    if ! command -v lcov &> /dev/null; then
        echo -e "${YELLOW}📦 Installing lcov...${NC}"
        if command -v brew &> /dev/null; then
            brew install lcov
        else
            echo -e "${RED}❌ lcov not found. Please install lcov to generate HTML coverage report.${NC}"
        fi
    fi
    
    # Generate HTML coverage report if lcov is available
    if command -v lcov &> /dev/null; then
        # Remove generated files from coverage
        lcov --remove coverage/lcov.info \
            '*/generated/*' \
            '*/l10n/*' \
            '*/test/*' \
            '*/test_driver/*' \
            '*/integration_test/*' \
            '*/.dart_tool/*' \
            '*/build/*' \
            '**/main.dart' \
            -o coverage/lcov_filtered.info
        
        # Generate HTML report
        genhtml coverage/lcov_filtered.info -o coverage/html
        
        echo -e "${GREEN}✅ Coverage report generated: coverage/html/index.html${NC}"
    fi
    
    # Extract coverage percentage
    if command -v lcov &> /dev/null; then
        coverage_summary=$(lcov --summary coverage/lcov_filtered.info 2>&1)
        coverage_percentage=$(echo "$coverage_summary" | grep -o '[0-9]*\.[0-9]*%' | tail -1)
        
        if [ ! -z "$coverage_percentage" ]; then
            echo -e "${BLUE}📊 Coverage Summary:${NC}"
            echo "  • Overall Coverage: $coverage_percentage"
            
            # Extract numeric value for comparison
            coverage_numeric=$(echo "$coverage_percentage" | sed 's/%//')
            target_coverage=85.0

            if (( $(echo "$coverage_numeric >= $target_coverage" | bc -l) )); then
                echo -e "${GREEN}✅ Coverage target met! ($coverage_percentage >= 85%)${NC}"
            else
                echo -e "${YELLOW}⚠️  Coverage below target ($coverage_percentage < 85%)${NC}"
            fi
        fi
    fi
else
    echo -e "${RED}❌ Coverage data not generated${NC}"
fi

# Calculate execution time
end_time=$(date +%s)
execution_time=$((end_time - start_time))

echo ""
echo "================================================"
echo -e "${BLUE}📊 Test Execution Summary:${NC}"
echo "  • Execution Time: ${execution_time}s"

if [ "$all_tests_passed" = true ]; then
    echo -e "  • Overall Tests: ${GREEN}PASSED${NC}"
else
    echo -e "  • Overall Tests: ${RED}FAILED${NC}"
fi

# Show category results
echo -e "${BLUE}📋 Test Categories:${NC}"
for result in "${category_results[@]}"; do
    category=$(echo "$result" | cut -d':' -f1)
    status=$(echo "$result" | cut -d':' -f2)
    case $status in
        "PASSED")
            echo -e "  • $category: ${GREEN}PASSED${NC}"
            ;;
        "FAILED")
            echo -e "  • $category: ${RED}FAILED${NC}"
            ;;
        "NOT_FOUND")
            echo -e "  • $category: ${YELLOW}NOT FOUND${NC}"
            ;;
    esac
done

if [ -f "coverage/html/index.html" ]; then
    echo -e "  • Coverage Report: ${GREEN}Generated${NC}"
    echo "  • Report Location: coverage/html/index.html"
else
    echo -e "  • Coverage Report: ${YELLOW}Not Generated${NC}"
fi


if [ "$all_tests_passed" = true ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! Application ready for production deployment.${NC}"
    echo -e "${GREEN}📊 Test Statistics:${NC}"
    echo "  • Total Tests: $(find test -name "*_test.dart" | wc -l | tr -d ' ') files"
    echo "  • Block Time Tests: ✅ Complete"
    echo "  • Appointment Tests: ✅ Complete"
    echo "  • Calendar Tests: ✅ Complete"
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some tests failed. Please fix failing tests before deployment.${NC}"
    echo -e "${YELLOW}💡 Tip: Run 'flutter test --reporter=expanded' for detailed error information${NC}"
    exit 1
fi
