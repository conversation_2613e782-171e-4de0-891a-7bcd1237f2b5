<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Animalia Grooming</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>animaliaproject</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ro.animalia-programari.animalia</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-166674682070-ios-838930ed46df3a1213f977</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.166674682070-c65gio5ehqqh24g8f2cssb30je1pgbto</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb580858994980278</string>
			</array>
		</dict>
	</array>
	<key>FacebookAppID</key>
	<string>580858994980278</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Animalia</string>
<key>GIDClientID</key>
<string>166674682070-c65gio5ehqqh24g8f2cssb30je1pgbto.apps.googleusercontent.com</string>
<key>GMSApiKey</key>
<string>AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>Această aplicație necesită acces la cameră pentru a face fotografii animalelor de companie.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Această aplicație necesită acces la galeria foto pentru a selecta fotografii ale animalelor de companie.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Această aplicație necesită acces pentru a salva fotografii ale animalelor de companie în galeria foto.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Această aplicație necesită acces la locația dvs. pentru a vă ajuta să găsiți adrese și să afișați hărți.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Această aplicație necesită acces la locația dvs. pentru a vă ajuta să găsiți adrese și să afișați hărți.</string>
</dict>
</plist>
