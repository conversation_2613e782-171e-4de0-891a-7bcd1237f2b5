PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - FBSDKLoginKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Crashlytics (11.10.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_analytics (11.4.6):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.5.4):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_crashlytics (4.3.6):
    - Firebase/Crashlytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.6):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.14.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.14.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.14.0)
  - FirebaseSessions (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.1.2):
    - FBSDKLoginKit (~> 18.0.0)
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (101.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop

EXTERNAL SOURCES:
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  FBSDKLoginKit: 5875762d1fe09ddcb05d03365d4f5dc34413843d
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 23a2678c8f20c46ef305c2f05a9ead0e681163cf
  firebase_auth: 4d9603c196d22f71e80cf518b71939c4f2117088
  firebase_core: ba71b44041571da878cb624ce0d80250bcbe58ad
  firebase_crashlytics: 9301f26be1edba849e9a3a00b1d817ca1ae012b5
  firebase_messaging: 13129fe2ca166d1ed2d095062d76cee88943d067
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseAppCheckInterop: a92ba81d0ee3c4cddb1a2e52c668ea51dc63c3ae
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: e25b58ecb90f3285085fa2118861a3c9dfdc62ad
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseCrashlytics: 84b073c997235740e6a951b7ee49608932877e5c
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  FirebaseRemoteConfigInterop: 7b74ceaa54e28863ed17fa39da8951692725eced
  FirebaseSessions: 9b3b30947b97a15370e0902ee7a90f50ef60ead6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: d97dc09216aebd99d7ac52360186a630f67bdf18
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: 01b01a97c39a0762115bd67a5e39b2a7c321d2d3

COCOAPODS: 1.16.2
