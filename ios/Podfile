# Define a global platform for your project
platform :ios, '14.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

# Add this pre_install hook to fix Firebase modular headers issue
pre_install do |installer|
  # workaround for https://github.com/CocoaPods/CocoaPods/issues/3289
  Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
end

target 'Runner' do
  use_frameworks! :linkage => :static

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    # Fix for Firebase modular headers issue
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'

      # Fix for modular headers issue
      config.build_settings['DEFINES_MODULE'] = 'YES'

      # Add this to fix Firebase modular headers issue
      config.build_settings['APPLICATION_EXTENSION_API_ONLY'] = 'NO'

      # Fix for non-modular includes in Firebase
      if target.name.start_with?("firebase_auth", "Firebase", "FirebaseCore", "FirebaseAuth")
        config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      end

      # Disable code signing for pods
      config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
    end
  end

  # Fix for modular headers issue with Firebase
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
  end

  # Fix Firebase header issues by modifying the problematic header files
  system('find ${PODS_ROOT}/Headers/Public/Firebase -type f -name "*.h" -exec sed -i "" "s/\#import <Firebase\/Firebase.h>/\/\/\#import <Firebase\/Firebase.h>/g" {} \;')

  # Fix specific Firebase Auth header files
  firebase_auth_headers = [
    "${PODS_ROOT}/.symlinks/plugins/firebase_auth/ios/Classes/Private/FLTAuthStateChannelStreamHandler.h",
    "${PODS_ROOT}/.symlinks/plugins/firebase_auth/ios/Classes/Private/PigeonParser.h",
    "${PODS_ROOT}/.symlinks/plugins/firebase_auth/ios/Classes/Private/FLTIdTokenChannelStreamHandler.h",
    "${PODS_ROOT}/.symlinks/plugins/firebase_auth/ios/Classes/Private/FLTPhoneNumberVerificationStreamHandler.h",
    "${PODS_ROOT}/.symlinks/plugins/firebase_auth/ios/Classes/Public/FLTFirebaseAuthPlugin.h"
  ]

  firebase_auth_headers.each do |header|
    system("sed -i '' 's/\#import <Firebase\/Firebase.h>/\/\/\#import <Firebase\/Firebase.h>/g' #{header}")
  end
end
