**/dgph
*.mode1v3
*.mode2v3
*.moved-aside
*.pbxuser
*.perspectivev3
**/*sync/
.sconsign.dblite
.tags*
**/.vagrant/
**/DerivedData/
Icon?
**/Pods/
# Exception: Allow specific files for CI/CD compatibility
!Pods/Target Support Files/
!Pods/Target Support Files/Pods-Runner/
!Pods/Target Support Files/Pods-Runner/*.xcfilelist
!Pods/Target Support Files/Pods-Runner/*.sh
!Pods/Target Support Files/Pods-Runner/*.xcconfig
**/.symlinks/
profile
xcuserdata
**/.generated/
Flutter/App.framework
Flutter/Flutter.framework
Flutter/Flutter.podspec
# Flutter/Generated.xcconfig - temporarily allowing this for CI/CD
Flutter/ephemeral/
Flutter/app.flx
Flutter/app.zip
Flutter/flutter_assets/
Flutter/flutter_export_environment.sh
ServiceDefinitions.json
Runner/GeneratedPluginRegistrant.*

# Exceptions to above rules.
!default.mode1v3
!default.mode2v3
!default.pbxuser
!default.perspectivev3
