import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:animaliaproject/config/api_config.dart';
import 'package:animaliaproject/config/environment.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('API Integration Tests', () {
    
    setUp(() async {
      print('🌐 Setting up API integration tests...');
      print('📍 API Base URL: ${ApiConfig.baseUrl}');
      print('🏗️ Environment: ${EnvironmentConfig.currentEnvironment}');
    });

    test('API server connectivity test', () async {
      print('🎯 Testing: API server connectivity');
      
      try {
        // Test basic connectivity to the API server
        final response = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/health'),
          headers: ApiConfig.defaultHeaders,
        ).timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          print('✅ API server is reachable');
          
          try {
            final data = jsonDecode(response.body);
            print('📊 Health Check Response: $data');
          } catch (e) {
            print('⚠️ Response is not JSON: ${response.body}');
          }
        } else {
          print('⚠️ API server responded with status: ${response.statusCode}');
        }
        
        // The test passes if we get any response (even 404 means server is running)
        expect(response.statusCode, lessThan(500));
        
      } catch (e) {
        print('❌ API connectivity test failed: $e');
        
        // For integration tests, we want to know about connectivity issues
        // but not necessarily fail the test if the server is down
        print('⚠️ This might indicate the backend server is not running');
        print('💡 To run backend: Start your Spring Boot server or mock server');
      }
    });

    test('Authentication endpoints test', () async {
      print('🎯 Testing: Authentication endpoints');
      
      try {
        // Test Firebase login endpoint
        final loginResponse = await http.post(
          Uri.parse(ApiConfig.firebaseLoginEndpoint),
          headers: ApiConfig.defaultHeaders,
          body: jsonEncode({
            'idToken': 'test-token-for-integration-test',
            'deviceInfo': {
              'platform': 'iOS',
              'version': '1.0.0',
              'deviceId': 'integration-test-device'
            }
          }),
        ).timeout(const Duration(seconds: 10));

        print('📊 Login Response Status: ${loginResponse.statusCode}');
        
        if (loginResponse.statusCode == 200) {
          print('✅ Authentication endpoint is working');
          
          try {
            final data = jsonDecode(loginResponse.body);
            print('📊 Login Response: ${data.keys.toList()}');
          } catch (e) {
            print('⚠️ Login response is not JSON');
          }
        } else if (loginResponse.statusCode == 401 || loginResponse.statusCode == 400) {
          print('✅ Authentication endpoint is working (expected auth failure with test token)');
        } else {
          print('⚠️ Unexpected response: ${loginResponse.statusCode}');
        }
        
        // Accept various response codes that indicate the endpoint exists
        expect([200, 400, 401, 422].contains(loginResponse.statusCode), true);
        
      } catch (e) {
        print('❌ Authentication endpoint test failed: $e');
        print('💡 Make sure the backend authentication service is running');
      }
    });

    test('Salon data endpoints test', () async {
      print('🎯 Testing: Salon data endpoints');
      
      try {
        // Test salon settings endpoint
        final salonResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/salons/1/settings'),
          headers: ApiConfig.defaultHeaders,
        ).timeout(const Duration(seconds: 10));

        print('📊 Salon Response Status: ${salonResponse.statusCode}');
        
        if (salonResponse.statusCode == 200) {
          print('✅ Salon endpoints are working');
          
          try {
            final data = jsonDecode(salonResponse.body);
            print('📊 Salon Data Keys: ${data.keys.toList()}');
          } catch (e) {
            print('⚠️ Salon response is not JSON');
          }
        } else if (salonResponse.statusCode == 401) {
          print('✅ Salon endpoints require authentication (expected)');
        } else if (salonResponse.statusCode == 404) {
          print('⚠️ Salon endpoint not found - check API routes');
        } else {
          print('⚠️ Unexpected salon response: ${salonResponse.statusCode}');
        }
        
        // Accept various response codes
        expect([200, 401, 404].contains(salonResponse.statusCode), true);
        
      } catch (e) {
        print('❌ Salon endpoints test failed: $e');
      }
    });

    test('Client management endpoints test', () async {
      print('🎯 Testing: Client management endpoints');
      
      try {
        // Test clients endpoint
        final clientsResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/salons/1/clients'),
          headers: ApiConfig.defaultHeaders,
        ).timeout(const Duration(seconds: 10));

        print('📊 Clients Response Status: ${clientsResponse.statusCode}');
        
        if (clientsResponse.statusCode == 200) {
          print('✅ Client endpoints are working');
        } else if (clientsResponse.statusCode == 401) {
          print('✅ Client endpoints require authentication (expected)');
        } else {
          print('⚠️ Unexpected clients response: ${clientsResponse.statusCode}');
        }
        
        // Accept various response codes
        expect([200, 401, 404].contains(clientsResponse.statusCode), true);
        
      } catch (e) {
        print('❌ Client endpoints test failed: $e');
      }
    });

    test('Services endpoints test', () async {
      print('🎯 Testing: Services endpoints');
      
      try {
        // Test services endpoint
        final servicesResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/salons/1/services'),
          headers: ApiConfig.defaultHeaders,
        ).timeout(const Duration(seconds: 10));

        print('📊 Services Response Status: ${servicesResponse.statusCode}');
        
        if (servicesResponse.statusCode == 200) {
          print('✅ Services endpoints are working');
        } else if (servicesResponse.statusCode == 401) {
          print('✅ Services endpoints require authentication (expected)');
        } else {
          print('⚠️ Unexpected services response: ${servicesResponse.statusCode}');
        }
        
        // Accept various response codes
        expect([200, 401, 404].contains(servicesResponse.statusCode), true);
        
      } catch (e) {
        print('❌ Services endpoints test failed: $e');
      }
    });

    test('API configuration validation', () async {
      print('🎯 Testing: API configuration validation');
      
      // Test API configuration
      expect(ApiConfig.baseUrl.isNotEmpty, true);
      expect(ApiConfig.apiVersion.isNotEmpty, true);
      expect(ApiConfig.isValidServerConfig(), true);
      
      print('✅ API configuration is valid');
      print('📊 Base URL: ${ApiConfig.baseUrl}');
      print('📊 API Version: ${ApiConfig.apiVersion}');
      print('📊 Environment: ${EnvironmentConfig.currentEnvironment}');
      
      // Validate headers
      final headers = ApiConfig.defaultHeaders;
      expect(headers['Content-Type'], 'application/json');
      expect(headers['Accept'], 'application/json');
      
      print('✅ API headers are correctly configured');
    });
  });
}
