# Environment Automation Guide

This document explains the automated environment configuration system for the Animalia Grooming Flutter app.

## 🌍 Environment Overview

The app supports three environments:

- **Development** (`development`): Local development with debug features
- **Staging** (`staging`): Pre-production testing environment
- **Production** (`production`): Live production environment

## 🤖 Automatic Environment Detection

The environment is automatically detected based on:

1. **Compile-time environment variable** (`FLUTTER_ENV`)
2. **Git branch name** (fallback)
3. **Flutter build mode** (final fallback)

### Branch-to-Environment Mapping

| Git Branch | Environment | Description |
|------------|-------------|-------------|
| `main`, `master` | `production` | Production builds for App Store |
| `staging`, `stage`, `develop` | `staging` | Pre-production testing |
| All other branches | `development` | Local development |

## 🚀 CI/CD Integration

### Xcode Cloud (iOS)

The CI scripts automatically detect and set the environment:

- **`ci_scripts/ci_post_clone.sh`**: Detects branch and sets `FLUTTER_ENV`
- **`ci_scripts/ci_pre_xcodebuild.sh`**: Ensures environment is set before build

### GitHub Actions

- **`.github/workflows/production-build.yml`**: Automated builds for all environments
- **`.github/workflows/test.yml`**: Runs tests on pull requests

## 🛠️ Local Development

### Quick Commands

```bash
# Auto-detect environment and build
./scripts/build_for_environment.sh

# Build for specific environment
./scripts/build_for_environment.sh production ios release
./scripts/build_for_environment.sh staging android debug

# Set environment manually
./scripts/set_environment.sh production
```

### Manual Environment Setting

```bash
# Set environment variable
export FLUTTER_ENV=production

# Build with environment
flutter build ios --dart-define=FLUTTER_ENV=production
flutter build apk --dart-define=FLUTTER_ENV=production
```

## 📱 Environment Configurations

### API Endpoints

| Environment | API Base URL |
|-------------|--------------|
| Development | `http://*************:8080` |
| Staging | `https://staging-api.animalia-grooming.ro` |
| Production | `https://www.api.animalia-programari.ro` |

### Bundle IDs

| Environment | iOS Bundle ID |
|-------------|---------------|
| Development | `ro.animalia-programari.animalia.dev` |
| Staging | `ro.animalia-programari.animalia.staging` |
| Production | `ro.animalia-programari.animalia` |

### App Names

| Environment | Display Name |
|-------------|--------------|
| Development | "Animalia Grooming (Dev)" |
| Staging | "Animalia Grooming (Staging)" |
| Production | "Animalia Grooming" |

## 🔧 Configuration Files

### Core Files

- **`lib/config/environment.dart`**: Main environment configuration
- **`lib/config/api_config.dart`**: API endpoint configuration
- **`lib/services/firebase_config.dart`**: Firebase initialization

### Scripts

- **`scripts/set_environment.sh`**: Manual environment configuration
- **`scripts/build_for_environment.sh`**: Environment-aware building
- **`ci_scripts/ci_post_clone.sh`**: Xcode Cloud post-clone setup
- **`ci_scripts/ci_pre_xcodebuild.sh`**: Xcode Cloud pre-build setup

## 🚀 Production Deployment Workflow

### Automatic Production Builds

1. **Push to `main` branch**
2. **Xcode Cloud detects branch** and sets `FLUTTER_ENV=production`
3. **GitHub Actions builds** both iOS and Android
4. **Artifacts are uploaded** for distribution

### Manual Production Build

```bash
# Set production environment
./scripts/set_environment.sh production

# Clean and build
flutter clean
flutter pub get

# Build iOS for production
cd ios && pod install && cd ..
flutter build ios --release --dart-define=FLUTTER_ENV=production

# Build Android for production
flutter build appbundle --release --dart-define=FLUTTER_ENV=production
```

## 🔍 Verification

### Check Current Environment

```dart
import 'package:animaliaproject/config/environment.dart';

void main() {
  print('Current environment: ${EnvironmentConfig.currentEnvironment}');
  print('API URL: ${EnvironmentConfig.apiBaseUrl}');
  print('Bundle ID: ${EnvironmentConfig.bundleId}');
  print('Is Production: ${EnvironmentConfig.isProduction}');
}
```

### Debug Output

In debug mode, the app automatically prints configuration:

```
🌍 Environment: production
🔗 API Base URL: https://www.api.animalia-programari.ro
📱 App Name: Animalia Grooming
📦 Bundle ID: ro.animalia-programari.animalia
⏱️ API Timeout: 15s
📝 Log Level: ERROR
```

## ⚠️ Important Notes

### Production Checklist

Before deploying to production:

- [ ] Verify Firebase configuration
- [ ] Check API endpoints are correct
- [ ] Ensure proper code signing certificates
- [ ] Test on physical devices
- [ ] Verify bundle IDs match App Store configuration

### Security Considerations

- API keys and sensitive data should be stored in environment-specific configurations
- Production builds should have minimal logging
- Debug features should be disabled in production

## 🐛 Troubleshooting

### Environment Not Detected

```bash
# Check current branch
git branch --show-current

# Manually set environment
export FLUTTER_ENV=production

# Verify in Flutter
flutter build ios --dart-define=FLUTTER_ENV=production
```

### Build Issues

```bash
# Clean everything
flutter clean
cd ios && pod install && cd ..
flutter pub get

# Rebuild
./scripts/build_for_environment.sh production
```

### CI/CD Issues

1. Check Xcode Cloud logs for environment detection
2. Verify GitHub Actions environment variables
3. Ensure all scripts have execute permissions

## 📞 Support

For issues with environment configuration:

1. Check the debug output in the app
2. Verify CI/CD logs
3. Test locally with manual environment setting
4. Review this documentation for troubleshooting steps
