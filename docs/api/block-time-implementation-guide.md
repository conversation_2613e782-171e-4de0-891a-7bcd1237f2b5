# Block Time API Implementation Guide

## Overview
Implementation guide for backend developers to implement the block time feature API endpoints for the Animalia grooming salon management system.

## Technology Stack
- **Framework**: Spring Boot 3.x
- **Database**: PostgreSQL 15+
- **Authentication**: JWT with Spring Security
- **Validation**: Jakarta Bean Validation
- **Documentation**: OpenAPI 3.0 (Swagger)

## Project Structure
```
src/main/java/ro/animalia/api/
├── controller/
│   └── BlockTimeController.java
├── service/
│   ├── BlockTimeService.java
│   └── impl/BlockTimeServiceImpl.java
├── repository/
│   └── BlockTimeRepository.java
├── dto/
│   ├── request/
│   │   ├── CreateBlockTimeRequest.java
│   │   ├── UpdateBlockTimeRequest.java
│   │   └── BulkBlockTimeRequest.java
│   └── response/
│       ├── BlockTimeResponse.java
│       ├── BlockTimeListResponse.java
│       └── BlockTimeStatisticsResponse.java
├── entity/
│   ├── BlockTime.java
│   └── RecurrencePattern.java
├── exception/
│   ├── BlockTimeNotFoundException.java
│   ├── SchedulingConflictException.java
│   └── InvalidTimeRangeException.java
└── config/
    └── BlockTimeConfig.java
```

## Entity Implementation

### BlockTime Entity
```java
@Entity
@Table(name = "block_times")
@EntityListeners(AuditingEntityListener.class)
public class BlockTime {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(name = "salon_id", nullable = false)
    private UUID salonId;
    
    @Column(name = "start_time", nullable = false)
    private OffsetDateTime startTime;
    
    @Column(name = "end_time", nullable = false)
    private OffsetDateTime endTime;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BlockReason reason;
    
    @Column(name = "custom_reason")
    private String customReason;
    
    @ElementCollection
    @CollectionTable(name = "block_time_staff", joinColumns = @JoinColumn(name = "block_time_id"))
    @Column(name = "staff_id")
    private Set<UUID> staffIds = new HashSet<>();
    
    @Column(name = "created_by", nullable = false)
    private UUID createdBy;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;
    
    @Column(name = "updated_by")
    private UUID updatedBy;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private OffsetDateTime updatedAt;
    
    @Column(name = "is_recurring")
    private Boolean isRecurring = false;
    
    @Embedded
    private RecurrencePattern recurrencePattern;
    
    private String notes;
    
    @Enumerated(EnumType.STRING)
    private BlockStatus status = BlockStatus.ACTIVE;
    
    // Constructors, getters, setters, equals, hashCode
}

public enum BlockReason {
    PAUZA("Pauză"),
    INTALNIRE("Întâlnire"),
    CONCEDIU("Concediu"),
    PERSONAL("Personal"),
    TRAINING("Training"),
    ALTELE("Altele");
    
    private final String displayName;
    
    BlockReason(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
}

public enum BlockStatus {
    ACTIVE, CANCELLED, EXPIRED
}
```

### RecurrencePattern Embeddable
```java
@Embeddable
public class RecurrencePattern {
    
    @Enumerated(EnumType.STRING)
    @Column(name = "recurrence_type")
    private RecurrenceType type;
    
    @Column(name = "recurrence_interval")
    private Integer interval;
    
    @ElementCollection
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "recurrence_days", joinColumns = @JoinColumn(name = "block_time_id"))
    @Column(name = "day_of_week")
    private Set<DayOfWeek> daysOfWeek;
    
    @Column(name = "day_of_month")
    private Integer dayOfMonth;
    
    @Column(name = "end_date")
    private OffsetDateTime endDate;
    
    @Column(name = "occurrences")
    private Integer occurrences;
    
    // Constructors, getters, setters
}

public enum RecurrenceType {
    DAILY, WEEKLY, MONTHLY
}
```

## Repository Implementation

### BlockTimeRepository
```java
@Repository
public interface BlockTimeRepository extends JpaRepository<BlockTime, UUID> {
    
    @Query("""
        SELECT bt FROM BlockTime bt 
        WHERE bt.salonId = :salonId 
        AND bt.status = 'ACTIVE'
        AND bt.startTime <= :endTime 
        AND bt.endTime >= :startTime
        """)
    List<BlockTime> findConflictingBlocks(
        @Param("salonId") UUID salonId,
        @Param("startTime") OffsetDateTime startTime,
        @Param("endTime") OffsetDateTime endTime
    );
    
    @Query("""
        SELECT bt FROM BlockTime bt 
        WHERE bt.salonId = :salonId 
        AND (:staffId IS NULL OR :staffId MEMBER OF bt.staffIds)
        AND (:startDate IS NULL OR bt.startTime >= :startDate)
        AND (:endDate IS NULL OR bt.endTime <= :endDate)
        AND (:reason IS NULL OR bt.reason = :reason)
        AND (:status IS NULL OR bt.status = :status)
        ORDER BY bt.startTime DESC
        """)
    Page<BlockTime> findByCriteria(
        @Param("salonId") UUID salonId,
        @Param("staffId") UUID staffId,
        @Param("startDate") OffsetDateTime startDate,
        @Param("endDate") OffsetDateTime endDate,
        @Param("reason") BlockReason reason,
        @Param("status") BlockStatus status,
        Pageable pageable
    );
    
    @Query("""
        SELECT COUNT(bt), SUM(EXTRACT(EPOCH FROM (bt.endTime - bt.startTime))/3600)
        FROM BlockTime bt 
        WHERE bt.salonId = :salonId 
        AND bt.status = 'ACTIVE'
        AND bt.startTime >= :startDate 
        AND bt.endTime <= :endDate
        """)
    Object[] getStatisticsSummary(
        @Param("salonId") UUID salonId,
        @Param("startDate") OffsetDateTime startDate,
        @Param("endDate") OffsetDateTime endDate
    );
    
    @Modifying
    @Query("UPDATE BlockTime bt SET bt.status = 'EXPIRED' WHERE bt.endTime < :now AND bt.status = 'ACTIVE'")
    int expireOldBlocks(@Param("now") OffsetDateTime now);
}
```

## Service Implementation

### BlockTimeService Interface
```java
public interface BlockTimeService {
    
    BlockTimeResponse createBlockTime(UUID salonId, CreateBlockTimeRequest request, UUID userId);
    
    BlockTimeListResponse getBlockTimes(UUID salonId, BlockTimeSearchCriteria criteria, Pageable pageable);
    
    BlockTimeResponse getBlockTimeById(UUID salonId, UUID blockId);
    
    BlockTimeResponse updateBlockTime(UUID salonId, UUID blockId, UpdateBlockTimeRequest request, UUID userId);
    
    void deleteBlockTime(UUID salonId, UUID blockId, String reason, UUID userId);
    
    BulkOperationResponse bulkOperation(UUID salonId, BulkBlockTimeRequest request, UUID userId);
    
    AvailabilityCheckResponse checkAvailability(UUID salonId, AvailabilityCheckRequest request);
    
    BlockTimeStatisticsResponse getStatistics(UUID salonId, StatisticsCriteria criteria);
    
    void expireOldBlocks();
}
```

### BlockTimeServiceImpl
```java
@Service
@Transactional
@Slf4j
public class BlockTimeServiceImpl implements BlockTimeService {
    
    private final BlockTimeRepository blockTimeRepository;
    private final AppointmentService appointmentService;
    private final StaffService staffService;
    private final NotificationService notificationService;
    private final BlockTimeMapper blockTimeMapper;
    
    public BlockTimeServiceImpl(
            BlockTimeRepository blockTimeRepository,
            AppointmentService appointmentService,
            StaffService staffService,
            NotificationService notificationService,
            BlockTimeMapper blockTimeMapper) {
        this.blockTimeRepository = blockTimeRepository;
        this.appointmentService = appointmentService;
        this.staffService = staffService;
        this.notificationService = notificationService;
        this.blockTimeMapper = blockTimeMapper;
    }
    
    @Override
    public BlockTimeResponse createBlockTime(UUID salonId, CreateBlockTimeRequest request, UUID userId) {
        log.info("Creating block time for salon {} by user {}", salonId, userId);
        
        // Validate request
        validateBlockTimeRequest(request);
        
        // Check permissions
        validateUserPermissions(salonId, userId, request.getStaffIds());
        
        // Check for conflicts
        List<Conflict> conflicts = checkForConflicts(salonId, request);
        if (!conflicts.isEmpty() && !request.isForceCreate()) {
            throw new SchedulingConflictException("Scheduling conflicts detected", conflicts);
        }
        
        // Create block time entity
        BlockTime blockTime = blockTimeMapper.toEntity(request);
        blockTime.setSalonId(salonId);
        blockTime.setCreatedBy(userId);
        blockTime.setStatus(BlockStatus.ACTIVE);
        
        // Save to database
        BlockTime savedBlockTime = blockTimeRepository.save(blockTime);
        
        // Handle recurring blocks
        if (request.isRecurring()) {
            createRecurringBlocks(savedBlockTime);
        }
        
        // Send notifications
        notificationService.notifyStaffBlockTimeCreated(savedBlockTime);
        
        // Handle conflicting appointments
        if (!conflicts.isEmpty()) {
            handleConflictingAppointments(conflicts, savedBlockTime);
        }
        
        log.info("Block time created successfully with ID {}", savedBlockTime.getId());
        return blockTimeMapper.toResponse(savedBlockTime);
    }
    
    private void validateBlockTimeRequest(CreateBlockTimeRequest request) {
        OffsetDateTime now = OffsetDateTime.now();
        
        if (request.getStartTime().isBefore(now.plusMinutes(5))) {
            throw new InvalidTimeRangeException("Block start time must be at least 5 minutes in the future");
        }
        
        if (!request.getEndTime().isAfter(request.getStartTime())) {
            throw new InvalidTimeRangeException("Block end time must be after start time");
        }
        
        Duration duration = Duration.between(request.getStartTime(), request.getEndTime());
        if (duration.toMinutes() < 15) {
            throw new InvalidTimeRangeException("Minimum block duration is 15 minutes");
        }
        
        if (duration.toHours() > 12) {
            throw new InvalidTimeRangeException("Maximum block duration is 12 hours");
        }
        
        if (request.getReason() == BlockReason.ALTELE && 
            (request.getCustomReason() == null || request.getCustomReason().trim().isEmpty())) {
            throw new ValidationException("Custom reason is required when reason is 'Altele'");
        }
    }
    
    private List<Conflict> checkForConflicts(UUID salonId, CreateBlockTimeRequest request) {
        List<Conflict> conflicts = new ArrayList<>();
        
        // Check for appointment conflicts
        List<Appointment> conflictingAppointments = appointmentService.findConflictingAppointments(
            salonId, request.getStartTime(), request.getEndTime(), request.getStaffIds()
        );
        
        conflicts.addAll(conflictingAppointments.stream()
            .map(this::mapAppointmentToConflict)
            .toList());
        
        // Check for existing block conflicts
        List<BlockTime> conflictingBlocks = blockTimeRepository.findConflictingBlocks(
            salonId, request.getStartTime(), request.getEndTime()
        );
        
        conflicts.addAll(conflictingBlocks.stream()
            .filter(block -> hasStaffOverlap(block.getStaffIds(), request.getStaffIds()))
            .map(this::mapBlockToConflict)
            .toList());
        
        return conflicts;
    }
    
    // Additional service methods...
}
```

## Controller Implementation

### BlockTimeController
```java
@RestController
@RequestMapping("/api/v1/salons/{salonId}/block-time")
@Validated
@Slf4j
@Tag(name = "Block Time", description = "Block time management operations")
public class BlockTimeController {

    private final BlockTimeService blockTimeService;

    public BlockTimeController(BlockTimeService blockTimeService) {
        this.blockTimeService = blockTimeService;
    }

    @PostMapping
    @Operation(summary = "Create block time", description = "Creates a new blocked time slot")
    @ApiResponses({
        @ApiResponse(responseCode = "201", description = "Block time created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "409", description = "Scheduling conflict detected")
    })
    public ResponseEntity<ApiResponse<BlockTimeResponse>> createBlockTime(
            @PathVariable @Valid @NotNull UUID salonId,
            @RequestBody @Valid CreateBlockTimeRequest request,
            Authentication authentication) {

        UUID userId = extractUserId(authentication);
        BlockTimeResponse response = blockTimeService.createBlockTime(salonId, request, userId);

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(response, "Timpul a fost blocat cu succes"));
    }

    @GetMapping
    @Operation(summary = "Get block times", description = "Retrieves block times with filtering")
    public ResponseEntity<ApiResponse<BlockTimeListResponse>> getBlockTimes(
            @PathVariable UUID salonId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) UUID staffId,
            @RequestParam(required = false) BlockReason reason,
            @RequestParam(required = false) BlockStatus status,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "50") @Min(1) @Max(100) int limit,
            @RequestParam(defaultValue = "startTime") String sortBy,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection) {

        BlockTimeSearchCriteria criteria = BlockTimeSearchCriteria.builder()
            .startDate(startDate != null ? startDate.atStartOfDay().atOffset(ZoneOffset.UTC) : null)
            .endDate(endDate != null ? endDate.atTime(23, 59, 59).atOffset(ZoneOffset.UTC) : null)
            .staffId(staffId)
            .reason(reason)
            .status(status)
            .build();

        Pageable pageable = PageRequest.of(page, limit, Sort.by(sortDirection, sortBy));
        BlockTimeListResponse response = blockTimeService.getBlockTimes(salonId, criteria, pageable);

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    @GetMapping("/{blockId}")
    @Operation(summary = "Get block time details", description = "Retrieves detailed information about a block time")
    public ResponseEntity<ApiResponse<BlockTimeResponse>> getBlockTime(
            @PathVariable UUID salonId,
            @PathVariable UUID blockId) {

        BlockTimeResponse response = blockTimeService.getBlockTimeById(salonId, blockId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    @PutMapping("/{blockId}")
    @Operation(summary = "Update block time", description = "Updates an existing block time")
    public ResponseEntity<ApiResponse<BlockTimeResponse>> updateBlockTime(
            @PathVariable UUID salonId,
            @PathVariable UUID blockId,
            @RequestBody @Valid UpdateBlockTimeRequest request,
            Authentication authentication) {

        UUID userId = extractUserId(authentication);
        BlockTimeResponse response = blockTimeService.updateBlockTime(salonId, blockId, request, userId);

        return ResponseEntity.ok(ApiResponse.success(response, "Blocul de timp a fost actualizat cu succes"));
    }

    @DeleteMapping("/{blockId}")
    @Operation(summary = "Delete block time", description = "Cancels a block time")
    public ResponseEntity<ApiResponse<Void>> deleteBlockTime(
            @PathVariable UUID salonId,
            @PathVariable UUID blockId,
            @RequestParam(required = false) String reason,
            @RequestParam(defaultValue = "true") boolean notifyStaff,
            Authentication authentication) {

        UUID userId = extractUserId(authentication);
        blockTimeService.deleteBlockTime(salonId, blockId, reason, userId);

        return ResponseEntity.ok(ApiResponse.success(null, "Blocul de timp a fost anulat cu succes"));
    }

    @PostMapping("/bulk")
    @Operation(summary = "Bulk operations", description = "Performs bulk operations on block times")
    public ResponseEntity<ApiResponse<BulkOperationResponse>> bulkOperation(
            @PathVariable UUID salonId,
            @RequestBody @Valid BulkBlockTimeRequest request,
            Authentication authentication) {

        UUID userId = extractUserId(authentication);
        BulkOperationResponse response = blockTimeService.bulkOperation(salonId, request, userId);

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    @PostMapping("/check-availability")
    @Operation(summary = "Check availability", description = "Checks if time slot is available for blocking")
    public ResponseEntity<ApiResponse<AvailabilityCheckResponse>> checkAvailability(
            @PathVariable UUID salonId,
            @RequestBody @Valid AvailabilityCheckRequest request) {

        AvailabilityCheckResponse response = blockTimeService.checkAvailability(salonId, request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    @GetMapping("/statistics")
    @Operation(summary = "Get statistics", description = "Retrieves block time usage statistics")
    public ResponseEntity<ApiResponse<BlockTimeStatisticsResponse>> getStatistics(
            @PathVariable UUID salonId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) UUID staffId) {

        StatisticsCriteria criteria = StatisticsCriteria.builder()
            .startDate(startDate != null ? startDate.atStartOfDay().atOffset(ZoneOffset.UTC) : null)
            .endDate(endDate != null ? endDate.atTime(23, 59, 59).atOffset(ZoneOffset.UTC) : null)
            .staffId(staffId)
            .build();

        BlockTimeStatisticsResponse response = blockTimeService.getStatistics(salonId, criteria);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    private UUID extractUserId(Authentication authentication) {
        // Extract user ID from JWT token or authentication principal
        return UUID.fromString(authentication.getName());
    }
}
```

## Exception Handling

### Global Exception Handler
```java
@RestControllerAdvice
@Slf4j
public class BlockTimeExceptionHandler {

    @ExceptionHandler(SchedulingConflictException.class)
    public ResponseEntity<ApiResponse<ConflictDetails>> handleSchedulingConflict(
            SchedulingConflictException ex) {

        log.warn("Scheduling conflict detected: {}", ex.getMessage());

        ConflictDetails conflictDetails = ConflictDetails.builder()
            .conflictingAppointments(ex.getConflicts())
            .suggestedActions(ex.getSuggestedActions())
            .build();

        ApiError error = ApiError.builder()
            .code("SCHEDULING_CONFLICT")
            .message("Există programări în intervalul selectat")
            .details(conflictDetails)
            .build();

        return ResponseEntity.status(HttpStatus.CONFLICT)
            .body(ApiResponse.error(error));
    }

    @ExceptionHandler(InvalidTimeRangeException.class)
    public ResponseEntity<ApiResponse<Void>> handleInvalidTimeRange(
            InvalidTimeRangeException ex) {

        log.warn("Invalid time range: {}", ex.getMessage());

        ApiError error = ApiError.builder()
            .code("INVALID_TIME_RANGE")
            .message(ex.getMessage())
            .build();

        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY)
            .body(ApiResponse.error(error));
    }

    @ExceptionHandler(BlockTimeNotFoundException.class)
    public ResponseEntity<ApiResponse<Void>> handleBlockTimeNotFound(
            BlockTimeNotFoundException ex) {

        log.warn("Block time not found: {}", ex.getMessage());

        ApiError error = ApiError.builder()
            .code("BLOCK_NOT_FOUND")
            .message("Blocul de timp nu a fost găsit")
            .build();

        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(error));
    }
}
```

## Configuration

### BlockTimeConfig
```java
@Configuration
@EnableConfigurationProperties(BlockTimeProperties.class)
public class BlockTimeConfig {

    @Bean
    public BlockTimeMapper blockTimeMapper() {
        return Mappers.getMapper(BlockTimeMapper.class);
    }

    @Bean
    @ConditionalOnProperty(name = "block-time.auto-expire.enabled", havingValue = "true")
    public TaskScheduler blockTimeTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(2);
        scheduler.setThreadNamePrefix("block-time-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(30);
        return scheduler;
    }

    @Scheduled(fixedRate = 3600000) // Every hour
    @ConditionalOnProperty(name = "block-time.auto-expire.enabled", havingValue = "true")
    public void expireOldBlocks() {
        blockTimeService.expireOldBlocks();
    }
}

@ConfigurationProperties(prefix = "block-time")
@Data
public class BlockTimeProperties {

    private Duration maxDuration = Duration.ofHours(12);
    private Duration minDuration = Duration.ofMinutes(15);
    private Duration futureBuffer = Duration.ofMinutes(5);

    private AutoExpire autoExpire = new AutoExpire();
    private Notifications notifications = new Notifications();

    @Data
    public static class AutoExpire {
        private boolean enabled = true;
        private Duration checkInterval = Duration.ofHours(1);
    }

    @Data
    public static class Notifications {
        private boolean enabled = true;
        private boolean emailEnabled = true;
        private boolean pushEnabled = true;
        private boolean smsEnabled = false;
    }
}
```

## Testing

### Integration Test Example
```java
@SpringBootTest
@AutoConfigureTestDatabase
@Transactional
class BlockTimeControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private BlockTimeRepository blockTimeRepository;

    @Test
    void createBlockTime_Success() {
        // Given
        CreateBlockTimeRequest request = CreateBlockTimeRequest.builder()
            .startTime(OffsetDateTime.now().plusHours(1))
            .endTime(OffsetDateTime.now().plusHours(2))
            .reason(BlockReason.PAUZA)
            .staffIds(Set.of(UUID.randomUUID()))
            .build();

        // When
        ResponseEntity<ApiResponse<BlockTimeResponse>> response = restTemplate.postForEntity(
            "/api/v1/salons/{salonId}/block-time",
            request,
            new ParameterizedTypeReference<ApiResponse<BlockTimeResponse>>() {},
            UUID.randomUUID()
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData().getReason()).isEqualTo(BlockReason.PAUZA);
    }
}
```
