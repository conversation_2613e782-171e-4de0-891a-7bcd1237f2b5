#!/bin/bash

# Script to automatically set environment based on Git branch or manual override
# Usage: ./scripts/set_environment.sh [environment]
# If no environment is provided, it will be auto-detected from Git branch

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to detect environment from Git branch
detect_environment_from_branch() {
    local branch_name

    # Try to get current branch name
    if git rev-parse --git-dir > /dev/null 2>&1; then
        branch_name=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    else
        branch_name="unknown"
    fi

    # Store branch name for later use
    CURRENT_BRANCH="$branch_name"

    case "$branch_name" in
        "main"|"master")
            echo "production"
            ;;
        "staging"|"stage"|"develop")
            echo "staging"
            ;;
        "dev"|"development"|*)
            echo "development"
            ;;
    esac
}

# Function to validate environment
validate_environment() {
    local env=$1
    case "$env" in
        "development"|"dev")
            echo "development"
            ;;
        "staging"|"stage")
            echo "staging"
            ;;
        "production"|"prod")
            echo "production"
            ;;
        *)
            print_error "Invalid environment: $env"
            print_info "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Function to set Flutter environment variable
set_flutter_environment() {
    local env=$1
    
    print_info "Setting Flutter environment to: $env"
    
    # Create or update .env file for local development
    echo "FLUTTER_ENV=$env" > .env
    
    # Export for current session
    export FLUTTER_ENV=$env
    
    print_success "Environment set to: $env"
    print_info "Environment variable FLUTTER_ENV=$env"
    print_info "This will be used by EnvironmentConfig.dart for automatic detection"
}

# Function to update iOS configuration
update_ios_config() {
    local env=$1
    
    print_info "Updating iOS configuration for environment: $env"
    
    # Update bundle identifier in iOS project based on environment
    local bundle_id
    case "$env" in
        "development")
            bundle_id="ro.animalia-programari.animalia.dev"
            ;;
        "staging")
            bundle_id="ro.animalia-programari.animalia.staging"
            ;;
        "production")
            bundle_id="ro.animalia-programari.animalia"
            ;;
    esac
    
    print_info "Bundle ID will be: $bundle_id"
    print_warning "Note: Bundle ID changes require manual Xcode configuration or CI/CD setup"
}

# Function to show current configuration
show_current_config() {
    print_info "Current Configuration:"
    echo "  Environment: ${FLUTTER_ENV:-"Not set (will auto-detect)"}"
    echo "  Git Branch: ${CURRENT_BRANCH:-$(git branch --show-current 2>/dev/null || echo "unknown")}"
    echo "  Auto-detected: $(detect_environment_from_branch)"
}

# Main script logic
main() {
    local target_env
    
    print_info "🚀 Environment Configuration Script"
    echo
    
    # Check if environment is provided as argument
    if [ $# -eq 1 ]; then
        target_env=$(validate_environment "$1")
        print_info "Using provided environment: $target_env"
    else
        target_env=$(detect_environment_from_branch)
        print_info "Current Git branch: $CURRENT_BRANCH"
        print_info "Auto-detected environment: $target_env"
    fi
    
    # Set the environment
    set_flutter_environment "$target_env"
    
    # Update iOS configuration
    update_ios_config "$target_env"
    
    echo
    print_success "Environment configuration completed!"
    echo
    show_current_config
    
    echo
    print_info "Next steps:"
    echo "  1. Run 'flutter clean' to clear build cache"
    echo "  2. Run 'flutter pub get' to refresh dependencies"
    echo "  3. Build your app with the new environment settings"
    
    if [ "$target_env" = "production" ]; then
        echo
        print_warning "PRODUCTION ENVIRONMENT ACTIVE"
        print_warning "Make sure you have:"
        print_warning "  - Production Firebase configuration"
        print_warning "  - Production API endpoints"
        print_warning "  - Proper code signing certificates"
    fi
}

# Run main function with all arguments
main "$@"
